import asyncio
import unittest
from unittest.mock import patch, AsyncMock, MagicMock, call 
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
import httpx 

from app.main import monitoring_loop, process_subscription, fetch_all_active_subscriptions
from app.config import settings 
from libs.shared_models.events import LiveStatusChangedEvent, StreamerInfo
from libs.plugin_interfaces.platform_adapter_v1 import PlatformAdapterV1


# --- Mock Platform Adapter ---
class MockMonitoringPlatformAdapter(PlatformAdapterV1):
    def __init__(self, platform_name: str, config: Optional[Dict[str, Any]] = None):
        super().__init__(platform_name, config)
        self.get_streamer_status_mock = AsyncMock() 
        self.init_called = False
        self.closed_called = False

    async def init(self) -> None: self.init_called = True
    async def get_streamer_status(self, room_id: str) -> Optional[Dict[str, Any]]:
        if self.get_streamer_status_mock.side_effect:
            # Allow raising exceptions for testing
            side_effect = self.get_streamer_status_mock.side_effect
            if isinstance(side_effect, Exception):
                raise side_effect
            else: # If it's a callable, call it
                return await side_effect(room_id) # type: ignore
        return await self.get_streamer_status_mock(room_id) # type: ignore
        
    def get_platform_display_name(self) -> str: return self.platform_name.capitalize()
    async def close(self) -> None: self.closed_called = True


class TestMonitoringFunctionality(unittest.IsolatedAsyncioTestCase):

    @patch('app.main.http_storage_client', new_callable=AsyncMock) 
    async def test_fetch_all_active_subscriptions_success(self, mock_http_client_global):
        expected_subs = {"user1": [{"platform_name": "mock", "room_id": "101"}]}
        mock_response = AsyncMock(spec=httpx.Response)
        mock_response.status_code = 200
        mock_response.json.return_value = expected_subs
        mock_http_client_global.get.return_value = mock_response

        subs = await fetch_all_active_subscriptions()
        self.assertEqual(subs, expected_subs)
        mock_http_client_global.get.assert_called_once_with(
            f"{settings.STORAGE_SERVICE_URL}/internal/subscriptions/all_active", timeout=10.0
        )

    @patch('app.main.http_storage_client', new_callable=AsyncMock)
    async def test_fetch_all_active_subscriptions_http_error(self, mock_http_client_global):
        mock_http_client_global.get.side_effect = httpx.RequestError("Connection failed")
        with self.assertLogs(logger='app.main', level='ERROR') as cm:
            subs = await fetch_all_active_subscriptions()
        self.assertIsNone(subs)
        self.assertTrue(any("Request error fetching subscriptions" in log for log in cm.output))

    # --- process_subscription Tests ---
    @patch('app.main.message_broker_client', new_callable=AsyncMock)
    @patch('app.main.previous_statuses', new_callable=dict) 
    async def test_process_subscription_live_start(self, mock_prev_statuses, mock_broker_client):
        user_id = "user_ls"
        platform = "mock_p"
        room_id = "room_ls"
        sub_details = {"platform_name": platform, "room_id": room_id}
        adapter = MockMonitoringPlatformAdapter(platform)
        status_info = {"platform_name": platform, "room_id": room_id, "anchor_name": "Anchor", "title": "Live!", "live_status": True, "stream_url": "url", "start_time": datetime.now(timezone.utc)}
        adapter.get_streamer_status_mock.return_value = status_info
        mock_prev_statuses[f"{user_id}_{platform}_{room_id}"] = {"live_status": False, "title": "Offline", "anchor_name": "Anchor"}
        mock_broker_client.publish_event.return_value = True

        await process_subscription(user_id, sub_details, adapter)
        
        mock_broker_client.publish_event.assert_called_once()
        event: LiveStatusChangedEvent = mock_broker_client.publish_event.call_args[0][0]
        self.assertEqual(event.event_type, "live_start")
        self.assertEqual(mock_prev_statuses[f"{user_id}_{platform}_{room_id}"]["live_status"], True)

    @patch('app.main.message_broker_client', new_callable=AsyncMock)
    @patch('app.main.previous_statuses', new_callable=dict)
    async def test_process_subscription_live_end(self, mock_prev_statuses, mock_broker_client):
        user_id = "user_le"
        platform = "mock_p"
        room_id = "room_le"
        sub_details = {"platform_name": platform, "room_id": room_id}
        adapter = MockMonitoringPlatformAdapter(platform)
        status_info = {"platform_name": platform, "room_id": room_id, "anchor_name": "Anchor", "title": "Offline now", "live_status": False, "stream_url": "url"}
        adapter.get_streamer_status_mock.return_value = status_info
        mock_prev_statuses[f"{user_id}_{platform}_{room_id}"] = {"live_status": True, "title": "Was Live", "anchor_name": "Anchor"}
        mock_broker_client.publish_event.return_value = True

        await process_subscription(user_id, sub_details, adapter)

        mock_broker_client.publish_event.assert_called_once()
        event: LiveStatusChangedEvent = mock_broker_client.publish_event.call_args[0][0]
        self.assertEqual(event.event_type, "live_end")
        self.assertEqual(mock_prev_statuses[f"{user_id}_{platform}_{room_id}"]["live_status"], False)

    @patch('app.main.message_broker_client', new_callable=AsyncMock)
    @patch('app.main.previous_statuses', new_callable=dict)
    async def test_process_subscription_title_change(self, mock_prev_statuses, mock_broker_client):
        user_id = "user_tc"
        platform = "mock_p"
        room_id = "room_tc"
        sub_details = {"platform_name": platform, "room_id": room_id}
        adapter = MockMonitoringPlatformAdapter(platform)
        status_info = {"platform_name": platform, "room_id": room_id, "anchor_name": "Anchor", "title": "New Title!", "live_status": True, "stream_url": "url", "start_time": datetime.now(timezone.utc)}
        adapter.get_streamer_status_mock.return_value = status_info
        mock_prev_statuses[f"{user_id}_{platform}_{room_id}"] = {"live_status": True, "title": "Old Title", "anchor_name": "Anchor"}
        mock_broker_client.publish_event.return_value = True

        await process_subscription(user_id, sub_details, adapter)

        mock_broker_client.publish_event.assert_called_once()
        event: LiveStatusChangedEvent = mock_broker_client.publish_event.call_args[0][0]
        self.assertEqual(event.event_type, "title_change")
        self.assertEqual(mock_prev_statuses[f"{user_id}_{platform}_{room_id}"]["title"], "New Title!")

    @patch('app.main.message_broker_client', new_callable=AsyncMock)
    @patch('app.main.previous_statuses', new_callable=dict)
    async def test_process_subscription_no_change(self, mock_prev_statuses, mock_broker_client):
        user_id = "user_nc"
        platform = "mock_p"
        room_id = "room_nc"
        sub_details = {"platform_name": platform, "room_id": room_id}
        adapter = MockMonitoringPlatformAdapter(platform)
        status_info = {"platform_name": platform, "room_id": room_id, "anchor_name": "Anchor", "title": "Same Title", "live_status": True, "stream_url": "url", "start_time": datetime.now(timezone.utc)}
        adapter.get_streamer_status_mock.return_value = status_info
        # Cache has same status and title
        mock_prev_statuses[f"{user_id}_{platform}_{room_id}"] = {"live_status": True, "title": "Same Title", "anchor_name": "Anchor"}
        
        await process_subscription(user_id, sub_details, adapter)
        mock_broker_client.publish_event.assert_not_called()
        # Verify cache is not unnecessarily updated if data is identical (optional check, current SUT updates if any diff or event)
        # For now, just ensure no event is published.

    @patch('app.main.message_broker_client', new_callable=AsyncMock)
    @patch('app.main.previous_statuses', new_callable=dict)
    async def test_process_subscription_first_check_no_event(self, mock_prev_statuses, mock_broker_client):
        user_id = "user_fc"
        platform = "mock_p"
        room_id = "room_fc"
        sub_details = {"platform_name": platform, "room_id": room_id}
        adapter = MockMonitoringPlatformAdapter(platform)
        status_info = {"platform_name": platform, "room_id": room_id, "anchor_name": "Anchor", "title": "Title", "live_status": True, "stream_url": "url"}
        adapter.get_streamer_status_mock.return_value = status_info
        
        await process_subscription(user_id, sub_details, adapter)

        mock_broker_client.publish_event.assert_not_called()
        self.assertIn(f"{user_id}_{platform}_{room_id}", mock_prev_statuses)
        self.assertEqual(mock_prev_statuses[f"{user_id}_{platform}_{room_id}"]["title"], "Title")

    @patch('app.main.message_broker_client', new_callable=AsyncMock)
    @patch('app.main.previous_statuses', new_callable=dict)
    async def test_process_subscription_adapter_returns_none(self, mock_prev_statuses, mock_broker_client):
        user_id = "user_an"
        platform = "mock_p"
        room_id = "room_an"
        sub_details = {"platform_name": platform, "room_id": room_id}
        adapter = MockMonitoringPlatformAdapter(platform)
        adapter.get_streamer_status_mock.return_value = None
        
        cache_key = f"{user_id}_{platform}_{room_id}"
        mock_prev_statuses[cache_key] = {"live_status": True, "title": "Old Title", "anchor_name": "Anchor"}

        with self.assertLogs(logger='app.main', level='WARNING') as cm:
            await process_subscription(user_id, sub_details, adapter)
        
        self.assertTrue(any(f"Failed to get status for {platform} room {room_id}" in log for log in cm.output))
        mock_broker_client.publish_event.assert_not_called()
        self.assertEqual(mock_prev_statuses[cache_key], {"live_status": True, "title": "Old Title", "anchor_name": "Anchor"})
        
    @patch('app.main.message_broker_client', new_callable=AsyncMock)
    @patch('app.main.previous_statuses', new_callable=dict)
    async def test_process_subscription_adapter_raises_exception(self, mock_prev_statuses, mock_broker_client):
        user_id = "user_ex"
        platform = "mock_p"
        room_id = "room_ex"
        sub_details = {"platform_name": platform, "room_id": room_id}
        adapter = MockMonitoringPlatformAdapter(platform)
        # Configure the mock to raise an exception when get_streamer_status is called
        adapter.get_streamer_status_mock.side_effect = Exception("Adapter API failed")

        # The process_subscription function itself does not catch exceptions from adapter.get_streamer_status()
        # So, the exception is expected to propagate.
        with self.assertRaisesRegex(Exception, "Adapter API failed"):
            await process_subscription(user_id, sub_details, adapter)
        
        mock_broker_client.publish_event.assert_not_called()


    @patch('app.main.message_broker_client', new_callable=AsyncMock)
    @patch('app.main.previous_statuses', new_callable=dict) 
    async def test_process_subscription_publish_fails(
        self, mock_prev_statuses, mock_broker_client
    ):
        user_id = "user_publish_fail"
        platform = "mock_platform_pf"
        room_id = "room_pf"
        sub_details = {"platform_name": platform, "room_id": room_id}
        
        adapter = MockMonitoringPlatformAdapter(platform_name=platform)
        live_streamer_info = {"platform_name": platform, "room_id": room_id, "anchor_name": "AnchorPF", "title": "Live", "live_status": True, "stream_url": "url_pf", "start_time": datetime.now(timezone.utc)}
        adapter.get_streamer_status_mock.return_value = live_streamer_info

        cache_key = f"{user_id}_{platform}_{room_id}"
        mock_prev_statuses[cache_key] = {"live_status": False, "title": "Offline", "anchor_name": "AnchorPF"}
        mock_broker_client.publish_event.return_value = False

        with self.assertLogs(logger='app.main', level='ERROR') as cm:
            await process_subscription(user_id, sub_details, adapter)
        
        self.assertTrue(any(f"Failed to publish live_start event for {cache_key}" in log for log in cm.output))
        mock_broker_client.publish_event.assert_called_once()
        self.assertTrue(mock_prev_statuses[cache_key]["live_status"])


    # --- monitoring_loop Tests ---
    @patch('app.main.fetch_all_active_subscriptions', new_callable=AsyncMock)
    @patch('app.main.process_subscription', new_callable=AsyncMock) 
    @patch('app.main.platform_adapters', new_callable=dict) 
    @patch('app.main.shutdown_event') 
    @patch('asyncio.wait_for') 
    @patch('app.main.http_storage_client', new_callable=AsyncMock) 
    async def test_monitoring_loop_basic_cycle(
        self, mock_main_loop_http_client, mock_asyncio_wait_for, mock_main_shutdown_event, 
        mock_main_plat_adapters, mock_main_process_sub, mock_main_fetch_subs
    ):
        mock_main_shutdown_event.is_set.side_effect = [False, True] 
        mock_asyncio_wait_for.side_effect = asyncio.TimeoutError 

        mock_adapter_bili = MockMonitoringPlatformAdapter(platform_name="bilibili")
        mock_main_plat_adapters["bilibili"] = mock_adapter_bili

        user_id = "test_user_loop"
        bili_sub = {"platform_name": "bilibili", "room_id": "123"} 
        mock_main_fetch_subs.return_value = {user_id: [bili_sub]}

        await monitoring_loop()

        mock_main_fetch_subs.assert_called_once()
        mock_main_process_sub.assert_called_once_with(user_id, bili_sub, mock_adapter_bili)
        mock_asyncio_wait_for.assert_called_once_with(mock_main_shutdown_event.wait(), timeout=settings.MONITOR_INTERVAL_SECONDS)

    @patch('app.main.fetch_all_active_subscriptions', new_callable=AsyncMock)
    @patch('app.main.process_subscription', new_callable=AsyncMock) 
    @patch('app.main.shutdown_event')
    @patch('asyncio.wait_for')
    @patch('app.main.http_storage_client', new_callable=AsyncMock)
    async def test_monitoring_loop_no_subscriptions_fetches(
        self, mock_main_loop_http_client, mock_asyncio_wait_for, mock_main_shutdown_event, 
        mock_main_process_sub, mock_main_fetch_subs 
    ):
        mock_main_shutdown_event.is_set.side_effect = [False, True]
        mock_asyncio_wait_for.side_effect = asyncio.TimeoutError
        mock_main_fetch_subs.return_value = {} 

        await monitoring_loop()
        mock_main_fetch_subs.assert_called_once()
        mock_main_process_sub.assert_not_called() 

    @patch('app.main.fetch_all_active_subscriptions', new_callable=AsyncMock)
    @patch('app.main.process_subscription', new_callable=AsyncMock) 
    @patch('app.main.shutdown_event')
    @patch('asyncio.wait_for')
    @patch('app.main.http_storage_client', new_callable=AsyncMock)
    async def test_monitoring_loop_fetch_subscriptions_returns_none(
        self, mock_main_loop_http_client, mock_asyncio_wait_for, mock_main_shutdown_event, 
        mock_main_process_sub, mock_main_fetch_subs 
    ):
        mock_main_shutdown_event.is_set.side_effect = [False, True]
        mock_asyncio_wait_for.side_effect = asyncio.TimeoutError
        mock_main_fetch_subs.return_value = None 

        with self.assertLogs(logger='app.main', level='WARNING') as cm:
            await monitoring_loop()
        
        self.assertTrue(any("Failed to fetch subscriptions, or no subscriptions found." in log for log in cm.output))
        mock_main_fetch_subs.assert_called_once()
        mock_main_process_sub.assert_not_called()


if __name__ == "__main__":
    unittest.main()
