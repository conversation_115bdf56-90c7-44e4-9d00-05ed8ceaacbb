from pydantic import BaseModel, ConfigDict, Field
from typing import Dict, Any, Optional, List
from enum import Enum

# --- TemplateType Enum ---
class TemplateTypeEnum(str, Enum):
    live_start = "live_start"
    live_end = "live_end"
    title_change = "title_change"
    # Add other event types as needed

# --- MessageTemplate Schemas ---
# These mirror the schemas in the Storage Service
class MessageTemplateBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, description="Descriptive name for the template")
    template_content: str = Field(..., min_length=1, description="Jinja2 template string")
    template_type: TemplateTypeEnum
    user_id: Optional[str] = Field(None, description="User ID for user-specific templates, None for global/system")

class MessageTemplateCreate(MessageTemplateBase):
    pass

class MessageTemplateUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    template_content: Optional[str] = Field(None, min_length=1)
    template_type: Optional[TemplateTypeEnum] = None

class MessageTemplate(MessageTemplateBase):
    id: int
    
    model_config = ConfigDict(from_attributes=True) # Pydantic V2

# --- Subscription Schemas (existing) ---
class SubscriptionSettings(BaseModel):
    notify_live_start: bool = True
    notify_live_end: bool = False
    notify_title_change: bool = False

class SubscriptionBase(BaseModel):
    platform_name: str
    room_id: str
    settings: SubscriptionSettings = SubscriptionSettings()

class SubscriptionCreate(SubscriptionBase):
    user_id: str 

class Subscription(SubscriptionBase):
    id: int
    user_id: str
    
    model_config = ConfigDict(from_attributes=True) # Pydantic V2

# --- NotificationChannel Schemas (existing) ---
class NotificationChannelBase(BaseModel):
    user_id: str
    channel_type: str 
    channel_uid: str  

class NotificationChannelCreate(NotificationChannelBase):
    pass

class NotificationChannel(NotificationChannelBase):
    id: int

    model_config = ConfigDict(from_attributes=True) # Pydantic V2

# --- General Error Schema for API Gateway responses (existing) ---
class ErrorDetail(BaseModel):
    detail: str
    source_service_status_code: Optional[int] = None
    source_service_response: Optional[str] = None # Or Dict if it's always JSON
