import unittest
from sqlalchemy import create_engine, exc as sqlalchemy_exc # Import sqlalchemy_exc for IntegrityError
from sqlalchemy.orm import sessionmaker, Session
from typing import Optional, List

from app import models, schemas, crud
from app.database import Base 

# --- Test Database Setup (In-memory SQLite) ---
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_crud.db" 
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

class TestCrudOperations(unittest.TestCase):

    db: Session 

    @classmethod
    def setUpClass(cls):
        Base.metadata.create_all(bind=engine)

    @classmethod
    def tearDownClass(cls):
        Base.metadata.drop_all(bind=engine)

    def setUp(self):
        self.connection = engine.connect()
        self.trans = self.connection.begin()
        self.db = TestingSessionLocal(bind=self.connection)
        
        for table in reversed(Base.metadata.sorted_tables):
            self.db.execute(table.delete()) # Use self.db.execute for session-based operations
        self.db.commit() # Commit the cleaning within the session context setup for the test

    def tearDown(self):
        self.db.rollback() # Rollback any uncommitted changes from the test
        self.db.close()
        self.trans.rollback() # Rollback the outer transaction
        self.connection.close()

    # --- Subscription CRUD Tests ---
    def test_create_and_get_subscription(self):
        user_id = "user1"
        sub_in = schemas.SubscriptionCreate(
            user_id=user_id,
            platform_name="bilibili",
            room_id="123",
            settings=schemas.SubscriptionSettings(notify_live_start=True)
        )
        created_sub = crud.create_subscription(self.db, subscription=sub_in)
        self.assertIsNotNone(created_sub.id)
        self.assertEqual(created_sub.user_id, user_id)
        self.assertEqual(created_sub.platform_name, "bilibili")
        self.assertTrue(created_sub.settings["notify_live_start"]) # Access dict directly

        retrieved_sub = crud.get_subscription(self.db, subscription_id=created_sub.id)
        self.assertIsNotNone(retrieved_sub)
        self.assertEqual(retrieved_sub.id, created_sub.id) 

    def test_create_duplicate_subscription_raises_integrity_error(self):
        user_id = "user_dup"
        sub_in = schemas.SubscriptionCreate(user_id=user_id, platform_name="bilibili", room_id="dup123")
        crud.create_subscription(self.db, subscription=sub_in) # Create first one
        
        # Attempt to create the exact same subscription
        with self.assertRaises(sqlalchemy_exc.IntegrityError):
            crud.create_subscription(self.db, subscription=sub_in)
            self.db.flush() # Force the session to interact with DB to hit constraint if not auto-flushing

    def test_get_user_subscriptions(self):
        user_id = "user2"
        sub1_in = schemas.SubscriptionCreate(user_id=user_id, platform_name="bilibili", room_id="101")
        sub2_in = schemas.SubscriptionCreate(user_id=user_id, platform_name="douyu", room_id="202")
        crud.create_subscription(self.db, subscription=sub1_in)
        crud.create_subscription(self.db, subscription=sub2_in)

        user_subs = crud.get_user_subscriptions(self.db, user_id=user_id)
        self.assertEqual(len(user_subs), 2)

        user_subs_none = crud.get_user_subscriptions(self.db, user_id="non_existent_user")
        self.assertEqual(len(user_subs_none), 0)

    def test_delete_subscription(self):
        sub_in = schemas.SubscriptionCreate(user_id="user_del", platform_name="huya", room_id="303")
        created_sub = crud.create_subscription(self.db, subscription=sub_in)
        
        deleted_sub = crud.delete_subscription(self.db, subscription_id=created_sub.id)
        self.assertIsNotNone(deleted_sub)
        self.assertEqual(deleted_sub.id, created_sub.id) 

        self.assertIsNone(crud.get_subscription(self.db, subscription_id=created_sub.id))
        
        non_existent_deleted_sub = crud.delete_subscription(self.db, subscription_id=99999)
        self.assertIsNone(non_existent_deleted_sub)

    def test_update_subscription_settings(self):
        sub_in = schemas.SubscriptionCreate(user_id="user_upd", platform_name="bilibili", room_id="404",
                                          settings=schemas.SubscriptionSettings(notify_live_start=True))
        created_sub = crud.create_subscription(self.db, subscription=sub_in)
        
        new_settings = schemas.SubscriptionSettings(notify_live_start=False, notify_title_change=True)
        updated_sub = crud.update_subscription_settings(self.db, subscription_id=created_sub.id, settings=new_settings)
        
        self.assertIsNotNone(updated_sub)
        self.assertEqual(updated_sub.id, created_sub.id) 
        self.assertFalse(updated_sub.settings["notify_live_start"]) 
        self.assertTrue(updated_sub.settings["notify_title_change"])

    def test_get_all_active_subscriptions_grouped_by_user(self):
        crud.create_subscription(self.db, schemas.SubscriptionCreate(user_id="u1", platform_name="p1", room_id="r1"))
        crud.create_subscription(self.db, schemas.SubscriptionCreate(user_id="u1", platform_name="p1", room_id="r2"))
        crud.create_subscription(self.db, schemas.SubscriptionCreate(user_id="u2", platform_name="p2", room_id="r3"))
        
        grouped = crud.get_all_active_subscriptions_grouped_by_user(self.db)
        self.assertIn("u1", grouped)
        self.assertIn("u2", grouped)
        self.assertEqual(len(grouped["u1"]), 2)
        self.assertEqual(len(grouped["u2"]), 1)

    # --- NotificationChannel CRUD Tests ---
    def test_upsert_notification_channel_create_and_update(self):
        user_id = "user_nc"
        channel_type = "wxpusher"
        
        nc_create_in = schemas.NotificationChannelCreate(user_id=user_id, channel_type=channel_type, channel_uid="UID_initial")
        created_nc = crud.upsert_notification_channel(self.db, channel_data=nc_create_in)
        self.assertIsNotNone(created_nc.id)
        self.assertEqual(created_nc.channel_uid, "UID_initial")

        nc_update_in = schemas.NotificationChannelCreate(user_id=user_id, channel_type=channel_type, channel_uid="UID_updated")
        updated_nc = crud.upsert_notification_channel(self.db, channel_data=nc_update_in)
        self.assertEqual(updated_nc.id, created_nc.id) 
        self.assertEqual(updated_nc.channel_uid, "UID_updated")

    def test_get_user_notification_channels(self):
        user_id = "user_nc_list"
        crud.upsert_notification_channel(self.db, schemas.NotificationChannelCreate(user_id=user_id, channel_type="wxpusher", channel_uid="uid1"))
        crud.upsert_notification_channel(self.db, schemas.NotificationChannelCreate(user_id=user_id, channel_type="email", channel_uid="<EMAIL>"))
        
        channels = crud.get_user_notification_channels(self.db, user_id=user_id)
        self.assertEqual(len(channels), 2)

        channels_none = crud.get_user_notification_channels(self.db, user_id="non_existent_user")
        self.assertEqual(len(channels_none), 0)

    # --- MessageTemplate CRUD Tests ---
    def test_create_and_get_message_template(self):
        template_in = schemas.MessageTemplateCreate(
            name="Test Template",
            template_content="Hello {{ streamer_info.anchor_name }}!",
            template_type=schemas.TemplateTypeEnum.live_start,
            user_id="user_mt"
        )
        created_template = crud.create_message_template(self.db, template=template_in)
        self.assertIsNotNone(created_template.id)
        self.assertEqual(created_template.name, "Test Template")
        self.assertEqual(created_template.template_type, "live_start")
        self.assertEqual(created_template.user_id, "user_mt")

        retrieved_template = crud.get_message_template(self.db, template_id=created_template.id)
        self.assertIsNotNone(retrieved_template)
        self.assertEqual(retrieved_template.id, created_template.id) 

    def test_create_duplicate_message_template_raises_integrity_error(self):
        user_id = "user_mt_dup"
        template_name = "Dupe Template"
        template_type = schemas.TemplateTypeEnum.live_start
        
        template1_in = schemas.MessageTemplateCreate(
            name=template_name, template_content="Content1", 
            template_type=template_type, user_id=user_id
        )
        crud.create_message_template(self.db, template=template1_in)

        template2_in = schemas.MessageTemplateCreate(
            name=template_name, template_content="Content2", 
            template_type=template_type, user_id=user_id # Same name, type, user
        )
        with self.assertRaises(sqlalchemy_exc.IntegrityError):
            crud.create_message_template(self.db, template=template2_in)
            self.db.flush() 

    def test_get_message_templates_by_user_and_type(self):
        crud.create_message_template(self.db, schemas.MessageTemplateCreate(name="Global LS", template_content="G", template_type=schemas.TemplateTypeEnum.live_start, user_id=None))
        crud.create_message_template(self.db, schemas.MessageTemplateCreate(name="User1 LS", template_content="U1LS", template_type=schemas.TemplateTypeEnum.live_start, user_id="user1"))
        crud.create_message_template(self.db, schemas.MessageTemplateCreate(name="User1 LE", template_content="U1LE", template_type=schemas.TemplateTypeEnum.live_end, user_id="user1"))

        global_ls = crud.get_message_templates_by_user_and_type(self.db, user_id=None, template_type=schemas.TemplateTypeEnum.live_start)
        self.assertEqual(len(global_ls), 1)
        self.assertEqual(global_ls[0].name, "Global LS")

        user1_ls = crud.get_message_templates_by_user_and_type(self.db, user_id="user1", template_type=schemas.TemplateTypeEnum.live_start)
        self.assertEqual(len(user1_ls), 1)
        self.assertEqual(user1_ls[0].name, "User1 LS")
        
        user1_all = crud.get_message_templates_by_user_and_type(self.db, user_id="user1")
        self.assertEqual(len(user1_all), 2)

        global_all = crud.get_message_templates_by_user_and_type(self.db, user_id=None)
        self.assertEqual(len(global_all), 1) 

    def test_update_message_template(self):
        template_in = schemas.MessageTemplateCreate(name="Initial", template_content="Init", template_type=schemas.TemplateTypeEnum.title_change)
        created_template = crud.create_message_template(self.db, template=template_in)
        
        update_data = schemas.MessageTemplateUpdate(name="Updated Name", template_content="New Content")
        updated_template = crud.update_message_template(self.db, template_id=created_template.id, template_update=update_data)
        
        self.assertIsNotNone(updated_template)
        self.assertEqual(updated_template.name, "Updated Name") 
        self.assertEqual(updated_template.template_content, "New Content") 
        self.assertEqual(updated_template.template_type, schemas.TemplateTypeEnum.title_change.value)

    def test_delete_message_template(self):
        template_in = schemas.MessageTemplateCreate(name="To Delete", template_content="Del", template_type=schemas.TemplateTypeEnum.live_start)
        created_template = crud.create_message_template(self.db, template=template_in)

        deleted_success = crud.delete_message_template(self.db, template_id=created_template.id)
        self.assertTrue(deleted_success)
        self.assertIsNone(crud.get_message_template(self.db, template_id=created_template.id))

        deleted_fail = crud.delete_message_template(self.db, template_id=99999)
        self.assertFalse(deleted_fail)

if __name__ == "__main__":
    unittest.main()
