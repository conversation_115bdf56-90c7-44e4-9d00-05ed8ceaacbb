FROM python:3.10-slim

WORKDIR /app

# Copy requirements first for layer caching
COPY ./requirements.txt /app/requirements.txt
RUN pip install --no-cache-dir --upgrade pip
RUN pip install --no-cache-dir -r /app/requirements.txt

# Copy the application code for the monitoring service
COPY ./app /app/app

# Copy the plugins directory for this service
COPY ./plugins /app/plugins

# Copy the shared libraries (plugin_interfaces, shared_models)
# This assumes the Docker build context is 'live_monitor_v2/services/monitoring_service/'
# and 'libs' is at '../../libs' relative to this Dockerfile.
COPY ../../libs /app/libs

# Set PYTHONPATH to include the /app directory, so 'app.config', 'plugins.module', 
# and 'libs.shared_models' can be imported.
ENV PYTHONPATH=/app

# Command to run the monitoring service
CMD ["python", "app/main.py"]
