// Shared frontend type definitions

export interface SubscriptionSettings {
  notify_live_start: boolean;
  notify_live_end: boolean;
  notify_title_change: boolean;
  // quiet_hours?: { start: string; end: string; enabled: boolean }; // Example
}

export interface Subscription {
  id: number; // Changed from string to number as per typical DB IDs
  user_id: string;
  platform_name: string;
  room_id: string;
  settings: SubscriptionSettings;
}

// For creating a subscription, ID is not needed
export interface SubscriptionCreatePayload {
  user_id: string;
  platform_name: string;
  room_id: string;
  settings?: SubscriptionSettings; // Settings are optional on creation, defaults used by backend
}

export interface NotificationChannel {
  user_id: string;
  channel_type: string; // e.g., 'email', 'webhook', 'discord'
  channel_uid: string; // e.g., email address, webhook URL, discord_user_id/channel_id
  // last_notified_at?: string; // Optional: timestamp of last notification
  // created_at: string; // Optional: timestamp of creation
  // updated_at: string; // Optional: timestamp of last update
}

export interface NotificationChannelCreate {
  // user_id and channel_type are typically part of the URL path or determined by the server
  channel_uid: string; // The unique identifier for the channel, e.g., email or webhook URL
}

// --- Message Template Types ---

export enum TemplateTypeEnum {
  LIVE_START = 'live_start',
  LIVE_END = 'live_end',
  TITLE_CHANGE = 'title_change',
  // Add other template types as needed
}

export interface MessageTemplate {
  id: number;
  user_id: string | null; // Nullable for global default templates
  template_type: TemplateTypeEnum;
  template_name: string; // User-defined name for the template
  subject_template: string; // Template for the notification subject/title
  body_template: string;    // Template for the notification body
  is_default: boolean;      // True if this is a system default template
  // created_at?: string;    // Optional: timestamp of creation
  // updated_at?: string;    // Optional: timestamp of last update
}

export interface MessageTemplateCreatePayload {
  template_type: TemplateTypeEnum;
  template_name: string;
  subject_template: string;
  body_template: string;
  user_id?: string | null; // Optional: associates template with a user, null for admin creating global
  // is_default will likely be managed by the backend or specific admin actions
}

export interface MessageTemplateUpdatePayload {
  template_name?: string;
  subject_template?: string;
  body_template?: string;
  // template_type and user_id are typically not updatable or handled differently
  // is_default status change would likely be a separate, privileged action
}
