# Example settings for Notification Service (notification_service/config/settings.yaml.example)
# These would be loaded by the service's pydantic-settings model.

# Storage Service URL (for fetching user notification preferences/details like WxPusher UIDs or email addresses)
# Typically set via environment variable LIVE_MONITOR_STORAGE_SERVICE_URL in docker-compose
# storage_service_url: "http://storage_service:8000" 

# Message Broker URL (for consuming events)
# Typically set via environment variable LIVE_MONITOR_MESSAGE_BROKER_URL in docker-compose
# message_broker_url: "amqp://lmv2_user:lmv2_password@message_broker/"

# Directory where notifier plugins are located
# This path is relative to the notification_service/app directory (where main.py and plugin_loader.py are)
# Example: "notifiers" means plugins are in notification_service/app/notifiers/
# The Dockerfile copies the 'notifiers' dir to '/app/notifiers', and PYTHONPATH includes '/app'.
# So, the import base for plugin_loader becomes 'notifiers.module_name'.
notifier_plugin_dir: "notifiers"

# Configuration for individual notifier plugins.
# The structure here should match what app/config.py's NotifierPluginConfig and Settings expect.
# For MVP, these specific notifier configs (like app_token for wxpusher, smtp details for email)
# are loaded via environment variables prefixed with LIVE_MONITOR_ and then processed
# in app/config.py to build the `MVP_NOTIFIERS_CONFIG` list.
# This example file primarily serves to document what *could* be set if loading from YAML directly
# or to remind which environment variables are primary.

# WxPusher Configuration (loaded via environment variables into settings model)
# LIVE_MONITOR_WXPUSHER_APP_TOKEN: "AT_your_wxpusher_token"
# LIVE_MONITOR_WXPUSHER_BASE_URL: "https://wxpusher.zjiecode.com/api" # Optional
# LIVE_MONITOR_WXPUSHER_CLOUDFLARE_WORKER_URL: "" # Optional
# LIVE_MONITOR_WXPUSHER_CLOUDFLARE_API_KEY: "" # Optional

# Email Notifier Configuration (loaded via environment variables into settings model)
# LIVE_MONITOR_EMAIL_SMTP_HOST: "smtp.example.com"
# LIVE_MONITOR_EMAIL_SMTP_PORT: 587 
# LIVE_MONITOR_EMAIL_SMTP_USER: "<EMAIL>"
# LIVE_MONITOR_EMAIL_SMTP_PASSWORD: "your_smtp_password"
# LIVE_MONITOR_EMAIL_SENDER_EMAIL: "<EMAIL>"
# LIVE_MONITOR_EMAIL_USE_TLS: true # Default based on port 587
# LIVE_MONITOR_EMAIL_USE_SSL: false # Default

# The app/config.py's `MVP_NOTIFIERS_CONFIG` logic will construct the list of NotifierPluginConfig
# based on whether these environment variables are set.
# For example, if LIVE_MONITOR_WXPUSHER_APP_TOKEN is set, a wxpusher config entry is added.
# If LIVE_MONITOR_EMAIL_SMTP_HOST and LIVE_MONITOR_EMAIL_SENDER_EMAIL are set, an email config entry is added.

# Example of how the structured list might look if directly defined (for documentation):
# notifiers_config_list_example:
#   - type: "wxpusher"
#     enabled: true
#     # module: "wxpusher_notifier" # Can be inferred
#     # class_name: "WxPusherNotifier" # Can be inferred
#     config:
#       app_token: "AT_your_wxpusher_token" # Value from LIVE_MONITOR_WXPUSHER_APP_TOKEN
#       base_url: "https://wxpusher.zjiecode.com/api"
#       # cloudflare_worker_url: ...
#       # cloudflare_api_key: ...
#   - type: "email"
#     enabled: true
#     config:
#       smtp_host: "smtp.example.com"
#       smtp_port: 587
#       smtp_user: "<EMAIL>"
#       smtp_password: "your_smtp_password"
#       sender_email: "<EMAIL>"
#       use_tls: true
#       use_ssl: false
# This list (`MVP_NOTIFIERS_CONFIG`) is dynamically built in app/config.py based on environment variables.
