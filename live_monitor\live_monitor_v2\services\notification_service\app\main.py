import asyncio
import logging
import signal
import json 
from typing import Dict, Any, Optional, List

import httpx
import aio_pika 
from jinja2 import Environment, select_autoescape, TemplateNotFound # Added Jinja2

from app.config import settings, MVP_NOTIFIERS_CONFIG 
from app.plugin_loader import load_notifiers
from app.message_broker import message_consumer_client 
from libs.plugin_interfaces.base_notifier_v1 import BaseNotifierV1
from libs.shared_models.events import LiveStatusChangedEvent, StreamerInfo 

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

active_notifiers: Dict[str, BaseNotifierV1] = {}
http_storage_client: Optional[httpx.AsyncClient] = None
shutdown_event = asyncio.Event()

# Initialize Jinja2 Environment
jinja_env = Environment(
    autoescape=select_autoescape(['html', 'xml'], default_for_string=True, default=True), # Enable autoescape for strings
    extensions=['jinja2.ext.do'] # Example extension
)

async def fetch_template_from_storage(
    user_id: Optional[str], 
    template_type: str
) -> Optional[str]:
    """
    Fetches a template string from the Storage Service.
    Tries user-specific first, then global for the given type.
    """
    global http_storage_client
    if not http_storage_client:
        logger.error("HTTP client for Storage Service not initialized for fetch_template.")
        return None

    async def _get_template(params: Dict[str, Any]) -> Optional[str]:
        try:
            url = f"{settings.STORAGE_SERVICE_URL}/templates"
            response = await http_storage_client.get(url, params=params, timeout=5.0) # type: ignore
            if response.status_code == 200:
                templates = response.json()
                if templates and isinstance(templates, list) and len(templates) > 0:
                    # Assuming the first one found is the most specific/correct one
                    return templates[0].get("template_content")
            elif response.status_code == 404: # No template found by this query
                return None
            else:
                logger.error(f"Error fetching template with params {params}: {response.status_code} - {response.text}")
            return None
        except httpx.RequestError as e:
            logger.error(f"Request error fetching template with params {params}: {e}")
        except Exception as e:
            logger.error(f"Generic error fetching template with params {params}: {e}")
        return None

    # 1. Try user-specific template
    if user_id:
        logger.debug(f"Fetching user-specific template for user_id: {user_id}, type: {template_type}")
        user_template_content = await _get_template({"user_id": user_id, "template_type": template_type})
        if user_template_content:
            logger.info(f"Found user-specific template for user {user_id}, type {template_type}")
            return user_template_content

    # 2. Try global template (user_id=None or not provided)
    logger.debug(f"Fetching global template for type: {template_type}")
    global_template_content = await _get_template({"template_type": template_type}) # Assuming API handles user_id=None for global
    if global_template_content:
        logger.info(f"Found global template for type {template_type}")
        return global_template_content
    
    logger.warning(f"No template found for user {user_id} or global for type {template_type}")
    return None


async def get_user_wxpusher_uid(user_id: str) -> Optional[str]:
    global http_storage_client
    if not http_storage_client:
        logger.error("HTTP client for Storage Service not initialized for get_user_wxpusher_uid.")
        return None
    
    channel_type = "wxpusher"
    url = f"{settings.STORAGE_SERVICE_URL}/users/{user_id}/notification_channels/{channel_type}"
    
    try:
        response = await http_storage_client.get(url, timeout=5.0) # type: ignore
        if response.status_code == 200:
            channel_data = response.json()
            return channel_data.get("channel_uid")
        elif response.status_code == 404:
            logger.info(f"No WxPusher channel found for user_id: {user_id}")
            return None
        else:
            logger.error(f"Error fetching WxPusher UID for user {user_id}: {response.status_code} - {response.text}")
            return None
    except httpx.RequestError as e:
        logger.error(f"Request error fetching WxPusher UID for user {user_id}: {e}")
    except Exception as e: 
        logger.error(f"Generic error fetching WxPusher UID for user {user_id}: {e}")
    return None

async def process_event_message(message: aio_pika.IncomingMessage) -> None:
    async with message.process(ignore_processed=True): 
        try:
            event_data = json.loads(message.body.decode())
            event = LiveStatusChangedEvent(**event_data)
            logger.info(f"Received event: {event.event_type} for user {event.user_id}, streamer {event.streamer_info.platform_name}/{event.streamer_info.room_id}")

            wxpusher_notifier = active_notifiers.get("wxpusher")
            if not wxpusher_notifier:
                logger.warning("WxPusher notifier not loaded/available. Cannot send notification.")
                await message.reject(requeue=False) 
                return

            wxpusher_uid = await get_user_wxpusher_uid(event.user_id)
            if not wxpusher_uid:
                logger.info(f"No WxPusher UID for user {event.user_id}. Notification for {event.event_type} not sent.")
                await message.ack() 
                return

            s_info = event.streamer_info
            streamer_info_dict = s_info.model_dump() # For Jinja2 context

            # Fetch template
            template_content_str = await fetch_template_from_storage(
                user_id=event.user_id, 
                template_type=event.event_type # e.g., "live_start"
            )

            # Programmatic title (MVP)
            event_type_display = event.event_type.replace('_', ' ').title()
            title = f"{s_info.platform_name.capitalize()} - {s_info.anchor_name} - {event_type_display}"
            body: str

            if template_content_str:
                try:
                    template = jinja_env.from_string(template_content_str)
                    body = template.render(streamer_info=streamer_info_dict)
                    logger.info(f"Rendered body from custom template for event {event.event_type}, user {event.user_id}")
                except Exception as e:
                    logger.error(f"Error rendering Jinja2 template for event {event.event_type}, user {event.user_id}: {e}. Falling back to default.", exc_info=True)
                    # Fallback to hardcoded if template rendering fails
                    template_content_str = None # Ensure fallback is used
            
            if not template_content_str: # Fallback or if no template was found initially
                logger.info(f"Using fallback formatting for event {event.event_type}, user {event.user_id}")
                if event.event_type == "live_start":
                    body = f"🎉 主播 {s_info.anchor_name} 在 {s_info.platform_name.capitalize()} 开播啦!\n标题: {s_info.title}\n🔗 {s_info.stream_url}"
                elif event.event_type == "live_end":
                    body = f"😴 主播 {s_info.anchor_name} 在 {s_info.platform_name.capitalize()} 下播了。"
                elif event.event_type == "title_change":
                    body = f"📢 主播 {s_info.anchor_name} 在 {s_info.platform_name.capitalize()} 更新了标题:\n新标题: {s_info.title}\n🔗 {s_info.stream_url}"
                else:
                    logger.warning(f"Unknown event type: {event.event_type}. Cannot format fallback message.")
                    await message.ack() 
                    return
            
            logger.info(f"Sending notification via WxPusher to UID {wxpusher_uid} for user {event.user_id}")
            await wxpusher_notifier.send(
                title=title,
                body=body,
                target_users=[wxpusher_uid], 
                details={"event_timestamp": event.timestamp.isoformat()} 
            )
            await message.ack() 
            logger.debug(f"Message for event {event.event_type} user {event.user_id} acknowledged.")

        except json.JSONDecodeError:
            logger.error(f"Failed to decode message body: {message.body.decode()}")
            await message.reject(requeue=False) 
        except Exception as e:
            logger.error(f"Error processing event message: {e}", exc_info=True)
            await message.reject(requeue=False) 


async def main_service_loop():
    global active_notifiers, http_storage_client

    logger.info("Initializing Notification Service...")
    http_storage_client = httpx.AsyncClient()
    
    active_notifiers = load_notifiers(
        plugin_dir_setting=settings.NOTIFIER_PLUGIN_DIR,
        configured_notifiers=MVP_NOTIFIERS_CONFIG 
    )

    if not active_notifiers:
        logger.error("No notifier plugins loaded. Notification service cannot function.")
        if http_storage_client: await http_storage_client.aclose()
        return
    else:
        logger.info(f"Loaded notifier plugins: {list(active_notifiers.keys())}")
        for notifier_type, notifier_instance in active_notifiers.items():
            try:
                await notifier_instance.init()
                logger.info(f"Initialized notifier: {notifier_type}")
            except Exception as e:
                logger.error(f"Failed to initialize notifier {notifier_type}: {e}")

    try:
        await message_consumer_client.connect()
        consume_task = asyncio.create_task(
            message_consumer_client.consume_events(on_message_callback=process_event_message)
        )
        logger.info("Message consumer started.")
        await shutdown_event.wait() 

    except Exception as e:
        logger.error(f"Could not connect to message broker or start consumer: {e}. Service will exit.")
    finally:
        logger.info("Shutting down Notification Service...")
        if 'consume_task' in locals() and not consume_task.done(): 
            consume_task.cancel() 
            try:
                await consume_task 
            except asyncio.CancelledError:
                logger.info("Message consumption task cancelled.")
        
        if http_storage_client:
            await http_storage_client.aclose()
            logger.info("HTTP storage client closed.")

        for notifier_type, notifier_instance in active_notifiers.items():
            if hasattr(notifier_instance, 'close') and callable(notifier_instance.close):
                try:
                    await notifier_instance.close() 
                    logger.info(f"Closed notifier: {notifier_type}")
                except Exception as e:
                    logger.error(f"Error closing notifier {notifier_type}: {e}")
                    
        await message_consumer_client.close()
        logger.info("Notification Service shut down gracefully.")

def signal_handler(sig, frame):
    logger.info(f"Received signal {sig}, initiating shutdown...")
    shutdown_event.set()

if __name__ == "__main__":
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        asyncio.run(main_service_loop())
    except KeyboardInterrupt:
        logger.info("KeyboardInterrupt received, shutting down...")
        if not shutdown_event.is_set():
             shutdown_event.set()
    except Exception as e:
        logger.error(f"Unhandled exception in main execution: {e}", exc_info=True)
    finally:
        logger.info("Application exiting.")
