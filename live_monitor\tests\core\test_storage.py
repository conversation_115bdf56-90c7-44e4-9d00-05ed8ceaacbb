import asyncio
import json
import os
import shutil
import unittest
from app.core.storage import AsyncStorage
from app.constants import DEFAULT_SUBSCRIPTION_SETTINGS

class TestAsyncStorageAddSubscription(unittest.IsolatedAsyncioTestCase):

    async def asyncSetUp(self):
        self.test_data_dir = os.path.join("tests", "temp_data")
        os.makedirs(self.test_data_dir, exist_ok=True)
        self.storage = AsyncStorage(data_dir=self.test_data_dir)
        # No need to call self.storage.init() as it's called by other methods if needed,
        # or its relevant parts (like loading cache) are not essential for add_subscription alone.
        # We want to test add_subscription in a somewhat isolated manner for subscriptions.json handling.

        # Ensure a clean state for subscriptions file before each test
        if os.path.exists(self.storage.subscriptions_file):
            os.remove(self.storage.subscriptions_file)
        # Create an empty subscriptions file to simulate initial state
        with open(self.storage.subscriptions_file, 'w', encoding='utf-8') as f:
            json.dump({}, f)

    async def asyncTearDown(self):
        # await self.storage.cleanup() # Cleanup might try to save cache, which is not the focus here.
        if os.path.exists(self.test_data_dir):
            shutil.rmtree(self.test_data_dir)

    async def test_add_new_subscription(self):
        uid = "user1"
        platform = "platformA"
        room_id = "room123"
        
        success = await self.storage.add_subscription(uid, platform, room_id)
        self.assertTrue(success, "add_subscription should return True for a new subscription.")
        
        subscriptions = await self.storage.load_subscriptions()
        self.assertIn(uid, subscriptions)
        self.assertEqual(len(subscriptions[uid]), 1)
        self.assertEqual(subscriptions[uid][0]['platform'], platform)
        self.assertEqual(subscriptions[uid][0]['room_id'], room_id)

    async def test_add_new_subscription_uses_default_settings(self):
        uid = "user2"
        platform = "platformB"
        room_id = "room456"

        success = await self.storage.add_subscription(uid, platform, room_id)
        self.assertTrue(success)

        subscriptions = await self.storage.load_subscriptions()
        self.assertIn(uid, subscriptions)
        self.assertEqual(subscriptions[uid][0]['settings'], DEFAULT_SUBSCRIPTION_SETTINGS)

    async def test_add_existing_subscription_no_new_settings(self):
        uid = "user3"
        platform = "platformC"
        room_id = "room789"

        # First addition
        await self.storage.add_subscription(uid, platform, room_id)
        
        # Attempt to add again without new settings
        success_again = await self.storage.add_subscription(uid, platform, room_id)
        self.assertTrue(success_again, "add_subscription should return True for an existing subscription even with no new settings.")

        subscriptions = await self.storage.load_subscriptions()
        self.assertEqual(len(subscriptions[uid]), 1, "Subscription should not be duplicated.")

    async def test_add_existing_subscription_with_new_settings(self):
        uid = "user4"
        platform = "platformD"
        room_id = "room012"
        initial_settings = {"notify": False} # Different from default to ensure update is testable
        new_settings = {"notify": True, "quality": "high"}

        # First addition with initial (or default) settings
        await self.storage.add_subscription(uid, platform, room_id, settings=initial_settings.copy())
        
        subscriptions = await self.storage.load_subscriptions()
        self.assertEqual(subscriptions[uid][0]['settings'], initial_settings)

        # Attempt to add again with new settings
        success_update = await self.storage.add_subscription(uid, platform, room_id, settings=new_settings.copy())
        self.assertTrue(success_update, "add_subscription should return True when updating settings for an existing subscription.")

        subscriptions_updated = await self.storage.load_subscriptions()
        self.assertEqual(len(subscriptions_updated[uid]), 1, "Subscription should not be duplicated.")
        self.assertEqual(subscriptions_updated[uid][0]['settings'], new_settings, "Subscription settings should be updated.")

if __name__ == '__main__':
    unittest.main()
