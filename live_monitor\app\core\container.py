"""
依赖注入容器模块
"""
from typing import Dict, Any, Type, TypeVar, Set

from app.utils.logger import setup_logger

logger = setup_logger('core.container')

T = TypeVar('T')

class Container:
    """依赖注入容器，管理应用中的所有依赖"""

    def __init__(self):
        """初始化容器"""
        self._instances: Dict[str, Any] = {}
        self._factories: Dict[str, callable] = {}
        self._initialized_components: Set[str] = set()

        # 注册默认工厂
        # 注意: 默认工厂在app.py中注册

    def register(self, name: str, instance: Any) -> None:
        """
        注册一个实例

        Args:
            name: 实例名称
            instance: 实例对象
        """
        self._instances[name] = instance
        logger.debug(f"已注册实例: {name}")

    def register_factory(self, name: str, factory: callable) -> None:
        """
        注册一个工厂函数

        Args:
            name: 实例名称
            factory: 工厂函数，用于创建实例
        """
        self._factories[name] = factory
        logger.debug(f"已注册工厂: {name}")

    def get(self, name: str) -> Any:
        """
        获取一个实例

        Args:
            name: 实例名称

        Returns:
            Any: 实例对象

        Raises:
            KeyError: 如果实例不存在且没有对应的工厂
        """
        # 如果实例已存在，直接返回
        if name in self._instances:
            return self._instances[name]

        # 如果有对应的工厂，创建实例并缓存
        if name in self._factories:
            instance = self._factories[name]()
            self._instances[name] = instance
            logger.debug(f"已创建实例: {name}")
            return instance

        # 如果既没有实例也没有工厂，抛出异常
        raise KeyError(f"未找到依赖: {name}")

    def get_or_default(self, name: str, default: Any = None) -> Any:
        """
        获取一个实例，如果不存在则返回默认值

        Args:
            name: 实例名称
            default: 默认值

        Returns:
            Any: 实例对象或默认值
        """
        try:
            return self.get(name)
        except KeyError:
            return default

    def get_typed(self, name: str, cls: Type[T]) -> T:
        """
        获取一个指定类型的实例

        Args:
            name: 实例名称
            cls: 实例类型

        Returns:
            T: 指定类型的实例

        Raises:
            KeyError: 如果实例不存在且没有对应的工厂
            TypeError: 如果实例类型不匹配
        """
        instance = self.get(name)
        if not isinstance(instance, cls):
            raise TypeError(f"依赖 {name} 的类型不是 {cls.__name__}")
        return instance

    async def init_component(self, name: str) -> None:
        """
        初始化组件

        Args:
            name: 组件名称

        Raises:
            KeyError: 如果组件不存在
        """
        if name in self._initialized_components:
            logger.debug(f"组件 {name} 已经初始化")
            return

        instance = self.get(name)

        # 检查组件是否有init方法
        if hasattr(instance, 'init') and callable(getattr(instance, 'init')):
            logger.info(f"初始化组件: {name}")
            await instance.init()
            self._initialized_components.add(name)
        else:
            logger.debug(f"组件 {name} 没有init方法")

    async def cleanup_component(self, name: str) -> None:
        """
        清理组件

        Args:
            name: 组件名称

        Raises:
            KeyError: 如果组件不存在
        """
        if name not in self._initialized_components:
            logger.debug(f"组件 {name} 未初始化")
            return

        instance = self.get(name)

        # 检查组件是否有cleanup方法
        if hasattr(instance, 'cleanup') and callable(getattr(instance, 'cleanup')):
            logger.info(f"清理组件: {name}")
            await instance.cleanup()
            self._initialized_components.remove(name)
        else:
            logger.debug(f"组件 {name} 没有cleanup方法")

    async def init_all(self) -> None:
        """初始化所有组件"""
        for name in list(self._instances.keys()):
            await self.init_component(name)

    async def cleanup_all(self) -> None:
        """清理所有组件"""
        # 复制一份列表，因为在循环中会修改self._initialized_components
        for name in list(self._initialized_components):
            await self.cleanup_component(name)

# 创建全局容器实例
container = Container()
