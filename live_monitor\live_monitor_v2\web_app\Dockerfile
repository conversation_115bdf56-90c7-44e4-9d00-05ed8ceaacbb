# Stage 1: Build the Vue.js application
FROM node:18-alpine AS builder
WORKDIR /app

# Copy package.json and package-lock.json (if available)
# For a fresh scaffold, package-lock.json might not exist yet.
# npm install will create it.
COPY package.json ./
# If package-lock.json is available, copy it for reproducible builds
# COPY package-lock.json ./

RUN npm install 

# Copy the rest of the application code
COPY . .

# Build the application
RUN npm run build

# Stage 2: Serve the static files with Nginx
FROM nginx:1.25-alpine

# Copy the built application from the builder stage to Nginx's web root
COPY --from=builder /app/dist /usr/share/nginx/html

# Optional: Add a custom nginx.conf for Single Page Application (SPA) routing
# This ensures that direct navigation to routes like /about are handled by index.html
# Create a file named 'nginx.default.conf' in the web_app directory with content like:
# server {
#    listen       80;
#    server_name  localhost;
#
#    location / {
#        root   /usr/share/nginx/html;
#        index  index.html index.htm;
#        try_files $uri $uri/ /index.html; # Important for SPA
#    }
#
#    error_page   500 502 503 504  /50x.html;
#    location = /50x.html {
#        root   /usr/share/nginx/html;
#    }
# }
# Then uncomment the next line:
# COPY nginx.default.conf /etc/nginx/conf.d/default.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
