import httpx
import logging
from typing import Dict, Any, Optional, List

# Assuming BaseNotifierV1 is in libs.plugin_interfaces relative to a common root
# This path needs to be correct for the execution environment (e.g., Docker with PYTHONPATH)
from libs.plugin_interfaces.base_notifier_v1 import BaseNotifierV1

logger = logging.getLogger(__name__)

class WxPusherNotifier(BaseNotifierV1):
    DEFAULT_WXPUSHER_API_URL = "https://wxpusher.zjiecode.com/api/send/message"

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(notifier_type="wxpusher", config=config)
        self.app_token = self.config.get("app_token")
        if not self.app_token:
            # If no app_token, this notifier is essentially non-functional.
            # Depending on strictness, could raise an error or log a severe warning.
            logger.error("WxPusherNotifier: 'app_token' is missing from config. Notifications will fail.")
        
        self.api_url = self.config.get("base_url", self.DEFAULT_WXPUSHER_API_URL)
        # Cloudflare relay support (optional, from original project)
        self.cloudflare_url = self.config.get("cloudflare_worker_url")
        self.cloudflare_api_key = self.config.get("cloudflare_api_key")
        
        self.http_client: Optional[httpx.AsyncClient] = None

    async def init(self) -> None:
        """Initialize the HTTP client."""
        if self.http_client is None:
            # Standard headers; WxPusher doesn't require special ones beyond content-type for JSON
            headers = {'Content-Type': 'application/json'}
            self.http_client = httpx.AsyncClient(headers=headers, timeout=10.0)
        logger.info(f"WxPusherNotifier for type '{self.notifier_type}' initialized.")

    async def send(self, title: str, body: str, target_users: List[str], details: Optional[Dict[str, Any]] = None) -> None:
        if not self.app_token:
            logger.error("WxPusherNotifier: Cannot send notification, app_token is not configured.")
            return
        
        if not target_users:
            logger.warning("WxPusherNotifier: No target_users provided for notification.")
            return

        if not self.http_client:
            logger.error("WxPusherNotifier: HTTP client not initialized. Call init() first.")
            # Optionally, could try to auto-init here, but better to ensure init is called on startup.
            await self.init() # Attempt to init if not already
            if not self.http_client: # Still not initialized
                logger.error("WxPusherNotifier: HTTP client failed to initialize during send. Cannot send.")
                return

        # WxPusher content can be simple text. Title and body can be combined.
        # For more advanced formatting (HTML/Markdown), contentType would change.
        content = f"{title}\n\n{body}"
        if details and details.get("summary"): # Example of using details
            content += f"\n\nSummary: {details['summary']}"

        payload = {
            "appToken": self.app_token,
            "content": content,
            "summary": title, # Optional: WxPusher uses this for message list summary
            "contentType": 1,  # 1: text, 2: html, 3: markdown
            "uids": target_users,
            "verifyPay": False # As per original project
        }

        target_url = self.api_url
        if self.cloudflare_url:
            logger.debug(f"WxPusherNotifier: Using Cloudflare Worker relay: {self.cloudflare_url}")
            target_url = self.cloudflare_url
            if self.cloudflare_api_key:
                payload["apiKey"] = self.cloudflare_api_key # Assuming worker uses this key

        try:
            response = await self.http_client.post(target_url, json=payload)
            response_data = response.json() # Assuming WxPusher always returns JSON

            if response_data.get("code") == 1000:
                logger.info(f"WxPusherNotifier: Successfully sent notification to UIDs: {target_users}. Title: {title}")
            else:
                error_msg = response_data.get("msg", "Unknown error from WxPusher")
                logger.error(f"WxPusherNotifier: Failed to send notification. Status: {response_data.get('code')}, Message: {error_msg}, UIDs: {target_users}")

        except httpx.HTTPStatusError as e:
            logger.error(f"WxPusherNotifier: HTTP error sending notification: {e.response.status_code} - {e.response.text}. UIDs: {target_users}")
        except httpx.RequestError as e:
            logger.error(f"WxPusherNotifier: Request error sending notification: {e}. UIDs: {target_users}")
        except Exception as e: # Includes JSONDecodeError if response is not JSON
            logger.error(f"WxPusherNotifier: Generic error sending notification: {e}. UIDs: {target_users}")

    async def close(self) -> None:
        """Close the HTTP client."""
        if self.http_client:
            await self.http_client.aclose()
            self.http_client = None
        logger.info(f"WxPusherNotifier for type '{self.notifier_type}' closed.")
