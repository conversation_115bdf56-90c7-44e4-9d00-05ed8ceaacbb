"""
存储接口定义
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional


class IStorage(ABC):
    """存储接口"""

    @abstractmethod
    async def init(self) -> None:
        """初始化存储"""
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        """清理资源"""
        pass

    @abstractmethod
    async def load_subscriptions(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        加载所有订阅数据

        Returns:
            Dict[str, List[Dict[str, Any]]]: 订阅数据，格式为 {uid: [订阅列表]}
        """
        pass

    @abstractmethod
    async def save_subscriptions(self, data: Dict[str, List[Dict[str, Any]]]) -> bool:
        """
        保存所有订阅数据

        Args:
            data: 订阅数据，格式为 {uid: [订阅列表]}

        Returns:
            bool: 是否保存成功
        """
        pass

    @abstractmethod
    async def get_user_subscriptions(self, uid: str) -> List[Dict[str, Any]]:
        """
        获取指定用户的订阅列表

        Args:
            uid: 用户ID

        Returns:
            List[Dict[str, Any]]: 订阅列表
        """
        pass

    @abstractmethod
    async def get_all_subscriptions(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        获取所有用户的订阅数据

        Returns:
            Dict[str, List[Dict[str, Any]]]: 所有订阅数据
        """
        pass

    @abstractmethod
    async def add_subscription(self, uid: str, platform: str, room_id: str, settings: Dict = None) -> bool:
        """
        添加订阅

        Args:
            uid: 用户ID
            platform: 平台名称
            room_id: 房间ID
            settings: 自定义设置，如果为None则使用默认设置

        Returns:
            bool: 是否添加成功
        """
        pass

    @abstractmethod
    async def remove_subscription(self, uid: str, platform: str, room_id: str) -> bool:
        """
        移除订阅

        Args:
            uid: 用户ID
            platform: 平台名称
            room_id: 房间ID

        Returns:
            bool: 是否移除成功
        """
        pass

    @abstractmethod
    async def load_status_cache(self) -> Dict[str, Dict[str, Any]]:
        """
        加载状态缓存

        Returns:
            Dict[str, Dict[str, Any]]: 状态缓存，格式为 {platform_room_id: {状态信息}}
        """
        pass

    @abstractmethod
    async def save_status_cache(self, data: Dict[str, Dict[str, Any]]) -> bool:
        """
        保存状态缓存

        Args:
            data: 状态缓存，格式为 {platform_room_id: {状态信息}}

        Returns:
            bool: 是否保存成功
        """
        pass

    @abstractmethod
    async def get_status_cache(self, platform: str, room_id: str) -> Optional[Dict[str, Any]]:
        """
        获取指定主播的状态缓存

        Args:
            platform: 平台名称
            room_id: 房间ID

        Returns:
            Optional[Dict[str, Any]]: 状态缓存，如果不存在则返回None
        """
        pass

    @abstractmethod
    async def update_status_cache(self, platform: str, room_id: str, status: Dict[str, Any]) -> bool:
        """
        更新指定主播的状态缓存

        Args:
            platform: 平台名称
            room_id: 房间ID
            status: 状态信息

        Returns:
            bool: 是否更新成功
        """
        pass
