<template>
  <div class="edit-message-template-form">
    <h4>{{ formTitle }}</h4>
    <form @submit.prevent="submitForm">
      <div class="form-group">
        <label for="templateName">Template Name:</label>
        <input type="text" id="templateName" v_model="formData.template_name" required />
      </div>

      <div class="form-group">
        <label for="templateType">Template Type:</label>
        <select id="templateType" v_model="formData.template_type" required :disabled="isEditMode && !canChangeTypeOnEdit">
          <option disabled value="">Please select one</option>
          <option v-for="typeValue in templateTypeEnumValues" :key="typeValue" :value="typeValue">
            {{ formatTemplateType(typeValue) }}
          </option>
        </select>
        <small v-if="isEditMode && !canChangeTypeOnEdit">Template type cannot be changed after creation.</small>
      </div>

      <div class="form-group">
        <label for="subjectTemplate">Subject Template:</label>
        <input type="text" id="subjectTemplate" v_model="formData.subject_template" />
        <small>Variables like {{ liveExampleVariables }} can be used.</small>
      </div>

      <div class="form-group">
        <label for="bodyTemplate">Body Template (Main Content):</label>
        <textarea id="bodyTemplate" v-model="formData.body_template" rows="6" required></textarea>
        <small>Variables like {{ liveExampleVariables }} can be used. HTML is allowed if your notification channels support it.</small>
      </div>

      <!-- User ID is handled internally based on props.currentUserId for new templates -->
      <!-- is_default is not managed by this form -->

      <div class="form-actions">
        <button type="submit" :disabled="isLoading">
          {{ isLoading ? (isEditMode ? 'Saving...' : 'Creating...') : (isEditMode ? 'Save Changes' : 'Create Template') }}
        </button>
        <button type="button" @click="cancelForm" :disabled="isLoading">Cancel</button>
      </div>

      <div v_if="error" class="error-message">{{ error }}</div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue';
import { useMessageTemplateStore } from '@/stores/messageTemplates';
import { TemplateTypeEnum } from '@/types';
import type { MessageTemplate, MessageTemplateCreatePayload, MessageTemplateUpdatePayload } from '@/types';

const props = defineProps<{
  templateToEdit?: MessageTemplate | null;
  currentUserId?: string | null; // Used to associate new templates with a user
}>();

const emit = defineEmits<{
  (e: 'saveSuccess', createdOrUpdatedTemplate: MessageTemplate): void;
  (e: 'cancel'): void;
}>();

const templateStore = useMessageTemplateStore();
const templateTypeEnumValues = Object.values(TemplateTypeEnum);

// Example variables - this could be fetched or configured elsewhere
const liveExampleVariables = ref('{room_id}, {platform_name}, {title}, {user_name}');

// Configuration: whether template_type can be changed during an edit.
// Usually, type is fixed after creation.
const canChangeTypeOnEdit = ref(false);

interface FormData {
  template_name: string;
  template_type: TemplateTypeEnum | ''; // Allow empty for initial state before selection
  subject_template: string;
  body_template: string;
}

const initialFormData: FormData = {
  template_name: '',
  template_type: '',
  subject_template: '',
  body_template: '',
};

const formData = ref<FormData>({ ...initialFormData });

const isEditMode = computed(() => !!props.templateToEdit);
const formTitle = computed(() => (isEditMode.value ? 'Edit Message Template' : 'Create New Message Template'));

const isLoading = computed(() => templateStore.isLoading);
const error = computed(() => templateStore.error);

watch(
  () => props.templateToEdit,
  (newVal) => {
    if (newVal) {
      formData.value.template_name = newVal.template_name;
      formData.value.template_type = newVal.template_type;
      formData.value.subject_template = newVal.subject_template;
      formData.value.body_template = newVal.body_template;
    } else {
      resetForm();
    }
  },
  { immediate: true }
);

onMounted(() => {
  if (!isEditMode.value) {
    resetForm();
  }
});

function resetForm() {
  formData.value = { ...initialFormData };
  // Set a default type if desired, or leave it for user selection
  // formData.value.template_type = TemplateTypeEnum.LIVE_START;
  templateStore.error = null;
}

function formatTemplateType(type: TemplateTypeEnum): string {
  if (!type) return '';
  return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

async function submitForm() {
  if (formData.value.template_type === '') {
    templateStore.error = "Please select a template type.";
    return;
  }

  templateStore.error = null; // Clear previous errors

  try {
    let createdOrUpdatedTemplate: MessageTemplate | null = null;
    if (isEditMode.value && props.templateToEdit) {
      const updatePayload: MessageTemplateUpdatePayload = {
        template_name: formData.value.template_name,
        subject_template: formData.value.subject_template,
        body_template: formData.value.body_template,
        // template_type is not part of MessageTemplateUpdatePayload in store if not changeable
      };
       if (canChangeTypeOnEdit.value) {
        // If type were updatable, it would need to be in MessageTemplateUpdatePayload
        // (updatePayload as any).template_type = formData.value.template_type;
      }
      createdOrUpdatedTemplate = await templateStore.updateTemplate(props.templateToEdit.id, updatePayload);
    } else {
      const createPayload: MessageTemplateCreatePayload = {
        template_name: formData.value.template_name,
        template_type: formData.value.template_type as TemplateTypeEnum, // Cast: already checked not empty
        subject_template: formData.value.subject_template,
        body_template: formData.value.body_template,
        user_id: props.currentUserId || null, // Explicitly pass null if no user (for global by admin)
      };
      createdOrUpdatedTemplate = await templateStore.createTemplate(createPayload);
    }

    if (createdOrUpdatedTemplate && !templateStore.error) {
      emit('saveSuccess', createdOrUpdatedTemplate);
      // Form reset is handled by parent if component is unmounted/re-mounted
      // or call resetForm() if it remains visible.
    }
  } catch (e) {
    // Error is set in the store by the action itself.
    console.error("Form submission error:", e);
  }
}

function cancelForm() {
  // Resetting form here might be good if parent doesn't always unmount it
  // resetForm();
  emit('cancel');
}
</script>

<style scoped>
.edit-message-template-form {
  margin-top: 10px;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: #fdfdfd;
}
.form-group {
  margin-bottom: 15px;
}
.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}
.form-group input[type="text"],
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 1rem;
}
.form-group select {
  appearance: auto; /* Or use a custom dropdown style */
}
.form-group textarea {
  min-height: 100px;
  resize: vertical;
}
.form-group input:disabled,
.form-group select:disabled {
  background-color: #e9ecef;
  opacity: 0.7;
}
.form-group small {
  display: block;
  margin-top: 4px;
  font-size: 0.85em;
  color: #666;
}
.form-actions {
  margin-top: 20px;
  text-align: right;
}
.form-actions button {
  margin-left: 10px;
  padding: 10px 18px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
}
.form-actions button[type="submit"] {
  background-color: #28a745; /* Green */
  color: white;
}
.form-actions button[type="button"] {
  background-color: #6c757d; /* Gray */
  color: white;
}
.form-actions button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.error-message {
  margin-top: 15px;
  padding: 10px;
  color: #D8000C;
  background-color: #FFD2D2;
  border-radius: 4px;
  text-align: center;
}
</style>
