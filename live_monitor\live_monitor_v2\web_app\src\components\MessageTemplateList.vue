<template>
  <div class="message-template-list">
    <h3>Message Templates</h3>

    <div class="filters">
      <label for="templateTypeFilter">Filter by Type:</label>
      <select id="templateTypeFilter" v_model="selectedTemplateType">
        <option :value="null">All Types</option>
        <option v-for="typeValue in templateTypeEnumValues" :key="typeValue" :value="typeValue">
          {{ formatTemplateType(typeValue) }}
        </option>
      </select>

      <label for="userScopeFilter" v-if="currentUserId">Show:</label>
      <select id="userScopeFilter" v-model="userScope" v-if="currentUserId">
        <option value="user">My Templates</option>
        <option value="global">Global Defaults</option>
        <option value="all_relevant">My Templates + Global Defaults</option>
      </select>
      <button @click="applyFilters" :disabled="templateStore.isLoading">Apply Filters</button>
    </div>

    <div v_if="templateStore.isLoading" class="loading">Loading templates...</div>
    <div v_if="templateStore.error" class="error-message">Error: {{ templateStore.error }}</div>

    <ul v_if="filteredTemplates.length > 0">
      <li v-for="template in filteredTemplates" :key="template.id" class="template-item">
        <div class="template-details">
          <strong class="template-name">{{ template.template_name }}</strong>
          <span class="template-type">Type: {{ formatTemplateType(template.template_type) }}</span>
          <span class="template-owner">
            Owner: {{ template.user_id ? `User (${template.user_id.substring(0,8)}...)` : (template.is_default ? 'Global Default' : 'System') }}
          </span>
          <p class="template-subject">Subject: <code>{{ truncate(template.subject_template, 100) }}</code></p>
          <p class="template-body">Body Preview: <code>{{ truncate(template.body_template, 150) }}</code></p>
        </div>
        <div class="template-actions">
          <button @click="editTemplate(template)" :disabled="template.is_default && !canEditGlobalDefaults">Edit</button>
          <button @click="confirmDeleteTemplate(template.id)" :disabled="template.is_default && !canEditGlobalDefaults || (deletingTemplateId === template.id && templateStore.isLoading)" class="delete-btn">
            {{ deletingTemplateId === template.id && templateStore.isLoading ? 'Deleting...' : 'Delete' }}
          </button>
        </div>
      </li>
    </ul>
    <p v_if="!templateStore.isLoading && filteredTemplates.length === 0">
      No message templates found for the selected filters.
    </p>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useMessageTemplateStore } from '@/stores/messageTemplates';
import { useSubscriptionStore } from '@/stores/subscriptions'; // For currentUserId
import { TemplateTypeEnum } from '@/types';
import type { MessageTemplate } from '@/types';

const templateStore = useMessageTemplateStore();
const subscriptionStore = useSubscriptionStore();

const currentUserId = computed(() => subscriptionStore.userId);

// Assuming admin/special role might be able to edit global defaults
// For now, a simple flag, this would typically come from user roles/permissions store
const canEditGlobalDefaults = ref(false);

const selectedTemplateType = ref<TemplateTypeEnum | null>(null);
// 'user': only user-specific, 'global': only global defaults, 'all_relevant': user + global
const userScope = ref<'user' | 'global' | 'all_relevant'>('all_relevant');
const deletingTemplateId = ref<number | null>(null);

const templateTypeEnumValues = Object.values(TemplateTypeEnum);

const emit = defineEmits<{
  (e: 'editTemplate', template: MessageTemplate): void;
}>();

function formatTemplateType(type: TemplateTypeEnum): string {
  return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

function truncate(text: string, length: number): string {
  return text.length > length ? text.substring(0, length) + '...' : text;
}

async function fetchFilteredTemplates() {
  const typeToFetch = selectedTemplateType.value || undefined;

  if (userScope.value === 'all_relevant' && currentUserId.value) {
    await templateStore.fetchUserAndGlobalTemplates(currentUserId.value, typeToFetch);
  } else {
    let fetchUserId: string | undefined = undefined;
    if (userScope.value === 'user' && currentUserId.value) {
      fetchUserId = currentUserId.value;
    } else if (userScope.value === 'global') {
      // Fetches all accessible templates of the type (including global ones,
      // which are then filtered by the `filteredTemplates` computed prop for user_id === null or is_default)
      fetchUserId = undefined;
    } else {
      // This case handles:
      // 1. 'all_relevant' when no currentUserId (effectively same as 'global' in this scenario)
      // 2. Default scenario if userScope is somehow not 'user', 'global', or 'all_relevant' (though UI restricts this)
      // It will fetch all accessible templates, and `filteredTemplates` will determine what to show.
      fetchUserId = currentUserId.value || undefined; // Primarily for when currentUserId is null/undefined
    }
    await templateStore.fetchTemplates(fetchUserId, typeToFetch);
  }
}

const filteredTemplates = computed(() => {
  return templateStore.templates.filter(t => {
    let scopeMatch = false;
    if (userScope.value === 'user') {
      scopeMatch = t.user_id === currentUserId.value;
    } else if (userScope.value === 'global') {
      scopeMatch = t.user_id === null || t.is_default; // Assuming is_default also implies global accessible
    } else { // 'all_relevant'
      scopeMatch = (t.user_id === currentUserId.value) || (t.user_id === null || t.is_default);
    }
    // Type filter is handled by fetch, but can be re-applied if templates array holds mixed types
    const typeMatch = selectedTemplateType.value ? t.template_type === selectedTemplateType.value : true;
    return scopeMatch && typeMatch;
  });
});

onMounted(() => {
  // Initial fetch based on default filters
  applyFilters();
});

watch([currentUserId], () => {
    // Re-adjust scope if userId changes (e.g. user logs out)
    if (!currentUserId.value && userScope.value === 'user') {
        userScope.value = 'global'; // Default to global if user logs out
    }
    applyFilters();
});

function applyFilters() {
  fetchFilteredTemplates();
}

function editTemplate(template: MessageTemplate) {
  emit('editTemplate', template);
}

async function confirmDeleteTemplate(templateId: number) {
  if (window.confirm('Are you sure you want to delete this message template?')) {
    deletingTemplateId.value = templateId;
    try {
      await templateStore.deleteTemplate(templateId);
      // List should update automatically if store manipulates 'templates' array directly
      // or re-fetches. The current store action filters the local array.
    } catch (e) {
      // Error is set in store, displayed by templateStore.error
      console.error('Failed to delete template:', e);
    } finally {
      deletingTemplateId.value = null;
    }
  }
}

</script>

<style scoped>
.message-template-list {
  margin-top: 20px;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f9f9f9;
}
.filters {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  display: flex;
  gap: 15px;
  align-items: center;
}
.filters label {
  font-weight: bold;
}
.filters select, .filters button {
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ccc;
}
.filters button {
  background-color: #007bff;
  color: white;
  cursor: pointer;
}
.filters button:disabled {
  background-color: #aaa;
}

.loading, .error-message {
  padding: 10px;
  margin-bottom: 15px;
  border-radius: 4px;
  text-align: center;
}
.loading { background-color: #eef; }
.error-message { background-color: #fdd; color: #D8000C; }

ul {
  list-style-type: none;
  padding: 0;
}
.template-item {
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 6px;
  margin-bottom: 10px;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.template-details {
  flex-grow: 1;
}
.template-name {
  font-size: 1.2em;
  color: #337ab7;
  display: block;
  margin-bottom: 5px;
}
.template-type, .template-owner {
  font-size: 0.9em;
  color: #555;
  margin-right: 10px;
  display: inline-block;
  margin-bottom: 5px;
}
.template-subject, .template-body {
  font-size: 0.95em;
  color: #333;
  margin-top: 5px;
}
.template-subject code, .template-body code {
  background-color: #f0f0f0;
  padding: 2px 4px;
  border-radius: 3px;
  white-space: pre-wrap;
  word-break: break-all;
}
.template-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-left: 15px;
}
.template-actions button {
  padding: 6px 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  min-width: 80px;
}
.template-actions button.delete-btn {
  background-color: #f44336;
  color: white;
  border-color: #f44336;
}
.template-actions button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
