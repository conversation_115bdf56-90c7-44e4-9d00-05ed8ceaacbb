"""
配置加载模块 - 支持从文件和环境变量加载配置
"""
import os
import json
import yaml
from typing import Dict, Any, Optional

from .logger import setup_logger

logger = setup_logger('utils.config')

class ConfigLoader:
    """配置加载器"""

    @staticmethod
    def load_config(config_file: str, env_prefix: str = '') -> Dict[str, Any]:
        """
        加载配置文件，并使用环境变量覆盖

        Args:
            config_file: 配置文件路径
            env_prefix: 环境变量前缀

        Returns:
            Dict[str, Any]: 配置数据
        """
        # 加载配置文件
        config = ConfigLoader._load_file(config_file)
        if not config:
            logger.warning(f"配置文件 {config_file} 不存在或格式错误，使用空配置")
            config = {}

        # 使用环境变量覆盖配置
        if env_prefix:
            config = ConfigLoader._override_with_env(config, env_prefix)

        return config

    @staticmethod
    def _load_file(config_file: str) -> Optional[Dict[str, Any]]:
        """
        加载配置文件

        Args:
            config_file: 配置文件路径

        Returns:
            Optional[Dict[str, Any]]: 配置数据，如果加载失败则返回None
        """
        if not os.path.exists(config_file):
            logger.warning(f"配置文件 {config_file} 不存在")
            return None

        try:
            ext = os.path.splitext(config_file)[1].lower()
            with open(config_file, 'r', encoding='utf-8') as f:
                if ext == '.json':
                    return json.load(f)
                elif ext in ['.yaml', '.yml']:
                    return yaml.safe_load(f)
                else:
                    logger.error(f"不支持的配置文件格式: {ext}")
                    return None
        except Exception as e:
            logger.error(f"加载配置文件 {config_file} 失败: {str(e)}")
            return None

    @staticmethod
    def _override_with_env(config: Dict[str, Any], prefix: str) -> Dict[str, Any]:
        """
        使用环境变量覆盖配置

        Args:
            config: 原始配置
            prefix: 环境变量前缀

        Returns:
            Dict[str, Any]: 覆盖后的配置
        """
        result = config.copy()

        # 遍历所有环境变量
        for key, value in os.environ.items():
            # 检查是否以指定前缀开头
            if key.startswith(prefix):
                # 移除前缀，转换为小写，并使用下划线分隔
                config_key = key[len(prefix):].lower()

                # 尝试转换为适当的类型
                try:
                    # 尝试转换为数字
                    if value.isdigit():
                        value = int(value)
                    elif value.replace('.', '', 1).isdigit() and value.count('.') == 1:
                        value = float(value)
                    # 尝试转换为布尔值
                    elif value.lower() in ['true', 'false']:
                        value = value.lower() == 'true'
                    # 尝试转换为JSON
                    elif value.startswith('{') or value.startswith('['):
                        try:
                            value = json.loads(value)
                        except json.JSONDecodeError:
                            pass
                except Exception:
                    pass

                # 更新配置
                result[config_key] = value
                logger.info(f"使用环境变量 {key} 覆盖配置 {config_key}")

        return result
