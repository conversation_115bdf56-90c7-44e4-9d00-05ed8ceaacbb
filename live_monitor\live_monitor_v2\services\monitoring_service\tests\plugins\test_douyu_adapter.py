import asyncio
import unittest
from unittest.mock import patch, AsyncMock, MagicMock
from typing import Dict, Any, Optional
from datetime import datetime, timezone
import httpx # For raising httpx.RequestError

# Adjust the import path based on your project structure and PYTHONPATH
# Assuming 'live_monitor_v2' is the root for imports or PYTHONPATH is set accordingly.
from services.monitoring_service.plugins.douyu_adapter import DouyuAdapter

class TestDouyuAdapter(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        # Example config that might be passed to the adapter
        self.adapter_config = {
            "display_name": "Douyu Test",
            # "api_url_template": "...", # Can override defaults if needed for tests
        }
        self.adapter = DouyuAdapter(config=self.adapter_config)
        
        # Mock the internal http_client of the adapter instance
        # This allows us to control responses for each test method specifically
        self.mock_http_client_instance = AsyncMock(spec=httpx.AsyncClient)
        self.adapter.http_client = self.mock_http_client_instance # Replace instance's client

        # Common mock response structure
        self.mock_response = AsyncMock(spec=httpx.Response)
        self.mock_http_client_instance.get = AsyncMock(return_value=self.mock_response)


    async def test_init_and_close_adapter(self):
        # Test that init creates a client if one isn't there, and close closes it
        adapter = DouyuAdapter(config=self.adapter_config) # Fresh instance
        self.assertIsNone(adapter.http_client)
        await adapter.init()
        self.assertIsNotNone(adapter.http_client)
        self.assertIsInstance(adapter.http_client, httpx.AsyncClient)
        
        # Check if close is awaitable and sets client to None
        # We need to mock aclose on the real client if we were to test it fully
        # For now, just test our adapter's close method logic
        real_client = adapter.http_client
        with patch.object(real_client, 'aclose', new_callable=AsyncMock) as mock_aclose:
            await adapter.close()
            mock_aclose.assert_called_once()
            self.assertIsNone(adapter.http_client)


    async def test_get_platform_display_name(self):
        self.assertEqual(self.adapter.get_platform_display_name(), "Douyu Test")
        
        # Test with default display name
        adapter_default_name = DouyuAdapter()
        self.assertEqual(adapter_default_name.get_platform_display_name(), "斗鱼")

    async def test_get_streamer_status_live(self):
        room_id = "12345"
        mock_api_response = {
            "error": 0,
            "data": {
                "room_id": room_id,
                "owner_name": "TestAnchor",
                "room_name": "Live Title Test",
                "room_status": "1", # Live
                "room_thumb": "http://example.com/thumb.jpg",
                "online_num": 1000, # Example viewer count field
                "start_time": "1678886400" # Example Unix timestamp string for March 15, 2023 12:00:00 PM UTC
            }
        }
        self.mock_response.status_code = 200
        self.mock_response.json.return_value = mock_api_response

        status = await self.adapter.get_streamer_status(room_id)

        self.assertIsNotNone(status)
        self.assertEqual(status["platform_name"], "douyu")
        self.assertEqual(status["room_id"], room_id)
        self.assertEqual(status["anchor_name"], "TestAnchor")
        self.assertEqual(status["title"], "Live Title Test")
        self.assertTrue(status["live_status"])
        self.assertEqual(status["stream_url"], f"https://www.douyu.com/{room_id}")
        self.assertEqual(status["cover_image_url"], "http://example.com/thumb.jpg")
        self.assertEqual(status["viewer_count"], 1000)
        self.assertIsInstance(status["start_time"], datetime)
        self.assertEqual(status["start_time"], datetime.fromtimestamp(1678886400, tz=timezone.utc))
        self.mock_http_client_instance.get.assert_called_once()

    async def test_get_streamer_status_offline(self):
        room_id = "67890"
        mock_api_response = {
            "error": 0,
            "data": {
                "room_id": room_id,
                "owner_name": "OfflineAnchor",
                "room_name": "Offline Title",
                "room_status": "2", # Offline
                "room_thumb": "http://example.com/thumb_offline.jpg",
                "online_num": 0,
                "start_time": "0" # Or some other non-live indicator
            }
        }
        self.mock_response.status_code = 200
        self.mock_response.json.return_value = mock_api_response

        status = await self.adapter.get_streamer_status(room_id)

        self.assertIsNotNone(status)
        self.assertEqual(status["anchor_name"], "OfflineAnchor")
        self.assertEqual(status["title"], "Offline Title")
        self.assertFalse(status["live_status"])
        self.assertIsNone(status["start_time"]) # Should be None if offline
        self.mock_http_client_instance.get.assert_called_once()

    async def test_get_streamer_status_api_error_code(self):
        room_id = "error_room"
        mock_api_response = {"error": 1, "data": "Error from API"} # Douyu error format
        self.mock_response.status_code = 200 # API itself returns 200 but with error code
        self.mock_response.json.return_value = mock_api_response

        status = await self.adapter.get_streamer_status(room_id)
        self.assertIsNone(status)
        self.mock_http_client_instance.get.assert_called_once()

    async def test_get_streamer_status_http_error(self):
        room_id = "http_error_room"
        self.mock_http_client_instance.get.side_effect = httpx.HTTPStatusError(
            message="Simulated HTTP error", 
            request=MagicMock(spec=httpx.Request), 
            response=MagicMock(spec=httpx.Response, status_code=500, text="Server Error")
        )

        status = await self.adapter.get_streamer_status(room_id)
        self.assertIsNone(status)
        self.mock_http_client_instance.get.assert_called_once()

    async def test_get_streamer_status_request_error(self):
        room_id = "request_error_room"
        self.mock_http_client_instance.get.side_effect = httpx.RequestError(
            message="Simulated connection error", 
            request=MagicMock(spec=httpx.Request)
        )
        status = await self.adapter.get_streamer_status(room_id)
        self.assertIsNone(status)
        self.mock_http_client_instance.get.assert_called_once()

    async def test_get_streamer_status_json_decode_error(self):
        room_id = "json_error_room"
        self.mock_response.status_code = 200
        self.mock_response.json.side_effect = json.JSONDecodeError("Simulated JSON error", "doc", 0)
        self.mock_response.text = "Not a valid JSON" # For logging

        status = await self.adapter.get_streamer_status(room_id)
        self.assertIsNone(status)
        self.mock_http_client_instance.get.assert_called_once()

    async def test_get_streamer_status_missing_data_fields(self):
        room_id = "missing_fields"
        # Valid API response structure but missing crucial sub-fields
        mock_api_response = { 
            "error": 0, 
            "data": {
                # "owner_name": "Test", # Missing anchor_name
                "room_name": "Test Title",
                "room_status": "1" 
            }
        }
        self.mock_response.status_code = 200
        self.mock_response.json.return_value = mock_api_response
        
        status = await self.adapter.get_streamer_status(room_id)
        self.assertIsNotNone(status) # Should still process with defaults
        self.assertEqual(status["anchor_name"], "Unknown Anchor") # Default value
        self.assertEqual(status["title"], "Test Title")
        self.assertTrue(status["live_status"])

if __name__ == "__main__":
    # This allows running the tests directly via `python test_douyu_adapter.py`
    # However, typically use `python -m unittest discover ...`
    unittest.main()
