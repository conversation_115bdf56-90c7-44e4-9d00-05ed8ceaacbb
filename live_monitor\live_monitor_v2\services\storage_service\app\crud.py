from sqlalchemy.orm import Session
from typing import List, Dict, Optional, Type

from . import models, schemas

# --- Subscription CRUD ---

def get_subscription(db: Session, subscription_id: int) -> Optional[models.Subscription]:
    return db.query(models.Subscription).filter(models.Subscription.id == subscription_id).first()

def get_user_subscriptions(db: Session, user_id: str) -> List[models.Subscription]:
    return db.query(models.Subscription).filter(models.Subscription.user_id == user_id).all()

def create_subscription(db: Session, subscription: schemas.SubscriptionCreate) -> models.Subscription:
    db_subscription = models.Subscription(
        user_id=subscription.user_id,
        platform_name=subscription.platform_name,
        room_id=subscription.room_id,
        settings=subscription.settings.model_dump() # Pydantic V2
    )
    db.add(db_subscription)
    db.commit()
    db.refresh(db_subscription)
    return db_subscription

def delete_subscription(db: Session, subscription_id: int) -> Optional[models.Subscription]:
    db_subscription = get_subscription(db, subscription_id)
    if db_subscription:
        db.delete(db_subscription)
        db.commit()
    return db_subscription # Returns the deleted object or None if not found

def update_subscription_settings(db: Session, subscription_id: int, settings: schemas.SubscriptionSettings) -> Optional[models.Subscription]:
    db_subscription = get_subscription(db, subscription_id)
    if db_subscription:
        db_subscription.settings = settings.model_dump() # Pydantic V2
        db.commit()
        db.refresh(db_subscription)
    return db_subscription

def get_all_active_subscriptions_grouped_by_user(db: Session) -> Dict[str, List[models.Subscription]]:
    all_subs = db.query(models.Subscription).all()
    grouped_subs: Dict[str, List[models.Subscription]] = {}
    for sub in all_subs:
        if sub.user_id not in grouped_subs:
            grouped_subs[sub.user_id] = []
        grouped_subs[sub.user_id].append(sub)
    return grouped_subs

# --- NotificationChannel CRUD ---

def get_notification_channel(db: Session, user_id: str, channel_type: str) -> Optional[models.NotificationChannel]:
    return db.query(models.NotificationChannel).filter(
        models.NotificationChannel.user_id == user_id,
        models.NotificationChannel.channel_type == channel_type
    ).first()

def get_user_notification_channels(db: Session, user_id: str) -> List[models.NotificationChannel]:
    return db.query(models.NotificationChannel).filter(models.NotificationChannel.user_id == user_id).all()

def upsert_notification_channel(db: Session, channel_data: schemas.NotificationChannelCreate) -> models.NotificationChannel:
    db_channel = get_notification_channel(db, user_id=channel_data.user_id, channel_type=channel_data.channel_type)
    if db_channel:
        db_channel.channel_uid = channel_data.channel_uid
    else:
        db_channel = models.NotificationChannel(
            user_id=channel_data.user_id,
            channel_type=channel_data.channel_type,
            channel_uid=channel_data.channel_uid
        )
        db.add(db_channel)
    db.commit()
    db.refresh(db_channel)
    return db_channel

# --- MessageTemplate CRUD ---

def create_message_template(db: Session, template: schemas.MessageTemplateCreate) -> models.MessageTemplate:
    db_template = models.MessageTemplate(
        name=template.name,
        template_content=template.template_content,
        template_type=template.template_type.value, # Use enum value
        user_id=template.user_id
    )
    db.add(db_template)
    db.commit()
    db.refresh(db_template)
    return db_template

def get_message_template(db: Session, template_id: int) -> Optional[models.MessageTemplate]:
    return db.query(models.MessageTemplate).filter(models.MessageTemplate.id == template_id).first()

def get_message_templates_by_user_and_type(
    db: Session, 
    user_id: Optional[str] = None, 
    template_type: Optional[schemas.TemplateTypeEnum] = None
) -> List[models.MessageTemplate]:
    query = db.query(models.MessageTemplate)
    if user_id is not None: # Specific user's templates (could be null in DB for global)
        query = query.filter(models.MessageTemplate.user_id == user_id)
    else: # Global templates
        query = query.filter(models.MessageTemplate.user_id.is_(None))
        
    if template_type:
        query = query.filter(models.MessageTemplate.template_type == template_type.value)
    return query.all()

def update_message_template(
    db: Session, 
    template_id: int, 
    template_update: schemas.MessageTemplateUpdate
) -> Optional[models.MessageTemplate]:
    db_template = get_message_template(db, template_id)
    if db_template:
        update_data = template_update.model_dump(exclude_unset=True) # Pydantic V2
        for key, value in update_data.items():
            if key == "template_type" and value is not None: # Handle enum
                setattr(db_template, key, value.value)
            elif value is not None: # Ensure other fields are not accidentally set to None if not provided
                setattr(db_template, key, value)
        db.commit()
        db.refresh(db_template)
    return db_template

def delete_message_template(db: Session, template_id: int) -> bool:
    db_template = get_message_template(db, template_id)
    if db_template:
        db.delete(db_template)
        db.commit()
        return True
    return False
