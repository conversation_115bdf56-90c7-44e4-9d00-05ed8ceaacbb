"""
Web路由模块
"""
import os
from aiohttp import web
from aiohttp.web import FileResponse

from app.utils.logger import setup_logger

logger = setup_logger('web.routes')

routes = web.RouteTableDef()

# 获取静态文件和模板的路径
STATIC_DIR = os.path.join(os.path.dirname(__file__), 'static')
TEMPLATES_DIR = os.path.join(os.path.dirname(__file__), 'templates')

@routes.get('/')
async def index(request: web.Request) -> web.Response:
    """渲染主页"""
    return FileResponse(os.path.join(TEMPLATES_DIR, 'index.html'))

@routes.get('/static/{path:.*}')
async def static_files(request: web.Request) -> web.Response:
    """提供静态文件服务"""
    path = request.match_info['path']
    file_path = os.path.join(STATIC_DIR, path)

    if os.path.exists(file_path) and os.path.isfile(file_path):
        return FileResponse(file_path)
    else:
        raise web.HTTPNotFound()
