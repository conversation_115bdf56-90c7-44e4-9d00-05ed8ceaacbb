FROM python:3.10-slim

WORKDIR /app

# Copy requirements first for layer caching
COPY ./requirements.txt /app/requirements.txt
RUN pip install --no-cache-dir --upgrade pip
RUN pip install --no-cache-dir -r /app/requirements.txt

# Copy the application code for the notification service
COPY ./app /app/app

# Copy the notifiers (plugins) directory for this service
COPY ./notifiers /app/notifiers

# Copy the shared libraries (plugin_interfaces, shared_models)
# Assumes Docker build context is 'live_monitor_v2/services/notification_service/'
# and 'libs' is at '../../libs' relative to this Dockerfile.
COPY ../../libs /app/libs

# Set PYTHONPATH to include the /app directory, so 'app.config', 'notifiers.module', 
# and 'libs.shared_models' can be imported.
ENV PYTHONPATH=/app

# Command to run the notification service
CMD ["python", "app/main.py"]
