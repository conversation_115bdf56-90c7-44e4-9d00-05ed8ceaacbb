from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Dict, Optional

from . import crud, models, schemas
from .database import SessionLocal, engine, get_db # Ensure get_db is imported

# Create tables if they don't exist (for simple dev, Alembic is better for production)
# models.Base.metadata.create_all(bind=engine) # Comment out or remove if using Alembic for table creation

app = FastAPI(title="Storage Service")

# --- Subscription Endpoints ---

@app.post("/users/{user_id}/subscriptions", response_model=schemas.Subscription, status_code=status.HTTP_201_CREATED)
def create_user_subscription(user_id: str, subscription_in: schemas.SubscriptionCreate, db: Session = Depends(get_db)):
    if subscription_in.user_id != user_id:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="User ID in path does not match User ID in body.")
    return crud.create_subscription(db=db, subscription=subscription_in)

@app.get("/users/{user_id}/subscriptions", response_model=List[schemas.Subscription])
def read_user_subscriptions(user_id: str, db: Session = Depends(get_db)):
    subscriptions = crud.get_user_subscriptions(db=db, user_id=user_id)
    return subscriptions

@app.delete("/users/{user_id}/subscriptions/{subscription_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_user_subscription(user_id: str, subscription_id: int, db: Session = Depends(get_db)):
    db_subscription = crud.get_subscription(db, subscription_id=subscription_id)
    if db_subscription is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Subscription not found")
    if db_subscription.user_id != user_id:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to delete this subscription")
    
    crud.delete_subscription(db=db, subscription_id=subscription_id)
    return None 

@app.put("/users/{user_id}/subscriptions/{subscription_id}", response_model=schemas.Subscription)
def update_user_subscription_settings(
    user_id: str, 
    subscription_id: int, 
    settings_in: schemas.SubscriptionSettings, 
    db: Session = Depends(get_db)
):
    db_subscription = crud.get_subscription(db, subscription_id=subscription_id)
    if db_subscription is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Subscription not found")
    if db_subscription.user_id != user_id:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to update this subscription")
    
    updated_subscription = crud.update_subscription_settings(
        db=db, 
        subscription_id=subscription_id, 
        settings=settings_in
    )
    # crud.update_subscription_settings already handles the "not found" case by returning None,
    # but get_subscription above ensures it exists.
    return updated_subscription

# --- Notification Channel Endpoints ---

@app.put("/users/{user_id}/notification_channels/{channel_type}", response_model=schemas.NotificationChannel)
def set_user_notification_channel(
    user_id: str, 
    channel_type: str, 
    channel_data_in: schemas.NotificationChannelCreate, 
    db: Session = Depends(get_db)
):
    if channel_data_in.user_id != user_id:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="User ID in path does not match User ID in body.")
    if channel_data_in.channel_type != channel_type:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Channel type in path does not match channel type in body.")
        
    return crud.upsert_notification_channel(db=db, channel_data=channel_data_in)

@app.get("/users/{user_id}/notification_channels", response_model=List[schemas.NotificationChannel])
def read_user_notification_channels(user_id: str, db: Session = Depends(get_db)):
    channels = crud.get_user_notification_channels(db=db, user_id=user_id)
    return channels

@app.get("/users/{user_id}/notification_channels/{channel_type}", response_model=schemas.NotificationChannel)
def read_user_notification_channel(user_id: str, channel_type: str, db: Session = Depends(get_db)):
    channel = crud.get_notification_channel(db=db, user_id=user_id, channel_type=channel_type)
    if channel is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Notification channel not found")
    return channel

# --- Message Template Endpoints ---

@app.post("/templates", response_model=schemas.MessageTemplate, status_code=status.HTTP_201_CREATED)
def create_message_template_endpoint(
    template_in: schemas.MessageTemplateCreate, 
    db: Session = Depends(get_db)
):
    # Optional: Add logic to check for duplicate name/type/user_id if not handled by DB unique constraint well
    # or if a more specific error message is desired.
    # For now, relying on DB constraint and generic error handling if it occurs.
    return crud.create_message_template(db=db, template=template_in)

@app.get("/templates", response_model=List[schemas.MessageTemplate])
def list_message_templates_endpoint(
    user_id: Optional[str] = None, 
    template_type: Optional[schemas.TemplateTypeEnum] = None, # Use the Enum for validation
    db: Session = Depends(get_db)
):
    return crud.get_message_templates_by_user_and_type(db=db, user_id=user_id, template_type=template_type)

@app.get("/templates/{template_id}", response_model=schemas.MessageTemplate)
def get_message_template_endpoint(template_id: int, db: Session = Depends(get_db)):
    db_template = crud.get_message_template(db, template_id=template_id)
    if db_template is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Message template not found")
    return db_template

@app.put("/templates/{template_id}", response_model=schemas.MessageTemplate)
def update_message_template_endpoint(
    template_id: int, 
    template_in: schemas.MessageTemplateUpdate, 
    db: Session = Depends(get_db)
):
    updated_template = crud.update_message_template(db=db, template_id=template_id, template_update=template_in)
    if updated_template is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Message template not found for update")
    return updated_template

@app.delete("/templates/{template_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_message_template_endpoint(template_id: int, db: Session = Depends(get_db)):
    deleted = crud.delete_message_template(db=db, template_id=template_id)
    if not deleted:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Message template not found for deletion")
    return None


# --- Internal Endpoint ---

@app.get("/internal/subscriptions/all_active", response_model=Dict[str, List[schemas.Subscription]])
def get_all_active_subscriptions_for_monitoring(db: Session = Depends(get_db)):
    return crud.get_all_active_subscriptions_grouped_by_user(db=db)

# --- Health Check ---
@app.get("/health", status_code=200)
async def health_check():
    return {"status": "ok"}
