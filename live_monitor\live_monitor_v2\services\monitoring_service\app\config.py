from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field # Import Field
from typing import List, Optional, Dict, Any # Import Dict, Any

class PlatformAdapterConfig(BaseSettings): # Inherit from BaseSettings for potential env var overrides per adapter
    enabled: bool = True
    # specific_settings allows arbitrary key-value pairs for platform-specific needs
    specific_settings: Dict[str, Any] = Field(default_factory=dict)

    # If you want these to also be configurable via nested env vars,
    # you might need to adjust prefixing or use nested models.
    # For now, specific_settings are expected to be defined in a YAML/dict structure.
    # Example: LIVE_MONITOR_PLATFORM_ADAPTERS__BILIBILI__SPECIFIC_SETTINGS='{"key": "value"}' (if using complex env var parsing)
    # Or more simply, the whole platform_adapters dict is loaded from one complex env var or YAML.

class Settings(BaseSettings):
    STORAGE_SERVICE_URL: str = "http://localhost:8000" 
    MESSAGE_BROKER_URL: str = "amqp://lmv2_user:lmv2_password@localhost:5672/" 
    MONITOR_INTERVAL_SECONDS: int = 60
    
    # Changed from plugin_dir: str = "plugins" to reflect yaml example structure
    # This will be the Python import path to the plugins package, e.g., "app.plugins"
    # The plugin_loader.py used this as a base path for file scanning.
    # If using Python module path, it should be e.g. "services.monitoring_service.app.plugins"
    # For simplicity and consistency with Docker, "plugins" as a top-level dir in /app is easier.
    PLUGIN_DIR: str = "plugins" # This refers to the 'plugins' directory inside 'app'

    # Configuration for individual platform adapters
    # Key is the platform_name (e.g., "bilibili")
    platform_adapters: Dict[str, PlatformAdapterConfig] = Field(default_factory=dict)
    
    model_config = SettingsConfigDict(
        env_file=".env", 
        env_file_encoding='utf-8', 
        extra='ignore',
        env_prefix='LIVE_MONITOR_' # e.g. LIVE_MONITOR_STORAGE_SERVICE_URL
    )

settings = Settings()
