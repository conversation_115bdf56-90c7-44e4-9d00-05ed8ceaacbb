import asyncio
import unittest
from unittest.mock import patch, MagicMock, ANY

from app.core.notification_service import NotificationService
from app.notifications.base import BaseNotifier
from app.core.container import container # To mock its .get method
from typing import Dict, Optional, List, Any

# --- Mock Notifier Classes ---
class MockNotifier(BaseNotifier):
    def __init__(self, name="default_mock", config: Optional[Dict] = None): # Added config for potential future use
        self.name = name
        self.config = config or {}
        self.send_called_with: List[Dict[str, Any]] = []
        self.init_called = False
        self.cleanup_called = False
        self.initialized_with_service = False # For testing init from service

    async def send(self, title: str, body: str, details: Optional[Dict] = None) -> None:
        self.send_called_with.append({'title': title, 'body': body, 'details': details})

    async def init(self) -> None:
        self.init_called = True

    async def cleanup(self) -> None:
        self.cleanup_called = True

class FailingMockNotifier(MockNotifier):
    def __init__(self, name="failing_mock", config: Optional[Dict] = None):
        super().__init__(name, config)

    async def send(self, title: str, body: str, details: Optional[Dict] = None) -> None:
        # First, record the call like the parent
        await super().send(title, body, details)
        # Then, raise the simulated error
        raise RuntimeError(f"Simulated send failure from {self.name}")

# --- Test Case ---
class TestNotificationService(unittest.IsolatedAsyncioTestCase):

    def _get_mock_app_config(self, notifiers_config_list: Optional[List[Dict]] = None):
        """Helper to create a mock application configuration structure."""
        if notifiers_config_list is None:
            notifiers_config_list = []
        return {
            'notifications': {
                'notifiers': notifiers_config_list
            }
            # Other app configs (wxpusher, etc.) can be added here if needed by notifiers
        }

    @patch('app.core.notification_service.container')
    async def test_load_successful_notifier(self, mock_container_in_service):
        """Test that a correctly configured notifier is loaded."""
        mock_config = self._get_mock_app_config([
            {'type': 'mock_good', 'enabled': True, 'module_path': 'test.notifiers.good', 'class_name': 'GoodNotifier'}
        ])
        mock_container_in_service.get.return_value = mock_config

        mock_module = MagicMock()
        mock_module.GoodNotifier = MockNotifier # Our mock class

        with patch('importlib.import_module', return_value=mock_module) as mock_import:
            service = NotificationService()
            mock_import.assert_called_once_with('test.notifiers.good')
            self.assertEqual(len(service.active_notifiers), 1)
            self.assertIsInstance(service.active_notifiers[0], MockNotifier)

    @patch('app.core.notification_service.container')
    async def test_load_successful_notifier_with_inferred_paths(self, mock_container_in_service):
        """Test successful loading with inferred module_path and class_name."""
        mock_config = self._get_mock_app_config([
            {'type': 'mock_infer', 'enabled': True} # module_path and class_name will be inferred
        ])
        mock_container_in_service.get.return_value = mock_config

        mock_module = MagicMock()
        mock_module.AsyncMockInfer = MockNotifier # Corrected case: AsyncMockInfer

        with patch('importlib.import_module', return_value=mock_module) as mock_import:
            service = NotificationService()
            mock_import.assert_called_once_with('app.notifiers.mock_infer')
            self.assertEqual(len(service.active_notifiers), 1)
            self.assertIsInstance(service.active_notifiers[0], MockNotifier)


    @patch('app.core.notification_service.container')
    async def test_load_notifier_disabled(self, mock_container_in_service):
        """Test that a disabled notifier is not loaded."""
        mock_config = self._get_mock_app_config([
            {'type': 'mock_disabled', 'enabled': False, 'module_path': 'test.notifiers.disabled', 'class_name': 'DisabledNotifier'}
        ])
        mock_container_in_service.get.return_value = mock_config
        
        with patch('importlib.import_module') as mock_import: # Should not be called
            service = NotificationService()
            self.assertEqual(len(service.active_notifiers), 0)
            mock_import.assert_not_called()

    @patch('app.core.notification_service.container')
    @patch('importlib.import_module', side_effect=ImportError("Simulated module not found"))
    async def test_load_module_not_found(self, mock_import_module, mock_container_in_service):
        """Test graceful handling of module import failure."""
        mock_config = self._get_mock_app_config([
            {'type': 'non_existent', 'enabled': True, 'module_path': 'non.existent.module', 'class_name': 'SomeClass'}
        ])
        mock_container_in_service.get.return_value = mock_config
        
        with self.assertLogs('core.notification_service', level='ERROR') as cm:
            service = NotificationService()
            self.assertEqual(len(service.active_notifiers), 0)
        self.assertTrue(any("Failed to import notifier module non.existent.module" in log for log in cm.output))


    @patch('app.core.notification_service.container')
    async def test_load_class_not_found(self, mock_container_in_service):
        """Test graceful handling of class not found in an existing module."""
        mock_config = self._get_mock_app_config([
            {'type': 'mock_class_missing', 'enabled': True, 'module_path': 'test.notifiers.class_missing', 'class_name': 'MissingNotifier'}
        ])
        mock_container_in_service.get.return_value = mock_config

        mock_module = MagicMock()
        del mock_module.MissingNotifier 
        
        with patch('importlib.import_module', return_value=mock_module) as mock_import:
            with self.assertLogs('core.notification_service', level='ERROR') as cm:
                service = NotificationService()
                self.assertEqual(len(service.active_notifiers), 0)
            mock_import.assert_called_once_with('test.notifiers.class_missing')
            self.assertTrue(any("Failed to find class MissingNotifier" in log for log in cm.output))

    @patch('app.core.notification_service.container')
    async def test_dispatch_notification_single_notifier(self, mock_container_in_service):
        """Test dispatching to a single active notifier."""
        mock_container_in_service.get.return_value = self._get_mock_app_config() 
        service = NotificationService() 

        mock_notifier = MockNotifier()
        service.active_notifiers = [mock_notifier] 

        title, body, details = "Test Title", "Test Body", {'uids': ['test_uid']}
        await service.dispatch_notification(title, body, details)

        self.assertEqual(len(mock_notifier.send_called_with), 1)
        self.assertEqual(mock_notifier.send_called_with[0]['title'], title)
        self.assertEqual(mock_notifier.send_called_with[0]['body'], body)
        self.assertEqual(mock_notifier.send_called_with[0]['details'], details)

    @patch('app.core.notification_service.container')
    async def test_dispatch_notification_multiple_notifiers(self, mock_container_in_service):
        """Test dispatching to multiple active notifiers."""
        mock_container_in_service.get.return_value = self._get_mock_app_config()
        service = NotificationService()

        mock_notifier1 = MockNotifier("notifier1")
        mock_notifier2 = MockNotifier("notifier2")
        service.active_notifiers = [mock_notifier1, mock_notifier2]

        title, body, details = "Multi Test", "Body for Multi", {'info': 'data'}
        await service.dispatch_notification(title, body, details)

        for notifier in [mock_notifier1, mock_notifier2]:
            self.assertEqual(len(notifier.send_called_with), 1)
            self.assertEqual(notifier.send_called_with[0]['title'], title)
            self.assertEqual(notifier.send_called_with[0]['body'], body)
            self.assertEqual(notifier.send_called_with[0]['details'], details)

    @patch('app.core.notification_service.container')
    async def test_dispatch_notification_no_active_notifiers(self, mock_container_in_service):
        """Test dispatching with no active notifiers (should not error)."""
        mock_container_in_service.get.return_value = self._get_mock_app_config()
        service = NotificationService()
        service.active_notifiers = [] 

        with self.assertLogs('core.notification_service', level='WARNING') as cm:
            await service.dispatch_notification("No Notify Title", "No Notify Body")
        self.assertTrue(any("Dispatch called, but no active notifiers loaded" in log for log in cm.output))


    @patch('app.core.notification_service.container')
    async def test_dispatch_notification_with_failing_notifier(self, mock_container_in_service):
        """Test that one failing notifier does not stop others."""
        mock_container_in_service.get.return_value = self._get_mock_app_config()
        service = NotificationService()

        mock_notifier_good = MockNotifier("good_one")
        mock_notifier_failing = FailingMockNotifier("bad_one")
        service.active_notifiers = [mock_notifier_good, mock_notifier_failing]

        title, body = "Failure Test", "Testing Resilience"
        
        with self.assertLogs('core.notification_service', level='ERROR') as cm:
            await service.dispatch_notification(title, body)
        
        self.assertEqual(len(mock_notifier_good.send_called_with), 1)
        self.assertEqual(mock_notifier_good.send_called_with[0]['title'], title)

        self.assertEqual(len(mock_notifier_failing.send_called_with), 1)
        self.assertEqual(mock_notifier_failing.send_called_with[0]['title'], title)
        
        self.assertTrue(any(f"Error sending notification via FailingMockNotifier" in log for log in cm.output))
        self.assertTrue(any("Simulated send failure from bad_one" in log for log in cm.output))


    @patch('app.core.notification_service.container')
    async def test_init_all_notifiers(self, mock_container_in_service):
        """Test that init_all_notifiers calls init on active notifiers."""
        mock_container_in_service.get.return_value = self._get_mock_app_config()
        service = NotificationService()

        mock_notifier1 = MockNotifier("n1")
        mock_notifier2 = MockNotifier("n2")
        mock_notifier_no_init = MagicMock(spec=BaseNotifier) 
        del mock_notifier_no_init.init 

        service.active_notifiers = [mock_notifier1, mock_notifier_no_init, mock_notifier2]

        await service.init_all_notifiers()

        self.assertTrue(mock_notifier1.init_called)
        self.assertTrue(mock_notifier2.init_called)

    @patch('app.core.notification_service.container')
    async def test_cleanup_all_notifiers(self, mock_container_in_service):
        """Test that cleanup_all_notifiers calls cleanup on active notifiers."""
        mock_container_in_service.get.return_value = self._get_mock_app_config()
        service = NotificationService()

        mock_notifier1 = MockNotifier("n1_cleanup")
        mock_notifier2 = MockNotifier("n2_cleanup")
        service.active_notifiers = [mock_notifier1, mock_notifier2]

        await service.cleanup_all_notifiers()

        self.assertTrue(mock_notifier1.cleanup_called)
        self.assertTrue(mock_notifier2.cleanup_called)

if __name__ == '__main__':
    unittest.main()
