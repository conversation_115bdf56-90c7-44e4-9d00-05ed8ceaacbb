import importlib
import inspect
import logging
import os
from typing import Dict, Type, Any, Optional

from libs.plugin_interfaces.platform_adapter_v1 import PlatformAdapterV1
from app.config import settings, PlatformAdapterConfig # Import specific config models

logger = logging.getLogger(__name__)

def load_platform_adapters() -> Dict[str, PlatformAdapterV1]:
    """
    Loads platform adapter plugins based on configurations in settings.
    
    Uses settings.PLUGIN_DIR to find modules and settings.platform_adapters
    to determine which adapters to load and their specific configurations.
    
    Returns:
        A dictionary mapping platform_name to an instance of its adapter.
    """
    adapters: Dict[str, PlatformAdapterV1] = {}
    
    # Get base path for plugin modules (e.g., "plugins" for /app/plugins)
    # This is used to construct the full Python import path.
    plugin_module_base_import_path = settings.PLUGIN_DIR.replace("/", ".")
    
    # Get the absolute path to the directory to scan for .py files
    # This assumes plugin_loader.py is in 'app/' and PLUGIN_DIR is relative to 'app/' parent.
    # For Docker, if WORKDIR is /app and plugins are in /app/plugins, and this file is /app/app/plugin_loader.py
    # then settings.PLUGIN_DIR = "plugins" means the actual path is /app/plugins.
    current_script_dir = os.path.dirname(os.path.abspath(__file__)) # Should be /app/app
    # If PLUGIN_DIR is "plugins", it's a sibling to "app" directory that contains this loader.
    # The actual path to scan for .py files:
    actual_plugin_fs_dir = os.path.join(os.path.dirname(current_script_dir), settings.PLUGIN_DIR)

    if not os.path.isdir(actual_plugin_fs_dir):
        logger.error(f"Plugin directory '{actual_plugin_fs_dir}' not found or not a directory. Check PLUGIN_DIR setting.")
        return adapters

    for filename in os.listdir(actual_plugin_fs_dir):
        if filename.endswith(".py") and not filename.startswith("__init__"):
            module_name_file = filename[:-3] # e.g., "bilibili_adapter"
            # Construct full Python import path, e.g., "plugins.bilibili_adapter"
            full_module_import_path = f"{plugin_module_base_import_path}.{module_name_file}"
            
            try:
                module = importlib.import_module(full_module_import_path)
                for class_name_str, cls in inspect.getmembers(module, inspect.isclass):
                    if issubclass(cls, PlatformAdapterV1) and cls is not PlatformAdapterV1:
                        # Found a potential adapter class.
                        # Now, determine its intended platform_name to match with config.
                        # Adapters should ideally have a class attribute for platform_name,
                        # or we instantiate temporarily to get it from the instance.
                        # For now, let's assume platform_name is derived from module/class name convention
                        # or the adapter sets it in its __init__ based on a default.
                        
                        # Temporary instantiation to get platform_name (not ideal but works if adapter sets it in __init__)
                        try:
                            temp_instance = cls(config={}) # Minimal config to get platform_name
                            platform_key = temp_instance.platform_name 
                        except Exception as e:
                            logger.warning(f"Could not determine platform_name for class {class_name_str} in {full_module_import_path} without full config: {e}. Skipping.")
                            continue

                        # Check if this platform is configured in settings.platform_adapters
                        adapter_config_entry = settings.platform_adapters.get(platform_key)

                        if not adapter_config_entry:
                            logger.info(f"Adapter for platform '{platform_key}' (class {class_name_str}) found but not configured in settings.platform_adapters. Skipping.")
                            continue

                        if not adapter_config_entry.enabled:
                            logger.info(f"Adapter for platform '{platform_key}' (class {class_name_str}) is disabled in configuration. Skipping.")
                            continue
                        
                        # Instantiate with specific settings from the configuration
                        try:
                            specific_settings_dict = adapter_config_entry.specific_settings or {}
                            adapter_instance = cls(config=specific_settings_dict)
                            
                            if adapter_instance.platform_name in adapters: # Should match platform_key
                                logger.warning(
                                    f"Duplicate adapter for platform '{adapter_instance.platform_name}' "
                                    f"found via {full_module_import_path}. Keeping the first one loaded."
                                )
                            else:
                                adapters[adapter_instance.platform_name] = adapter_instance
                                logger.info(f"Loaded and configured platform adapter '{class_name_str}' for platform '{adapter_instance.platform_name}' from {full_module_import_path}")
                        except Exception as e:
                            logger.error(f"Failed to instantiate adapter '{class_name_str}' from {full_module_import_path} with specific_settings: {e}", exc_info=True)
                            
            except ImportError as e:
                logger.error(f"Failed to import plugin module {full_module_import_path}: {e}", exc_info=True)
            except Exception as e:
                logger.error(f"Unexpected error loading plugin from {full_module_import_path}: {e}", exc_info=True)
                
    if not adapters:
        logger.warning(f"No platform adapters were successfully loaded from '{actual_plugin_fs_dir}' based on current configurations.")
    return adapters
