"""
平台适配器基类
"""
from abc import abstractmethod
from typing import Dict, Any, Optional

import aiohttp

from app.interfaces.platform import IPlatform
from app.utils.logger import setup_logger
from app.utils.cache import async_cache
from app.utils.http_client import http_client

logger = setup_logger('platform.base')

class BasePlatform(IPlatform):
    """平台适配器基类，实现IPlatform接口"""

    def __init__(self, platform: str, name: str, api_url: str, room_url: str, headers: Dict[str, str]):
        """
        初始化平台适配器

        Args:
            platform: 平台标识
            name: 平台名称
            api_url: API URL模板
            room_url: 房间URL模板
            headers: 请求头
        """
        self._platform = platform
        self._name = name
        self.api_url = api_url
        self.room_url = room_url
        self.headers = headers
        self._initialized = False

    @property
    def platform(self) -> str:
        """
        获取平台标识名称

        Returns:
            str: 平台标识名称，如 'bilibili', 'douyu' 等
        """
        return self._platform

    @property
    def name(self) -> str:
        """
        获取平台显示名称

        Returns:
            str: 平台的中文显示名称，如 '哔哩哔哩', '斗鱼' 等
        """
        return self._name

    async def init(self) -> None:
        """初始化平台"""
        if self._initialized:
            return

        logger.info(f"初始化{self.name}平台")
        self._initialized = True

    async def cleanup(self) -> None:
        """清理资源"""
        if not self._initialized:
            return

        logger.info(f"清理{self.name}平台资源")
        self._initialized = False

    def get_platform_info(self) -> Dict[str, str]:
        """
        获取平台信息

        Returns:
            Dict[str, str]: 平台信息
        """
        return {
            'name': self.name,
            'platform': self.platform
        }

    @abstractmethod
    async def get_room_status(self, room_id: str) -> Optional[Dict[str, Any]]:
        """
        获取房间状态

        Args:
            room_id: 房间ID

        Returns:
            dict: 房间状态信息
            {
                'platform': str,
                'room_id': str,
                'anchor_name': str,
                'title': str,
                'live_status': bool,
                'url': str
            }
        """
        pass



    @async_cache(ttl=30)  # 缓存30秒
    async def fetch_json(self, url: str) -> Optional[Dict[str, Any]]:
        """
        获取JSON数据（带缓存）

        Args:
            url: 请求URL

        Returns:
            dict: JSON数据或包含错误信息的字典
        """
        try:
            # 打印请求头信息以便调试
            logger.debug(f"发送请求: {url}, 请求头: {self.headers}")

            # 使用HTTP客户端管理器获取会话
            session = await http_client.get_session()

            # 发送请求
            async with session.get(url, headers=self.headers, ssl=False) as response:
                # 打印响应头信息以便调试
                logger.debug(f"收到响应: {url}, 状态码: {response.status}, 响应头: {response.headers}")

                # 检查HTTP状态码
                if response.status != 200:
                    logger.error(f"请求失败: {url}, 状态码: {response.status}")
                    return {'error': f'请求失败: 状态码 {response.status}'}

                # 检查Content-Type
                content_type = response.headers.get('Content-Type', '').lower()

                # 尝试解析JSON，即使内容类型不是JSON
                try:
                    # 先尝试解析JSON，忽略Content-Type
                    return await response.json(content_type=None)
                except Exception as e:
                    # 如果JSON解析失败，检查内容类型
                    if 'application/json' not in content_type and 'text/json' not in content_type and 'application/javascript' not in content_type:
                        logger.warning(f"响应不是JSON格式: {url}, Content-Type: {content_type}")
                        # 如果是HTML，返回特殊标记
                        if 'text/html' in content_type:
                            return {'error': 'content_type_html', 'content_type': content_type}
                        return {'error': f'响应不是JSON格式: {content_type}'}

                    logger.error(f"JSON解析失败: {url}, 错误: {str(e)}")
                    return {'error': f'JSON解析失败: {str(e)}'}
        except Exception as e:
            logger.error(f"请求异常: {url}, 错误: {str(e)}")
            return {'error': f'请求异常: {str(e)}'}

    @async_cache(ttl=30)  # 缓存30秒
    async def fetch_html(self, url: str) -> Optional[str]:
        """
        获取HTML内容（带缓存）

        Args:
            url: 请求URL

        Returns:
            str: HTML内容
        """
        try:
            # 使用HTTP客户端管理器获取会话
            session = await http_client.get_session()

            # 发送请求
            async with session.get(url, headers=self.headers, ssl=False) as response:
                if response.status == 200:
                    return await response.text()
                else:
                    logger.error(f"请求失败: {url}, 状态码: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"请求异常: {url}, 错误: {str(e)}")
            return None
