import asyncio
import unittest
from unittest.mock import patch, AsyncMock, MagicMock
from typing import Dict, Any, Optional
from datetime import datetime, timezone
import httpx # For raising httpx.RequestError
import json # For json.JSONDecodeError

# Adjust the import path based on your project structure and PYTHONPATH
from services.monitoring_service.plugins.huya_adapter import HuyaAdapter

class TestHuyaAdapter(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        self.adapter_config = {
            "display_name": "Huya Test",
            # "api_url_template": "...", # Can override defaults if needed for tests
        }
        self.adapter = HuyaAdapter(config=self.adapter_config)
        
        self.mock_http_client_instance = AsyncMock(spec=httpx.AsyncClient)
        self.adapter.http_client = self.mock_http_client_instance

        self.mock_response = AsyncMock(spec=httpx.Response)
        self.mock_http_client_instance.get = AsyncMock(return_value=self.mock_response)

    async def test_init_and_close_adapter(self):
        adapter = HuyaAdapter(config=self.adapter_config) 
        self.assertIsNone(adapter.http_client)
        await adapter.init()
        self.assertIsNotNone(adapter.http_client)
        self.assertIsInstance(adapter.http_client, httpx.AsyncClient)
        
        real_client = adapter.http_client
        with patch.object(real_client, 'aclose', new_callable=AsyncMock) as mock_aclose:
            await adapter.close()
            mock_aclose.assert_called_once()
            self.assertIsNone(adapter.http_client)

    async def test_get_platform_display_name(self):
        self.assertEqual(self.adapter.get_platform_display_name(), "Huya Test")
        adapter_default_name = HuyaAdapter()
        self.assertEqual(adapter_default_name.get_platform_display_name(), "虎牙")

    async def test_get_streamer_status_live(self):
        room_id = "12345"
        mock_api_response = {
            "status": 200,
            "message": "Success",
            "data": {
                "profileInfo": {"nick": "TestAnchor", "avatar180": "http://example.com/avatar.jpg"},
                "liveData": {"introduction": "Live Title Test", "gameFullName": "Gaming"},
                "liveStatus": "ON" # or realLiveStatus: "ON"
            }
        }
        self.mock_response.status_code = 200
        self.mock_response.text = json.dumps(mock_api_response) # Simulate text response
        self.mock_response.json = MagicMock(return_value=mock_api_response) # For if it were called

        status = await self.adapter.get_streamer_status(room_id)

        self.assertIsNotNone(status)
        self.assertEqual(status["platform_name"], "huya")
        self.assertEqual(status["room_id"], room_id)
        self.assertEqual(status["anchor_name"], "TestAnchor")
        self.assertEqual(status["title"], "Live Title Test")
        self.assertTrue(status["live_status"])
        self.assertEqual(status["stream_url"], f"https://www.huya.com/{room_id}")
        self.assertEqual(status["cover_image_url"], "http://example.com/avatar.jpg")
        self.assertIsNone(status["viewer_count"]) # Not provided by this API endpoint
        self.assertIsNone(status["start_time"])   # Not reliably provided
        self.mock_http_client_instance.get.assert_called_once()

    async def test_get_streamer_status_offline(self):
        room_id = "67890"
        mock_api_response = {
            "status": 200,
            "message": "Success",
            "data": {
                "profileInfo": {"nick": "OfflineAnchor"},
                # liveData might be None or empty when offline
                "liveData": {"introduction": "Offline Title"}, 
                "liveStatus": "OFF"
            }
        }
        self.mock_response.status_code = 200
        self.mock_response.text = json.dumps(mock_api_response)
        self.mock_response.json.return_value = mock_api_response

        status = await self.adapter.get_streamer_status(room_id)

        self.assertIsNotNone(status)
        self.assertEqual(status["anchor_name"], "OfflineAnchor")
        self.assertEqual(status["title"], "Offline Title")
        self.assertFalse(status["live_status"])
        self.mock_http_client_instance.get.assert_called_once()

    async def test_get_streamer_status_api_error_status_not_200(self):
        room_id = "error_room_status"
        mock_api_response = {"status": 404, "message": "Room not found"}
        self.mock_response.status_code = 200 # HTTP call is OK
        self.mock_response.text = json.dumps(mock_api_response)
        self.mock_response.json.return_value = mock_api_response

        status = await self.adapter.get_streamer_status(room_id)
        self.assertIsNone(status)
        self.mock_http_client_instance.get.assert_called_once()

    async def test_get_streamer_status_api_no_data_field(self):
        room_id = "no_data_field"
        mock_api_response = {"status": 200, "message": "Success"} # Missing 'data' field
        self.mock_response.status_code = 200
        self.mock_response.text = json.dumps(mock_api_response)
        self.mock_response.json.return_value = mock_api_response

        status = await self.adapter.get_streamer_status(room_id)
        self.assertIsNone(status)
        self.mock_http_client_instance.get.assert_called_once()
        
    async def test_get_streamer_status_api_empty_data_field(self):
        room_id = "empty_data_field"
        mock_api_response = {"status": 200, "message": "Success", "data": None} # Empty 'data' field
        self.mock_response.status_code = 200
        self.mock_response.text = json.dumps(mock_api_response)
        self.mock_response.json.return_value = mock_api_response

        status = await self.adapter.get_streamer_status(room_id)
        self.assertIsNone(status)
        self.mock_http_client_instance.get.assert_called_once()


    async def test_get_streamer_status_http_error(self):
        room_id = "http_error_room"
        self.mock_http_client_instance.get.side_effect = httpx.HTTPStatusError(
            message="Simulated HTTP error", 
            request=MagicMock(spec=httpx.Request), 
            response=MagicMock(spec=httpx.Response, status_code=500, text="Server Error")
        )

        status = await self.adapter.get_streamer_status(room_id)
        self.assertIsNone(status)
        self.mock_http_client_instance.get.assert_called_once()

    async def test_get_streamer_status_request_error(self):
        room_id = "request_error_room"
        self.mock_http_client_instance.get.side_effect = httpx.RequestError(
            message="Simulated connection error", 
            request=MagicMock(spec=httpx.Request)
        )
        status = await self.adapter.get_streamer_status(room_id)
        self.assertIsNone(status)
        self.mock_http_client_instance.get.assert_called_once()

    async def test_get_streamer_status_json_decode_error_in_adapter(self):
        room_id = "json_error_room"
        self.mock_response.status_code = 200
        self.mock_response.text = "This is not valid JSON" 
        # json.loads(self.mock_response.text) will be called in the adapter
        
        status = await self.adapter.get_streamer_status(room_id)
        self.assertIsNone(status)
        self.mock_http_client_instance.get.assert_called_once()
        
    async def test_get_streamer_status_missing_profileInfo(self):
        room_id = "missing_profileInfo"
        mock_api_response = {
            "status": 200,
            "message": "Success",
            "data": {
                # profileInfo is missing
                "liveData": {"introduction": "Live Title Test"},
                "liveStatus": "ON"
            }
        }
        self.mock_response.status_code = 200
        self.mock_response.text = json.dumps(mock_api_response)
        self.mock_response.json.return_value = mock_api_response # If .json() was used

        status = await self.adapter.get_streamer_status(room_id)
        self.assertIsNotNone(status)
        self.assertEqual(status["anchor_name"], "Unknown Anchor (profileInfo missing)")
        self.assertTrue(status["live_status"])

if __name__ == "__main__":
    unittest.main()
