"""add_message_templates_table

Revision ID: 0002
Revises: 0001
Create Date: YYYY-MM-DD HH:MM:SS.MS+OFFSET # Replace with actual date if possible

"""
from alembic import op
import sqlalchemy as sa
# No specific dialect needed for this migration's operations beyond standard SQLAlchemy

# revision identifiers, used by Alembic.
revision = '0002'
down_revision = '0001' # Points to the previous migration
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('message_templates',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('template_content', sa.Text(), nullable=False),
    sa.Column('template_type', sa.String(length=50), nullable=False),
    sa.Column('user_id', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'name', 'template_type', name='uq_user_name_template_type')
    )
    op.create_index(op.f('ix_message_templates_id'), 'message_templates', ['id'], unique=False)
    op.create_index(op.f('ix_message_templates_template_type'), 'message_templates', ['template_type'], unique=False)
    op.create_index(op.f('ix_message_templates_user_id'), 'message_templates', ['user_id'], unique=False)
    op.create_index('ix_message_templates_user_id_template_type', 'message_templates', ['user_id', 'template_type'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_message_templates_user_id_template_type', table_name='message_templates')
    op.drop_index(op.f('ix_message_templates_user_id'), table_name='message_templates')
    op.drop_index(op.f('ix_message_templates_template_type'), table_name='message_templates')
    op.drop_index(op.f('ix_message_templates_id'), table_name='message_templates')
    op.drop_table('message_templates')
    # ### end Alembic commands ###
