import unittest
from unittest.mock import patch, AsyncMock, MagicMock
import httpx # For RequestError, and also for spec in mock_response
from fastapi import status
from typing import Optional, List, Dict, Any 
import json # For json.dumps in _configure_mock_response

from app.main import app, get_http_client 
from app.schemas import Subscription, SubscriptionCreate, SubscriptionSettings, \
                        NotificationChannel, NotificationChannelCreate, ErrorDetail, \
                        MessageTemplate, MessageTemplateCreate, MessageTemplateUpdate, TemplateTypeEnum
from app.config import settings 

# --- Test Setup ---
mock_async_client = AsyncMock(spec=httpx.AsyncClient)

async def override_get_http_client():
    return mock_async_client

app.dependency_overrides[get_http_client] = override_get_http_client

class TestAPIGateway(unittest.TestCase):

    def setUp(self):
        self.client = TestClient(app)
        mock_async_client.reset_mock(return_value=True, side_effect=None)
        
        self.mock_response = AsyncMock(spec=httpx.Response) # Use AsyncMock for response too
        self.mock_response.status_code = status.HTTP_200_OK
        self.mock_response.json = AsyncMock(return_value={}) # json() is an async method
        self.mock_response.text = "" # text is a property, can be simple string
        self.mock_response.content = b"" # content is bytes
        self.mock_response.headers = {'content-type': 'application/json'}
        mock_async_client.request = AsyncMock(return_value=self.mock_response)

    def _configure_mock_response(
        self, 
        status_code: int, 
        json_data: Optional[Any] = None, 
        text_data: Optional[str] = None,
        content_data: Optional[bytes] = None,
        headers: Optional[Dict[str,str]] = None,
        side_effect: Optional[Exception] = None # To simulate request errors
    ):
        if side_effect:
            mock_async_client.request.side_effect = side_effect
            return

        mock_async_client.request.side_effect = None # Clear any previous side effect
        self.mock_response.status_code = status_code
        
        actual_json_data = json_data
        actual_text_data = text_data
        actual_content_data = content_data

        if json_data is not None:
            if text_data is None:
                actual_text_data = json.dumps(json_data)
            if content_data is None and actual_text_data is not None:
                actual_content_data = actual_text_data.encode('utf-8')
        
        self.mock_response.json = AsyncMock(return_value=actual_json_data)
        self.mock_response.text = actual_text_data if actual_text_data is not None else ""
        self.mock_response.content = actual_content_data if actual_content_data is not None else b""
        self.mock_response.headers = headers or {'content-type': 'application/json' if json_data is not None else 'text/plain'}
        
        # Ensure .json() raises error if json_data is None and content_type is not application/json
        if json_data is None and (headers is None or headers.get('content-type') != 'application/json'):
            self.mock_response.json.side_effect = json.JSONDecodeError("No JSON data", "doc", 0)

        mock_async_client.request.return_value = self.mock_response


    # --- Health Check ---
    def test_health_check(self):
        response = self.client.get("/health")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json(), {"status": "ok", "service": "API Gateway V2"})

    # --- Subscription Endpoint Tests ---
    def test_create_user_subscription_success(self):
        user_id = "testuser1"
        sub_create_payload = {"user_id": user_id, "platform_name": "bilibili", "room_id": "123"}
        expected_response_data = {**sub_create_payload, "id": 1, "settings": {"notify_live_start": True, "notify_live_end": False, "notify_title_change": False}}
        self._configure_mock_response(status.HTTP_201_CREATED, expected_response_data)
        
        response = self.client.post(f"/api/v2/users/{user_id}/subscriptions", json=sub_create_payload)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.json(), expected_response_data)
        mock_async_client.request.assert_called_once()
        call_args = mock_async_client.request.call_args
        self.assertEqual(call_args.kwargs['method'], "POST")
        self.assertEqual(call_args.kwargs['url'], f"{settings.STORAGE_SERVICE_URL}/users/{user_id}/subscriptions")

    def test_create_user_subscription_mismatch_user_id(self):
        response = self.client.post(
            "/api/v2/users/testuser1/subscriptions",
            json={"user_id": "anotheruser", "platform_name": "bilibili", "room_id": "123"}
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        mock_async_client.request.assert_not_called()

    def test_read_user_subscriptions_success(self):
        user_id = "testuser2"
        expected_data = [{"id": 1, "user_id": user_id, "platform_name": "bilibili", "room_id": "101", "settings": {"notify_live_start": True, "notify_live_end": False, "notify_title_change": False}}]
        self._configure_mock_response(status.HTTP_200_OK, expected_data)

        response = self.client.get(f"/api/v2/users/{user_id}/subscriptions")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json(), expected_data)
        mock_async_client.request.assert_called_once_with(
            method="GET", url=f"{settings.STORAGE_SERVICE_URL}/users/{user_id}/subscriptions", 
            json=None, params=None, timeout=10.0
        )

    def test_delete_user_subscription_success(self):
        user_id = "testuser3"
        subscription_id = 1
        self._configure_mock_response(status.HTTP_204_NO_CONTENT, content_data=b'')

        response = self.client.delete(f"/api/v2/users/{user_id}/subscriptions/{subscription_id}")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        mock_async_client.request.assert_called_once_with(
            method="DELETE", url=f"{settings.STORAGE_SERVICE_URL}/users/{user_id}/subscriptions/{subscription_id}",
            json=None, params=None, timeout=10.0
        )

    def test_update_user_subscription_settings_success(self):
        user_id = "testuser4"
        subscription_id = 2
        settings_payload = {"notify_live_start": False, "notify_live_end": True, "notify_title_change": True}
        expected_response_data = {"id": subscription_id, "user_id": user_id, "platform_name": "douyu", "room_id": "202", "settings": settings_payload}
        self._configure_mock_response(status.HTTP_200_OK, expected_response_data)

        response = self.client.put(f"/api/v2/users/{user_id}/subscriptions/{subscription_id}", json=settings_payload)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json(), expected_response_data)

    # --- Notification Channel Endpoint Tests (Success paths) ---
    def test_set_user_notification_channel_success(self):
        user_id = "testuser5"
        channel_type = "wxpusher"
        channel_payload = {"user_id": user_id, "channel_type": channel_type, "channel_uid": "UID123"}
        expected_response_data = {**channel_payload, "id": 1}
        self._configure_mock_response(status.HTTP_200_OK, expected_response_data)

        response = self.client.put(f"/api/v2/users/{user_id}/notification_channels/{channel_type}", json=channel_payload)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json(), expected_response_data)

    def test_read_user_notification_channels_success(self):
        user_id = "testuser6"
        expected_data = [{"id": 1, "user_id": user_id, "channel_type": "wxpusher", "channel_uid": "UID_ABC"}]
        self._configure_mock_response(status.HTTP_200_OK, expected_data)
        response = self.client.get(f"/api/v2/users/{user_id}/notification_channels")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json(), expected_data)

    def test_read_user_notification_channel_specific_success(self):
        user_id = "testuser7"
        channel_type = "email"
        expected_data = {"id": 2, "user_id": user_id, "channel_type": channel_type, "channel_uid": "<EMAIL>"}
        self._configure_mock_response(status.HTTP_200_OK, expected_data)
        response = self.client.get(f"/api/v2/users/{user_id}/notification_channels/{channel_type}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json(), expected_data)


    # --- Message Template Endpoint Tests (Success paths already exist, adding error cases below) ---
    def test_create_message_template_success(self):
        # ... (existing test, ensure it uses _configure_mock_response)
        template_create_payload = {"name": "Test Template", "template_content": "Hello {{ name }}!", "template_type": "live_start", "user_id": "testuser"}
        expected_response_data = {**template_create_payload, "id": 1}
        self._configure_mock_response(status.HTTP_201_CREATED, expected_response_data)
        response = self.client.post("/api/v2/templates", json=template_create_payload)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.json(), expected_response_data)

    # --- Generic Downstream Error Handling Tests ---
    def test_downstream_404_returns_gateway_404(self):
        self._configure_mock_response(status.HTTP_404_NOT_FOUND, json_data={"detail": "Not found in storage"})
        response = self.client.get("/api/v2/templates/999") # Example endpoint
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn("downstream_response", response.json()["detail"])
        self.assertEqual(response.json()["detail"]["downstream_response"], {"detail": "Not found in storage"})

    def test_downstream_400_returns_gateway_400(self):
        payload = {"user_id": "bad_user", "platform_name": "", "room_id": "1"} # Invalid platform_name
        self._configure_mock_response(status.HTTP_400_BAD_REQUEST, json_data={"detail": "Validation error from storage"})
        response = self.client.post("/api/v2/users/bad_user/subscriptions", json=payload)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("downstream_response", response.json()["detail"])

    def test_downstream_422_returns_gateway_422(self):
        payload = {"name": "t", "template_content": "c", "template_type": "invalid_enum_value"} # Invalid enum
        self._configure_mock_response(status.HTTP_422_UNPROCESSABLE_ENTITY, json_data={"detail": "Storage validation error"})
        response = self.client.post("/api/v2/templates", json=payload)
        # The gateway's forward_request maps 4xx from downstream to the same 4xx.
        # If the gateway itself did validation before forwarding, it might return 422 directly.
        # Here, we test propagation of 422 from storage.
        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.assertIn("downstream_response", response.json()["detail"])


    def test_downstream_500_returns_gateway_502(self): # Or 500, depending on forward_request logic
        self._configure_mock_response(status.HTTP_500_INTERNAL_SERVER_ERROR, json_data={"detail": "Internal storage error"})
        response = self.client.get("/api/v2/templates")
        # Current forward_request logic maps 5xx from downstream to the same 5xx, if it's a valid HTTP error code.
        # If it's not a valid HTTP error code (e.g. 2xx but not expected), it becomes 502.
        # For a direct 500, it should be relayed as 500.
        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR) 
        self.assertIn("downstream_response", response.json()["detail"])

    def test_downstream_connect_error_returns_gateway_503(self):
        self._configure_mock_response(status_code=0, side_effect=httpx.ConnectError("Connection refused"))
        response = self.client.get("/api/v2/templates")
        self.assertEqual(response.status_code, status.HTTP_503_SERVICE_UNAVAILABLE)
        self.assertIn("Service unavailable or network error", response.json()["detail"])

    def test_downstream_timeout_returns_gateway_503(self): # httpx.TimeoutException is a subclass of RequestError
        self._configure_mock_response(status_code=0, side_effect=httpx.TimeoutException("Request timed out"))
        response = self.client.get("/api/v2/templates")
        self.assertEqual(response.status_code, status.HTTP_503_SERVICE_UNAVAILABLE)
        self.assertIn("Service unavailable or network error", response.json()["detail"])

    def test_downstream_invalid_json_response_returns_gateway_502(self):
        self._configure_mock_response(
            status.HTTP_200_OK, 
            json_data=None, # No valid JSON data
            text_data="This is not JSON", 
            headers={'content-type': 'text/plain'} # Simulate wrong content type
        )
        response = self.client.get("/api/v2/users/testuser_invalid_json/subscriptions") # Endpoint that expects JSON list
        self.assertEqual(response.status_code, status.HTTP_502_BAD_GATEWAY)
        self.assertIn("Invalid JSON response from downstream service", response.json()["detail"])
        
    def test_downstream_empty_response_for_200_ok_list_endpoint(self):
        # Test for an endpoint that is expected to return a list (e.g. List[Subscription])
        # but downstream returns 200 OK with empty/non-JSON content.
        # forward_request has logic for this: `return None if expected_status == status.HTTP_204_NO_CONTENT else (response.json() if response.content else None)`
        # If response.content is False (empty) and status is 200, response.json() would fail.
        # If response.json() fails, it raises an HTTPException(502).
        self._configure_mock_response(
            status.HTTP_200_OK, 
            json_data=None, # Simulate empty or non-JSON response
            text_data="",   # Empty text
            content_data=b"", # Empty content
            headers={'content-type': 'application/json'} # Type is JSON but content is empty
        )
        # This call to response.json() inside forward_request will fail if content is empty
        # and it's not a 204.
        response = self.client.get("/api/v2/users/some_user/subscriptions")
        self.assertEqual(response.status_code, status.HTTP_502_BAD_GATEWAY)
        self.assertIn("Invalid JSON response from downstream service", response.json()["detail"])


if __name__ == "__main__":
    unittest.main()
