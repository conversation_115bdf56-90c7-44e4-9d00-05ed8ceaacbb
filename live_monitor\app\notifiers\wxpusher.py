"""
WxPusher Notifier Implementation
"""
import json
import os
import aiohttp
from datetime import datetime
import pytz
from typing import Dict, List, Any, Optional

from app.notifications.base import BaseNotifier # New base class
from app.core.container import container
from app.constants import DEFAULT_SUBSCRIPTION_SETTINGS
from ..utils.logger import setup_logger # Adjusted import
from ..utils.helper import get_platform_name # Adjusted import

logger = setup_logger('notifier.wxpusher') # Changed logger name slightly for clarity

class AsyncWxPusher(BaseNotifier): # Inherits from BaseNotifier
    """
    基于WxPusher的异步通知器

    Conforms to BaseNotifier for sending direct messages.
    Includes legacy check_and_notify logic which will be refactored.
    """

    def __init__(self):
        """
        初始化WxPusher
        """
        self._initialized = False

        config = container.get('config')
        wxpusher_config = config.get('wxpusher', {})

        self.app_token = wxpusher_config.get('app_token', '')
        self.api_url = f"{wxpusher_config.get('base_url', 'https://wxpusher.zjiecode.com/api')}/send/message"
        self.cloudflare_url = wxpusher_config.get('cloudflare_worker_url')
        self.cloudflare_api_key = wxpusher_config.get('cloudflare_api_key')

        # 上次推送状态缓存 (related to check_and_notify, will be refactored)
        self.status_cache_file = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))), # Assuming this file is in app/notifiers/
            "data",
            "status_cache.json"
        )
        self.status_cache = {}
        self._ensure_data_dir()

    def _ensure_data_dir(self):
        """确保数据目录存在"""
        data_dir = os.path.dirname(self.status_cache_file)
        os.makedirs(data_dir, exist_ok=True)

    async def load_status_cache(self):
        """加载状态缓存"""
        try:
            if os.path.exists(self.status_cache_file):
                with open(self.status_cache_file, 'r', encoding='utf-8') as f:
                    self.status_cache = json.load(f)
            else:
                self.status_cache = {}
        except (FileNotFoundError, json.JSONDecodeError):
            self.status_cache = {}

    async def save_status_cache(self):
        """保存状态缓存"""
        with open(self.status_cache_file, 'w', encoding='utf-8') as f:
            json.dump(self.status_cache, f, ensure_ascii=False, indent=2)

    def get_cache_key(self, platform: str, room_id: str) -> str:
        """
        获取缓存键 (related to check_and_notify)
        """
        return f"{platform}_{room_id}"

    def format_live_message(self, streamer_info: Dict[str, Any], is_live: bool) -> str:
        """
        格式化直播消息 (related to check_and_notify)
        """
        platform_name = get_platform_name(streamer_info['platform'])

        if is_live:
            message = f"""【{platform_name}开播提醒】

🎮 主播：{streamer_info['anchor_name']}
🏠 房间号：{streamer_info['room_id']}
📺 标题：{streamer_info['title']}

🔴 直播已开始"""
        else:
            message = f"""【{platform_name}下播提醒】

🎮 主播：{streamer_info['anchor_name']}
🏠 房间号：{streamer_info['room_id']}
📺 标题：{streamer_info['title']}

⚪ 直播已结束"""
        return message

    async def check_and_notify(self, streamer_info: Dict[str, Any], uid: str) -> None:
        """
        检查状态变化并发送通知 (Legacy method, to be refactored)
        """
        try:
            storage = container.get('storage')
            user_subs = await storage.get_user_subscriptions(uid)
            sub_settings = None
            for sub in user_subs:
                if sub['platform'] == streamer_info['platform'] and sub['room_id'] == streamer_info['room_id']:
                    sub_settings = sub.get('settings', {})
                    break
            if not sub_settings:
                sub_settings = DEFAULT_SUBSCRIPTION_SETTINGS

            if sub_settings.get('quiet_hours', {}).get('enabled', False):
                beijing_tz = pytz.timezone('Asia/Shanghai')
                now = datetime.now(pytz.utc).astimezone(beijing_tz).time()
                start_time = datetime.strptime(sub_settings['quiet_hours']['start'], '%H:%M').time()
                end_time = datetime.strptime(sub_settings['quiet_hours']['end'], '%H:%M').time()
                local_time = datetime.now().time()
                logger.debug(f"当前服务器时间: {local_time}, 北京时间: {now}")
                is_quiet_time = False
                if start_time > end_time:
                    is_quiet_time = now >= start_time or now <= end_time
                else:
                    is_quiet_time = start_time <= now <= end_time
                if is_quiet_time:
                    logger.info(f"免打扰时段，跳过通知: {streamer_info['anchor_name']}")
                    return

            if not self.status_cache:
                await self.load_status_cache()

            cache_key = self.get_cache_key(streamer_info['platform'], streamer_info['room_id'])
            cached_info = self.status_cache.get(cache_key, {})
            previous_status = cached_info.get('live_status')
            previous_title = cached_info.get('title')
            current_status = streamer_info['live_status']
            current_title = streamer_info['title']

            self.status_cache[cache_key] = {
                'live_status': current_status,
                'title': current_title,
                'last_update': datetime.now().isoformat()
            }
            await self.save_status_cache()

            if previous_status is None or previous_title is None:
                logger.info(f"首次检查，记录状态: {streamer_info['anchor_name']} - 当前状态: {'开播' if current_status else '未开播'}")
                return

            # Send actual message using the new `send` method for decoupling (though check_and_notify still orchestrates)
            send_details = {'uids': [uid]}

            if previous_status != current_status:
                if current_status and sub_settings.get('notify_live_start', True):
                    msg_body = self.format_live_message(streamer_info, current_status)
                    # Using title and body for the send method
                    await self.send(title=f"{get_platform_name(streamer_info['platform'])}开播提醒", body=msg_body, details=send_details)
                    print(f"主播 {streamer_info['anchor_name']} 已开播") # Keep console print for now
                    logger.info(f"开播通知已发送 (via send method): {streamer_info['anchor_name']}")
                elif not current_status and sub_settings.get('notify_live_end', True):
                    msg_body = self.format_live_message(streamer_info, current_status)
                    await self.send(title=f"{get_platform_name(streamer_info['platform'])}下播提醒", body=msg_body, details=send_details)
                    print(f"主播 {streamer_info['anchor_name']} 已下播") # Keep console print for now
                    logger.info(f"下播通知已发送 (via send method): {streamer_info['anchor_name']}")
                else:
                    status_text = '开播' if current_status else '下播'
                    logger.info(f"由于用户设置，跳过{status_text}通知: {streamer_info['anchor_name']}")

            elif previous_title != current_title and previous_title != "" and current_title != "":
                if sub_settings.get('notify_title', True):
                    status_text = "直播中" if current_status else "未直播"
                    platform_name = get_platform_name(streamer_info['platform'])
                    title_update_body = f"""🏠 房间号：{streamer_info['room_id']}
📺 新标题：{current_title}
💡 状态：{status_text}"""
                    await self.send(title=f"【{platform_name}】{streamer_info['anchor_name']}更改标题", body=title_update_body, details=send_details)
                    print(f"主播 {streamer_info['anchor_name']} 更新标题") # Keep console print for now
                    logger.info(f"标题更新通知已发送 (via send method): {streamer_info['anchor_name']} - {current_title}")
                else:
                    logger.info(f"由于用户设置，跳过标题更新通知: {streamer_info['anchor_name']} - {current_title}")

        except Exception as e:
            logger.error(f"检查状态并发送通知时出错: {str(e)}")

    async def send(self, title: str, body: str, details: Optional[Dict] = None) -> None:
        """
        Sends a notification using WxPusher.

        Args:
            title: The title of the notification.
            body: The main content of the notification.
            details: An optional dictionary for notifier-specific parameters.
                     Expected to contain 'uids': List[str] for WxPusher.
        """
        if details is None:
            details = {}
        uids = details.get('uids')
        if not uids:
            logger.warning("WxPusher: No UIDs provided to send method. Notification not sent.")
            return

        # Construct content for WxPusher. Here, we use title + body.
        # WxPusher itself doesn't have a separate "title" field in the same way some systems do.
        # The content is typically HTML or Markdown. For simplicity, we combine them.
        # For more complex formatting, `body` could be pre-formatted HTML.
        content = f"{title}\n\n{body}"

        try:
            data = {
                "appToken": self.app_token,
                "content": content,
                "contentType": 1,  # 1 for text, 2 for HTML, 3 for Markdown
                "uids": uids,
                "verifyPay": False # As per original
            }

            if self.cloudflare_url and self.cloudflare_api_key:
                data["apiKey"] = self.cloudflare_api_key

            logger.debug(f"WxPusher: Preparing to send notification. AppToken: {self.app_token[:5]}..., UIDs: {uids}")

            async with aiohttp.ClientSession() as session:
                target_url = self.cloudflare_url if self.cloudflare_url else self.api_url
                if self.cloudflare_url:
                    logger.debug(f"WxPusher: Using Cloudflare Worker relay: {target_url}")
                
                async with session.post(target_url, json=data, timeout=10) as response:
                    response_text = await response.text() # Read text first for better error logging
                    try:
                        result = json.loads(response_text)
                    except json.JSONDecodeError:
                        logger.error(f"WxPusher: Failed to decode JSON response. Status: {response.status}, Response: {response_text}")
                        return # Exit, as we can't process this response

                logger.debug(f"WxPusher: API Response: {result}")

                success_code = 1000 # WxPusher success code
                # Check if the response structure is as expected and contains the 'code'
                if isinstance(result, dict) and 'code' in result and result['code'] == success_code:
                    logger.info(f"WxPusher: Message sent successfully to UIDs: {uids}. Title: {title}")
                else:
                    # Log detailed error if code is not 1000 or response structure is unexpected
                    error_detail = result.get('msg', 'No error message provided by API.') if isinstance(result, dict) else response_text
                    logger.error(f"WxPusher: Failed to send message. API Response Code: {result.get('code', 'N/A')}, Message: {error_detail}, UIDs: {uids}, Title: {title}")

        except aiohttp.ClientError as e: # More specific exception for network/HTTP issues
            logger.error(f"WxPusher: HTTP client error sending message: {str(e)}. UIDs: {uids}, Title: {title}")
        except Exception as e:
            error_msg = str(e) if str(e) else "Unknown error"
            logger.error(f"WxPusher: Generic error sending message: {error_msg}. UIDs: {uids}, Title: {title}")
            # Not re-raising an exception as per current instructions, just logging.

    async def init(self) -> None:
        """初始化通知器"""
        if self._initialized:
            return
        logger.info("WxPusher Notifier: Initializing")
        await self.load_status_cache() # Still needed for check_and_notify
        self._initialized = True

    async def cleanup(self) -> None:
        """清理资源"""
        if not self._initialized:
            return
        logger.info("WxPusher Notifier: Cleaning up")
        # await self.save_status_cache() # Save cache on cleanup if check_and_notify is still active
        self._initialized = False
