import httpx
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timezone # For start_time

from libs.plugin_interfaces.platform_adapter_v1 import PlatformAdapterV1

logger = logging.getLogger(__name__)

# Default Douyu API URLs (can be overridden by config passed to __init__)
DEFAULT_DOUYU_API_URL = "https://open.douyucdn.cn/api/RoomApi/room/{}"
DEFAULT_DOUYU_ROOM_URL_TEMPLATE = "https://www.douyu.com/{}"
DEFAULT_DOUYU_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}

class DouyuAdapter(PlatformAdapterV1):
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(platform_name="douyu", config=config)
        self.api_url_template = self.config.get("api_url_template", DEFAULT_DOUYU_API_URL)
        self.room_url_template = self.config.get("room_url_template", DEFAULT_DOUYU_ROOM_URL_TEMPLATE)
        self.headers = self.config.get("headers", DEFAULT_DOUYU_HEADERS)
        self.http_client: Optional[httpx.AsyncClient] = None
        self.display_name = self.config.get("display_name", "斗鱼") # Or "Douyu"

    async def init(self) -> None:
        """Initialize the HTTP client."""
        if self.http_client is None:
            self.http_client = httpx.AsyncClient(headers=self.headers, timeout=10.0)
        logger.info(f"DouyuAdapter for '{self.platform_name}' initialized.")

    async def _fetch_json(self, url: str) -> Optional[Dict[str, Any]]:
        if not self.http_client:
            await self.init()
        
        if not self.http_client:
            logger.error("HTTP client not initialized in DouyuAdapter.")
            return None
            
        try:
            response = await self.http_client.get(url)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error fetching {url}: {e.response.status_code} - {e.response.text}")
        except httpx.RequestError as e:
            logger.error(f"Request error fetching {url}: {e}")
        except Exception as e: # Includes JSONDecodeError
            logger.error(f"Generic error fetching or parsing JSON from {url}: {e}")
        return None

    async def get_streamer_status(self, room_id: str) -> Optional[Dict[str, Any]]:
        """Fetch Douyu streamer status."""
        api_url = self.api_url_template.format(room_id)
        data = await self._fetch_json(api_url)

        if not data or data.get("error") != 0:
            error_msg = data.get("data", "Failed to fetch room data or error in response.") if data else "No data from API."
            logger.warning(f"Douyu: Could not get room info for room_id {room_id}. Message: {error_msg}")
            return None

        room_info = data.get("data", {})
        
        is_live = room_info.get("room_status") == "1" # "1" for live, "2" for offline
        
        # Douyu API provides 'start_time' but it's a string like "16xxxxxxx" (unix timestamp as string)
        # It's present even when offline, representing last live time or something else.
        # We should only consider it valid if is_live is True.
        start_time_obj: Optional[datetime] = None
        if is_live:
            start_time_str = room_info.get("start_time")
            if start_time_str:
                try:
                    start_time_unix = int(start_time_str)
                    start_time_obj = datetime.fromtimestamp(start_time_unix, tz=timezone.utc)
                except ValueError:
                    logger.warning(f"Douyu: Could not parse start_time '{start_time_str}' for room {room_id}.")


        status_dict: Dict[str, Any] = {
            "platform_name": self.platform_name,
            "room_id": str(room_id),
            "anchor_name": room_info.get("owner_name", "Unknown Anchor"),
            "title": room_info.get("room_name", "Unknown Title"),
            "live_status": is_live,
            "stream_url": self.room_url_template.format(room_id),
            "cover_image_url": room_info.get("room_thumb"), # Douyu uses 'room_thumb'
            "viewer_count": room_info.get("online_num") or room_info.get("hn"), # 'online_num' or 'hn' for popularity/viewers
            "start_time": start_time_obj,
        }
        return status_dict

    def get_platform_display_name(self) -> str:
        """Return a user-friendly display name for the platform."""
        return self.display_name

    async def close(self) -> None:
        """Close the HTTP client."""
        if self.http_client:
            await self.http_client.aclose()
            self.http_client = None
        logger.info(f"DouyuAdapter for '{self.platform_name}' closed.")
