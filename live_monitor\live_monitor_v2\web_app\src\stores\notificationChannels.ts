import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { NotificationChannel, NotificationChannelCreate } from '@/types';

// Helper function to make API calls (copied from subscriptions.ts)
// TODO: Consider refactoring apiFetch into a shared utility
async function apiFetch(url: string, options: RequestInit = {}) {
  const defaultHeaders = {
    'Content-Type': 'application/json',
    // Add other default headers if needed, e.g., Authorization
  };
  options.headers = { ...defaultHeaders, ...options.headers };

  const response = await fetch(url, options);
  if (!response.ok) {
    let errorData;
    try {
      errorData = await response.json();
    } catch (e) {
      // If response is not JSON, use statusText
      errorData = { detail: response.statusText };
    }
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }
  if (response.status === 204) { // No Content
    return null;
  }
  return response.json();
}

export const useNotificationChannelStore = defineStore('notificationChannels', () => {
  // --- State ---
  const channels = ref<NotificationChannel[]>([]);
  const isLoading = ref<boolean>(false);
  const error = ref<string | null>(null);

  // --- Actions ---

  /**
   * Fetches all notification channels for a given user.
   * @param userId - The ID of the user.
   */
  async function fetchChannels(userId: string) {
    if (!userId || userId.trim() === "") {
      error.value = 'User ID is required to fetch notification channels.';
      channels.value = []; // Clear existing channels
      return;
    }
    isLoading.value = true;
    error.value = null;
    try {
      // Assuming API endpoint structure /api/v2/users/{user_id}/notification_channels
      const data = await apiFetch(`/api/v2/users/${userId}/notification_channels`);
      channels.value = data as NotificationChannel[];
    } catch (e: any) {
      error.value = e.message || 'Failed to fetch notification channels.';
      channels.value = []; // Clear channels on error
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Adds or updates a notification channel for a user.
   * The backend is expected to handle create or update logic (e.g., via PUT).
   * @param userId - The ID of the user.
   * @param channelType - The type of the channel (e.g., 'email', 'webhook').
   * @param channelUid - The unique identifier for the channel (e.g., email address, webhook URL).
   */
  async function setChannel(userId: string, channelType: string, channelUid: string) {
    if (!userId || !channelType || !channelUid) {
      error.value = 'User ID, Channel Type, and Channel UID are required.';
      return;
    }
    isLoading.value = true;
    error.value = null;

    const payload: NotificationChannelCreate = { channel_uid: channelUid };

    try {
      // API endpoint: /api/v2/users/{user_id}/notification_channels/{channel_type}
      // Using PUT for idempotent create/update behavior
      await apiFetch(`/api/v2/users/${userId}/notification_channels/${channelType}`, {
        method: 'PUT',
        body: JSON.stringify(payload),
      });
      // After successfully setting a channel, refresh the list to get the latest state
      // or update the local list directly if the PUT request returns the updated/created channel.
      // For simplicity, re-fetching is safer to ensure data consistency.
      await fetchChannels(userId);
    } catch (e: any) {
      error.value = e.message || `Failed to set notification channel (${channelType}).`;
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Deletes a notification channel for a user.
   * @param userId - The ID of the user.
   * @param channelType - The type of the channel to delete.
   */
  async function deleteChannel(userId: string, channelType: string) {
    if (!userId || !channelType) {
      error.value = 'User ID and Channel Type are required to delete a channel.';
      return;
    }
    isLoading.value = true;
    error.value = null;
    try {
      await apiFetch(`/api/v2/users/${userId}/notification_channels/${channelType}`, {
        method: 'DELETE',
      });
      // Remove the channel from the local list or re-fetch.
      // Re-fetching ensures consistency.
      await fetchChannels(userId);
    } catch (e: any) {
      error.value = e.message || `Failed to delete notification channel (${channelType}).`;
    } finally {
      isLoading.value = false;
    }
  }

  return {
    channels,
    isLoading,
    error,
    fetchChannels,
    setChannel,
    deleteChannel,
  };
});
