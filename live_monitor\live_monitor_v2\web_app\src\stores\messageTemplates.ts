import { defineStore } from 'pinia';
import { ref } from 'vue';
import type {
  MessageTemplate,
  MessageTemplateCreatePayload,
  MessageTemplateUpdatePayload,
  TemplateTypeEnum
} from '@/types';

// TODO: Consider refactoring apiFetch into a shared utility if not already done.
async function apiFetch(url: string, options: RequestInit = {}) {
  const defaultHeaders = {
    'Content-Type': 'application/json',
    // Add other default headers if needed, e.g., Authorization
  };
  options.headers = { ...defaultHeaders, ...options.headers };

  const response = await fetch(url, options);
  if (!response.ok) {
    let errorData;
    try {
      errorData = await response.json();
    } catch (e) {
      errorData = { detail: response.statusText };
    }
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }
  if (response.status === 204) { // No Content
    return null;
  }
  return response.json();
}

export const useMessageTemplateStore = defineStore('messageTemplates', () => {
  // --- State ---
  const templates = ref<MessageTemplate[]>([]);
  const isLoading = ref<boolean>(false);
  const error = ref<string | null>(null);

  // --- Actions ---

  /**
   * Fetches message templates, optionally filtered by user ID and/or template type.
   * @param userId - Optional ID of the user to filter templates for.
   * @param templateType - Optional template type to filter by.
   */
  async function fetchTemplates(userId?: string, templateType?: TemplateTypeEnum) {
    isLoading.value = true;
    error.value = null;
    let url = '/api/v2/message_templates/';
    const queryParams = new URLSearchParams();
    if (userId) {
      queryParams.append('user_id', userId);
    }
    if (templateType) {
      queryParams.append('template_type', templateType);
    }
    if (queryParams.toString()) {
      url += `?${queryParams.toString()}`;
    }

    try {
      const data = await apiFetch(url);
      templates.value = data as MessageTemplate[];
    } catch (e: any) {
      error.value = e.message || 'Failed to fetch message templates.';
      // Consider not clearing templates on error if displaying stale data is preferred
      // templates.value = [];
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Fetches both user-specific and global templates, then combines and de-duplicates them.
   * @param userId - The ID of the user for whom to fetch specific templates.
   * @param templateType - Optional template type to filter by for both user and global templates.
   */
  async function fetchUserAndGlobalTemplates(userId: string, templateType?: TemplateTypeEnum) {
    isLoading.value = true;
    error.value = null;

    const userTemplateParams = new URLSearchParams();
    userTemplateParams.append('user_id', userId);
    if (templateType) {
      userTemplateParams.append('template_type', templateType);
    }
    const userTemplatesUrl = `/api/v2/message_templates/?${userTemplateParams.toString()}`;

    const globalTemplateParams = new URLSearchParams();
    // To fetch global templates, we assume not passing user_id or passing a specific indicator like user_id=null
    // For this implementation, we'll fetch without user_id and then rely on templates having user_id === null
    // Or, an API might support a query like `is_global=true` or `scope=global`
    if (templateType) {
      globalTemplateParams.append('template_type', templateType);
    }
    // This URL will fetch all templates of a type (if specified), including global ones.
    // We will filter client-side for user_id === null for truly global ones.
    // Or, if API returns only non-user-specific ones when user_id is omitted, that's also fine.
    const globalTemplatesUrl = `/api/v2/message_templates/?${globalTemplateParams.toString()}`;

    try {
      const [userTemplatesData, allAccessibleTemplatesData] = await Promise.all([
        apiFetch(userTemplatesUrl),
        apiFetch(globalTemplatesUrl)
      ]);

      const combinedTemplates = new Map<number, MessageTemplate>();

      // Add user-specific templates
      (userTemplatesData as MessageTemplate[]).forEach(template => {
        if (template.user_id === userId) { // Ensure they are indeed user-specific
             combinedTemplates.set(template.id, template);
        }
      });

      // Add global templates (user_id is null or is_default is true)
      // and any other templates accessible that are not the current user's (if admin role, for example)
      // but primarily focusing on global ones here.
      (allAccessibleTemplatesData as MessageTemplate[]).forEach(template => {
        if (template.user_id === null || template.is_default) { // Definition of a global/default template
            if (!combinedTemplates.has(template.id)) { // Avoid overriding user's specific if ID somehow clashed and user one was fetched first
                combinedTemplates.set(template.id, template);
            }
        }
      });

      templates.value = Array.from(combinedTemplates.values());

    } catch (e: any) {
      error.value = e.message || 'Failed to fetch user and global message templates.';
      // Consider not clearing templates on error if displaying stale data is preferred
      // templates.value = [];
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Creates a new message template.
   * @param templateData - The data for the new template.
   */
  async function createTemplate(templateData: MessageTemplateCreatePayload) {
    isLoading.value = true;
    error.value = null;
    try {
      const newTemplate = await apiFetch('/api/v2/message_templates/', {
        method: 'POST',
        body: JSON.stringify(templateData),
      });
      // Add to local state or re-fetch. Re-fetching based on context (e.g., current filters) might be best.
      // For simplicity here, we add it if no user_id or template_type filter was implied for the current list.
      // A more robust solution might involve fetchTemplates with the original filter criteria.
      templates.value.push(newTemplate as MessageTemplate);
      // Or, more robustly:
      // await fetchTemplates(templateData.user_id || undefined, templateData.template_type);
      return newTemplate as MessageTemplate;
    } catch (e: any) {
      error.value = e.message || 'Failed to create message template.';
      throw e; // Re-throw to allow components to handle it
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Updates an existing message template.
   * @param templateId - The ID of the template to update.
   * @param templateData - The data to update the template with.
   */
  async function updateTemplate(templateId: number, templateData: MessageTemplateUpdatePayload) {
    isLoading.value = true;
    error.value = null;
    try {
      const updatedTemplate = await apiFetch(`/api/v2/message_templates/${templateId}`, {
        method: 'PUT',
        body: JSON.stringify(templateData),
      });
      // Update local state or re-fetch
      const index = templates.value.findIndex(t => t.id === templateId);
      if (index !== -1) {
        templates.value[index] = { ...templates.value[index], ...updatedTemplate } as MessageTemplate;
      }
      // Or, more robustly:
      // await fetchTemplates(userIdFromSomewhere, templateTypeFromSomewhere);
      return updatedTemplate as MessageTemplate;
    } catch (e: any) {
      error.value = e.message || `Failed to update message template ${templateId}.`;
      throw e; // Re-throw
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Deletes a message template.
   * @param templateId - The ID of the template to delete.
   */
  async function deleteTemplate(templateId: number) {
    isLoading.value = true;
    error.value = null;
    try {
      await apiFetch(`/api/v2/message_templates/${templateId}`, {
        method: 'DELETE',
      });
      // Remove from local state or re-fetch
      templates.value = templates.value.filter(t => t.id !== templateId);
      // Or, more robustly:
      // await fetchTemplates(userIdFromSomewhere, templateTypeFromSomewhere);
    } catch (e: any) {
      error.value = e.message || `Failed to delete message template ${templateId}.`;
      throw e; // Re-throw
    } finally {
      isLoading.value = false;
    }
  }

  return {
    templates,
    isLoading,
    error,
    fetchTemplates,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    fetchUserAndGlobalTemplates,
  };
});
