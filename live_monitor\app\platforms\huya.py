"""
虎牙平台适配器 - 异步版本
"""
from typing import Dict, Any, Optional

from app.utils.logger import setup_logger
from app.utils.helper import async_retry
from app.core.container import container
from .base import BasePlatform

logger = setup_logger('platform.huya')

class HuyaPlatform(BasePlatform):
    """
    虎牙直播平台适配器

    负责与虎牙直播API交互，获取主播直播状态、标题等信息
    """

    def __init__(self):
        """初始化虎牙平台适配器"""
        # 从容器中获取配置
        config = container.get('config')
        platforms_config = config.get('platforms', {})
        huya_config = platforms_config.get('huya', {})

        super().__init__(
            platform="huya",
            name=platforms_config.get('names', {}).get('huya', '虎牙'),
            api_url=huya_config.get('api_url', 'https://mp.huya.com/cache.php?m=Live&do=profileRoom&roomid={}'),
            room_url=huya_config.get('room_url', 'https://www.huya.com/{}'),
            headers=huya_config.get('headers', {'User-Agent': 'Mozilla/5.0', 'Referer': 'https://www.huya.com/'})
        )

    @async_retry(max_retries=3, retry_interval=1.0)
    async def get_room_status(self, room_id: str) -> Optional[Dict[str, Any]]:
        """
        获取房间状态

        Args:
            room_id: 房间ID

        Returns:
            dict: 房间状态信息
            {
                'platform': str,
                'room_id': str,
                'anchor_name': str,
                'title': str,
                'live_status': bool,
                'url': str
            }
        """
        try:
            # 获取房间信息
            api_url = self.api_url.format(room_id)
            # 虎牙API返回的Content-Type是text/html，但内容是JSON格式，需要忽略Content-Type
            data = await self.fetch_json(api_url)

            if data and data.get('status') == 200 and 'data' in data and data['data']:
                room_info = data['data']

                # 获取主播信息
                profile_info = room_info.get('profileInfo', {})
                live_data = room_info.get('liveData', {})

                # 判断直播状态 - 虎牙API返回的是字符串 "ON" 或 "OFF"
                live_status = room_info.get('realLiveStatus', room_info.get('liveStatus', 'OFF'))
                is_live = live_status == "ON"

                return {
                    'platform': self.platform,
                    'room_id': str(room_id),
                    'anchor_name': profile_info.get('nick', '未知主播'),
                    'title': live_data.get('introduction', ''),
                    'live_status': is_live,
                    'url': self.room_url.format(room_id)
                }
            else:
                error_msg = data.get('message', '未知错误') if data else '请求失败'
                logger.error(f"获取房间信息失败: {error_msg}")
                return None

        except Exception as e:
            logger.error(f"请求虎牙API异常: {str(e)}")
            raise

# 注意: 不再创建全局实例
# 而是在需要时通过 get_platform 函数获取实例
