# 系统配置
system:
  host: 0.0.0.0
  port: 58551
  data_dir: data
  debug: false

# 监控配置
monitor:
  check_interval: 10  # 检查间隔（秒）
  retry_times: 3      # 重试次数
  retry_interval: 1   # 重试间隔（秒）
  max_concurrent_tasks: 20  # 最大并发任务数
  batch_size: 5       # 每批处理的主播数量
  batch_interval: 0.5 # 批次间隔（秒）

# 缓存配置
cache:
  enabled: true       # 是否启用缓存
  ttl: 30             # 缓存生存时间（秒）
  max_size: 1000      # 缓存最大条目数

# Notification Service Configuration
notifications:
  notifiers:
    - type: wxpusher  # Corresponds to module app.notifiers.wxpusher and infers AsyncWxPusher class
      enabled: true
      # class_name: AsyncWxPusher # This can be omitted if NotificationService correctly infers it.
                                # Let's omit it to rely on inference.
      # No specific 'config' sub-block needed here for wxpusher, as AsyncWxPusher
      # currently loads its settings from the top-level 'wxpusher:' block.

# WxPusher配置 (This is the existing top-level block, ensure it remains)
wxpusher:
  app_token: AT_xxxxxxxxxxxxxxxxxxxxxxxxxxxxx  # 替换为你的WxPusher应用Token
  base_url: https://wxpusher.zjiecode.com/api
  # Cloudflare Worker配置（如果部署环境无法访问 WxPusher API可设置此项）
  # cloudflare_worker_url: https://your-worker.your-subdomain.workers.dev  # 请替换为你实际部署的 Cloudflare Worker URL
  # cloudflare_api_key: your_api_key  # 请替换为你在 Worker 中设置的密钥

# 异步HTTP客户端配置
http_client:
  timeout: 10  # 请求超时时间（秒）
  max_connections: 100  # 最大连接数
  connection_timeout: 30  # 连接超时时间（秒）

# 平台配置
platforms:
  # 平台名称显示
  names:
    bilibili: 哔哩哔哩
    douyu: 斗鱼
    huya: 虎牙

  # B站配置
  bilibili:
    api_url: https://api.live.bilibili.com/room/v1/Room/get_info?room_id={}
    user_api_url: https://api.live.bilibili.com/live_user/v1/Master/info?uid={}
    room_url: https://live.bilibili.com/{}
    headers:
      User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
      Referer: https://live.bilibili.com/
      Accept: application/json, text/plain, */*
      Accept-Language: zh-CN,zh;q=0.9,en;q=0.8
      Origin: https://live.bilibili.com

  # 斗鱼配置
  douyu:
    api_url: https://open.douyucdn.cn/api/RoomApi/room/{}
    room_url: https://www.douyu.com/{}
    headers:
      User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36

  # 虎牙配置
  huya:
    # 注意：虎牙API返回的Content-Type是text/html，但内容是JSON格式
    # 在BasePlatform类中使用content_type=None参数忽略Content-Type头
    api_url: https://mp.huya.com/cache.php?m=Live&do=profileRoom&roomid={}
    room_url: https://www.huya.com/{}
    headers:
      User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
      Referer: https://www.huya.com/
