"""
通知接口定义
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional


class INotifier(ABC):
    """通知接口"""

    @abstractmethod
    async def init(self) -> None:
        """初始化通知器"""
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        """清理资源"""
        pass

    @abstractmethod
    async def send_message(self, message: str, uids: List[str]) -> bool:
        """
        发送消息

        Args:
            message: 消息内容
            uids: 接收者ID列表

        Returns:
            bool: 是否发送成功
        """
        pass

    @abstractmethod
    async def check_and_notify(self, status: Dict[str, Any], uid: str) -> None:
        """
        检查状态变化并发送通知

        Args:
            status: 状态信息
            uid: 用户ID
        """
        pass

    @abstractmethod
    def format_live_message(self, streamer_info: Dict[str, Any], is_live: bool) -> str:
        """
        格式化直播状态消息

        Args:
            streamer_info: 主播信息
            is_live: 是否在直播

        Returns:
            str: 格式化后的消息
        """
        pass
