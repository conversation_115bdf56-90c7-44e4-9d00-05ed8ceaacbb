version: '3.8'

services:
  postgres_db:
    image: postgres:15-alpine
    container_name: lmv2_postgres
    environment:
      POSTGRES_USER: lmv2_user
      POSTGRES_PASSWORD: lmv2_password
      POSTGRES_DB: lmv2_db
    ports:
      - "5432:5432" 
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    networks:
      - live_monitor_v2_network

  redis_cache:
    image: redis:7-alpine
    container_name: lmv2_redis
    ports:
      - "6379:6379" 
    networks:
      - live_monitor_v2_network

  message_broker: 
    image: rabbitmq:3.12-management-alpine
    container_name: lmv2_rabbitmq
    ports:
      - "5672:5672"   
      - "15672:15672" 
    environment:
      RABBITMQ_DEFAULT_USER: lmv2_user
      RABBITMQ_DEFAULT_PASS: lmv2_password
    networks:
      - live_monitor_v2_network

  storage_service:
    build: ../../services/storage_service 
    container_name: lmv2_storage_service
    ports:
      - "8000:8000"
    depends_on:
      - postgres_db
    networks:
      - live_monitor_v2_network
    command: sh -c "alembic upgrade head && uvicorn app.main:app --host 0.0.0.0 --port 8000"

  api_gateway_service:
    build: ../../services/api_gateway
    container_name: lmv2_api_gateway_service
    ports:
      - "8080:8080"
    environment:
      - STORAGE_SERVICE_URL=http://storage_service:8000
    depends_on:
      - storage_service
    networks:
      - live_monitor_v2_network

  monitoring_service:
    build: ../../services/monitoring_service
    container_name: lmv2_monitoring_service
    environment:
      - STORAGE_SERVICE_URL=http://storage_service:8000
      - MESSAGE_BROKER_URL=amqp://lmv2_user:lmv2_password@message_broker:5672/
      - MONITOR_INTERVAL_SECONDS=30 
      - PYTHONUNBUFFERED=1 
    depends_on:
      - storage_service
      - message_broker
    networks:
      - live_monitor_v2_network

  notification_service:
    build: ../../services/notification_service
    container_name: lmv2_notification_service
    environment:
      - STORAGE_SERVICE_URL=http://storage_service:8000
      - MESSAGE_BROKER_URL=amqp://lmv2_user:lmv2_password@message_broker:5672/
      - LIVE_MONITOR_WXPUSHER_APP_TOKEN=YOUR_ACTUAL_WXPUSHER_APP_TOKEN_HERE 
      - PYTHONUNBUFFERED=1
    depends_on:
      - storage_service
      - message_broker
    networks:
      - live_monitor_v2_network

  web_app_service:
    build:
      context: ../../web_app # Path to the web_app directory
      dockerfile: Dockerfile
    container_name: lmv2_web_app
    ports:
      - "8081:80" # Expose Nginx (serving the Vue app) on host port 8081
    depends_on:
      - api_gateway_service # Ensures API gateway is started before web app
    networks:
      - live_monitor_v2_network
    # Optional: Add environment variables if the Nginx container or entrypoint needs them
    # environment:
    #   API_BASE_URL: http://api_gateway_service:8080 # Example

volumes:
  postgres_data:

networks:
  live_monitor_v2_network:
    driver: bridge
