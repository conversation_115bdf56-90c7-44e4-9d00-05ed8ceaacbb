"""
平台适配器模块初始化
"""
from .bilibili import BilibiliPlatform
from .douyu import DouyuPlatform
from .huya import HuyaPlatform

# 平台映射
PLATFORMS = {
    'bilibili': BilibiliPlatform,
    'douyu': DouyuPlatform,
    'huya': HuyaPlatform
}

# 平台实例缓存
_platform_instances = {}

def get_platform(platform_name):
    """
    获取平台适配器实例

    使用单例模式，确保每个平台只创建一个实例

    Args:
        platform_name: 平台名称

    Returns:
        平台适配器实例

    Raises:
        ValueError: 如果平台不受支持
    """
    if platform_name not in _platform_instances:
        if platform_name in PLATFORMS:
            _platform_instances[platform_name] = PLATFORMS[platform_name]()
        else:
            raise ValueError(f"不支持的平台: {platform_name}")
    return _platform_instances[platform_name]
