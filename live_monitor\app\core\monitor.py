"""
监控模块 - 异步版本
"""
import asyncio
import time
import random
from typing import Dict, List, Any, Set, Tuple, TYPE_CHECKING

import pytz
from datetime import datetime

from app.interfaces.monitor import IMonitor
from app.interfaces.storage import IStorage
# from app.interfaces.notifier import INotifier # No longer directly used
from app.utils.logger import setup_logger
from app.platforms import PLATFORMS
from app.core.container import container
from app.utils.helper import get_platform_name
from app.constants import DEFAULT_SUBSCRIPTION_SETTINGS

if TYPE_CHECKING:
    from app.core.notification_service import NotificationService

logger = setup_logger('core.monitor')

class AsyncMonitor(IMonitor):
    """异步监控类，实现IMonitor接口"""

    def __init__(self, storage: IStorage, notification_service: 'NotificationService', check_interval: int = None):
        """
        初始化监控器

        Args:
            storage: 存储实例
            notification_service: 通知服务实例
            check_interval: 检查间隔（秒）
        """
        config = container.get('config')
        monitor_config = config.get('monitor', {})

        self.storage = storage
        self.notification_service = notification_service # Changed from self.notifier
        self.check_interval = check_interval or monitor_config.get('check_interval', 10)
        self.running = False
        self.task = None
        self.monitor_status_cache: Dict[str, Dict[str, Any]] = {} # New cache

        self.max_concurrent_tasks = monitor_config.get('max_concurrent_tasks', 20)
        self.semaphore = asyncio.Semaphore(self.max_concurrent_tasks)

        self.batch_size = monitor_config.get('batch_size', 5)
        self.batch_interval = monitor_config.get('batch_interval', 0.5)

        self._initialized = False

    async def start(self) -> None:
        """启动监控"""
        if self.running:
            logger.warning("监控器已经在运行")
            return

        self.running = True
        self.task = asyncio.create_task(self._monitor_loop())
        # 使用print而非日志，直接输出到终端
        print(f"已启动主播监控器，检查间隔: {self.check_interval} 秒")

    async def stop(self) -> None:
        """停止监控"""
        if not self.running:
            logger.warning("监控器未在运行")
            return

        self.running = False
        if self.task:
            self.task.cancel()
            try:
                await self.task
            except asyncio.CancelledError:
                pass
            print("监控器已停止") # Kept original print statement

    async def _monitor_loop(self) -> None:
        """监控循环"""
        while self.running:
            try:
                start_time = time.time()
                streamer_count = await self._check_all_streamers()

                # 计算下一次检查的等待时间
                elapsed = time.time() - start_time
                wait_time = max(0, self.check_interval - elapsed)

                if streamer_count > 0:
                    # Kept original print statement, can be configured via logger if needed
                    print(f"已检查 {streamer_count} 个主播，耗时 {elapsed:.2f} 秒") 
                logger.debug(f"完成一轮检查，耗时 {elapsed:.2f} 秒，等待 {wait_time:.2f} 秒后进行下一轮")
                await asyncio.sleep(wait_time)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"监控过程出错: {str(e)}")
                await asyncio.sleep(5)  # 出错后等待一段时间再重试

    async def _check_all_streamers(self) -> int:
        """
        检查所有订阅的主播状态

        Returns:
            int: 检查的主播数量
        """
        try:
            # 加载所有订阅
            subscriptions = await self.storage.get_all_subscriptions()

            # 收集所有需要检查的主播，避免重复检查
            streamers_to_check: Set[Tuple[str, str]] = set()
            for user_subs in subscriptions.values():
                for sub in user_subs:
                    streamers_to_check.add((sub['platform'], sub['room_id']))

            streamer_count = len(streamers_to_check)
            logger.debug(f"本轮需要检查 {streamer_count} 个主播")

            # 将主播列表转换为列表并打乱顺序，避免总是按相同顺序检查
            streamers_list = list(streamers_to_check)
            random.shuffle(streamers_list)

            # 批量处理主播检查
            for i in range(0, len(streamers_list), self.batch_size):
                batch = streamers_list[i:i+self.batch_size]

                # 创建当前批次的任务
                tasks = []
                for platform_name, room_id in batch:
                    tasks.append(self._check_streamer(platform_name, room_id, subscriptions))
                if tasks:
                    await asyncio.gather(*tasks)
                if i + self.batch_size < len(streamers_list):
                    await asyncio.sleep(self.batch_interval)
            return streamer_count
        except Exception as e:
            logger.error(f"检查主播状态时出错: {str(e)}")
            return 0

    def _format_live_message_content(self, streamer_info: Dict[str, Any], is_live: bool) -> Tuple[str, str]:
        platform_display_name = get_platform_name(streamer_info['platform'])
        anchor_name = streamer_info['anchor_name']
        room_id = streamer_info['room_id']
        title = streamer_info['title']

        if is_live:
            event_type = "开播提醒"
            status_emoji = "🔴"
            status_text = "直播已开始"
        else:
            event_type = "下播提醒"
            status_emoji = "⚪"
            status_text = "直播已结束"

        notification_title = f"【{platform_display_name}{event_type}】{anchor_name}"
        notification_body = f"""🎮 主播：{anchor_name}
🏠 房间号：{room_id}
📺 标题：{title}

{status_emoji} {status_text}"""
        return notification_title, notification_body

    def _format_title_change_content(self, streamer_info: Dict[str, Any]) -> Tuple[str, str]:
        platform_display_name = get_platform_name(streamer_info['platform'])
        anchor_name = streamer_info['anchor_name']
        room_id = streamer_info['room_id']
        new_title = streamer_info['title']
        live_status_text = "直播中" if streamer_info['live_status'] else "未直播"

        notification_title = f"【{platform_display_name}】{anchor_name} 更改标题"
        notification_body = f"""🏠 房间号：{room_id}
📺 新标题：{new_title}
💡 状态：{live_status_text}"""
        return notification_title, notification_body

    async def _process_streamer_status_and_notify(self, platform_name: str, room_id: str,
                                                 current_status_info: Dict[str, Any],
                                                 relevant_subscriptions: List[Dict[str, Any]]):
        cache_key = f"{platform_name}_{room_id}"
        previous_cached_status = self.monitor_status_cache.get(cache_key, {})
        prev_live = previous_cached_status.get('live_status')
        prev_title = previous_cached_status.get('title')

        current_live = current_status_info['live_status']
        current_title = current_status_info['title']

        current_time_iso = datetime.now(pytz.utc).isoformat()

        if prev_live is None: # First check for this streamer in this monitor session
            self.monitor_status_cache[cache_key] = {
                'live_status': current_live, 
                'title': current_title, 
                'last_update': current_time_iso
            }
            logger.info(f"首次检查 {platform_name} {room_id} ({current_status_info['anchor_name']})，记录状态，不发送通知。当前状态: {'开播' if current_live else '未开播'}")
            return

        # Update cache regardless of notification sending
        self.monitor_status_cache[cache_key] = {
            'live_status': current_live, 
            'title': current_title, 
            'last_update': current_time_iso
        }

        for sub_info in relevant_subscriptions:
            uid = sub_info['uid']
            # Ensure sub_info itself contains 'settings', not nested deeper
            sub_settings = sub_info.get('settings', DEFAULT_SUBSCRIPTION_SETTINGS.copy())


            # Quiet Hours Check
            if sub_settings.get('quiet_hours', {}).get('enabled', False):
                beijing_tz = pytz.timezone('Asia/Shanghai')
                now_beijing = datetime.now(pytz.utc).astimezone(beijing_tz).time()
                start_str = sub_settings['quiet_hours']['start']
                end_str = sub_settings['quiet_hours']['end']
                
                try:
                    start_time = datetime.strptime(start_str, '%H:%M').time()
                    end_time = datetime.strptime(end_str, '%H:%M').time()
                except ValueError:
                    logger.error(f"无效的免打扰时间格式 for UID {uid}: start='{start_str}', end='{end_str}'. Skipping quiet hours check.")
                else:
                    is_quiet_time = False
                    if start_time > end_time:  # Spans midnight
                        is_quiet_time = now_beijing >= start_time or now_beijing <= end_time
                    else:
                        is_quiet_time = start_time <= now_beijing <= end_time
                    
                    if is_quiet_time:
                        logger.info(f"免打扰时段 for UID {uid}，跳过主播 {current_status_info['anchor_name']} 的通知")
                        continue # Skip to next subscriber

            # Live Status Change Check
            if prev_live != current_live:
                if current_live and sub_settings.get('notify_live_start', True):
                    title, body = self._format_live_message_content(current_status_info, True)
                    logger.info(f"发送开播通知: {current_status_info['anchor_name']} to UID {uid}")
                    await self.notification_service.dispatch_notification(title, body, details={'uids': [uid]})
                elif not current_live and sub_settings.get('notify_live_end', True):
                    title, body = self._format_live_message_content(current_status_info, False)
                    logger.info(f"发送下播通知: {current_status_info['anchor_name']} to UID {uid}")
                    await self.notification_service.dispatch_notification(title, body, details={'uids': [uid]})
            # Title Change Check (only if live status hasn't changed, and titles are different)
            elif prev_title != current_title and sub_settings.get('notify_title', True):
                # Also ensure titles are not empty to avoid notifications on initial empty titles
                if prev_title or current_title: # Check if at least one title is non-empty
                    title, body = self._format_title_change_content(current_status_info)
                    logger.info(f"发送标题更改通知: {current_status_info['anchor_name']} to UID {uid}. Old: '{prev_title}', New: '{current_title}'")
                    await self.notification_service.dispatch_notification(title, body, details={'uids': [uid]})


    async def _check_streamer(self, platform_name: str, room_id: str,
                             all_subscriptions: Dict[str, List[Dict[str, Any]]]) -> None:
        async with self.semaphore:
            try:
                platform_getter = PLATFORMS.get(platform_name)
                if not platform_getter:
                    logger.warning(f"未知平台: {platform_name}")
                    return
                platform = platform_getter()
                current_status_info = await platform.get_room_status(room_id)

                if not current_status_info:
                    logger.warning(f"获取主播状态失败: {platform_name} {room_id}")
                    return

                relevant_subscriptions: List[Dict[str, Any]] = []
                for uid, user_subs_list in all_subscriptions.items():
                    for sub_details in user_subs_list:
                        if sub_details['platform'] == platform_name and sub_details['room_id'] == room_id:
                            # Pass the whole sub_details dict, which includes settings, and add uid
                            subscription_data_for_notification = sub_details.copy()
                            subscription_data_for_notification['uid'] = uid
                            relevant_subscriptions.append(subscription_data_for_notification)
                            break # Found subscription for this user, move to next user
                
                if relevant_subscriptions:
                    await self._process_streamer_status_and_notify(
                        platform_name, room_id, current_status_info, relevant_subscriptions
                    )

            except Exception as e:
                logger.error(f"检查主播 {platform_name} {room_id} 状态时出错: {str(e)}")

    async def init(self) -> None:
        if self._initialized:
            return
        logger.info("初始化监控器")
        await self.storage.init()
        await self.notification_service.init_all_notifiers() # Changed
        self._initialized = True

    async def cleanup(self) -> None:
        if not self._initialized:
            return
        if self.running:
            await self.stop()
        logger.info("清理监控器资源")
        await self.storage.cleanup()
        await self.notification_service.cleanup_all_notifiers() # Changed
        self._initialized = False
