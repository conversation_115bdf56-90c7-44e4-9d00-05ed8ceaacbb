"""
HTTP客户端管理模块
"""
import aiohttp
import asyncio
from typing import Optional, Dict, Any

from app.core.container import container
from .logger import setup_logger

logger = setup_logger('utils.http_client')

class HttpClientManager:
    """HTTP客户端管理器，提供会话复用功能"""
    
    def __init__(self):
        """初始化HTTP客户端管理器"""
        self._session: Optional[aiohttp.ClientSession] = None
        self._lock = asyncio.Lock()
        self._initialized = False
        
    async def get_session(self) -> aiohttp.ClientSession:
        """
        获取或创建一个ClientSession
        
        Returns:
            aiohttp.ClientSession: 会话对象
        """
        async with self._lock:
            if self._session is None or self._session.closed:
                # 从配置中获取HTTP客户端设置
                config = container.get('config')
                http_config = config.get('http_client', {})
                timeout = aiohttp.ClientTimeout(
                    total=http_config.get('timeout', 10),
                    connect=http_config.get('connection_timeout', 30)
                )
                
                # 创建新会话
                self._session = aiohttp.ClientSession(timeout=timeout)
                logger.debug("创建新的HTTP会话")
            
            return self._session
    
    async def fetch_json(self, url: str, headers: Dict[str, str] = None, 
                         params: Dict[str, Any] = None, ssl: bool = False) -> Dict[str, Any]:
        """
        获取JSON数据
        
        Args:
            url: 请求URL
            headers: 请求头
            params: 查询参数
            ssl: 是否验证SSL证书
            
        Returns:
            Dict[str, Any]: JSON数据
            
        Raises:
            aiohttp.ClientError: 如果请求失败
        """
        session = await self.get_session()
        try:
            logger.debug(f"发送请求: {url}")
            async with session.get(url, headers=headers, params=params, ssl=ssl) as response:
                if response.status != 200:
                    logger.error(f"请求失败: {url}, 状态码: {response.status}")
                    return {'error': f'请求失败: 状态码 {response.status}'}
                
                return await response.json()
        except Exception as e:
            logger.error(f"请求异常: {url}, 错误: {str(e)}")
            return {'error': f'请求异常: {str(e)}'}
    
    async def fetch_text(self, url: str, headers: Dict[str, str] = None, 
                         params: Dict[str, Any] = None, ssl: bool = False) -> str:
        """
        获取文本数据
        
        Args:
            url: 请求URL
            headers: 请求头
            params: 查询参数
            ssl: 是否验证SSL证书
            
        Returns:
            str: 文本数据
            
        Raises:
            aiohttp.ClientError: 如果请求失败
        """
        session = await self.get_session()
        try:
            logger.debug(f"发送请求: {url}")
            async with session.get(url, headers=headers, params=params, ssl=ssl) as response:
                if response.status != 200:
                    logger.error(f"请求失败: {url}, 状态码: {response.status}")
                    return f'请求失败: 状态码 {response.status}'
                
                return await response.text()
        except Exception as e:
            logger.error(f"请求异常: {url}, 错误: {str(e)}")
            return f'请求异常: {str(e)}'
    
    async def init(self) -> None:
        """初始化HTTP客户端管理器"""
        if self._initialized:
            return
        
        logger.info("初始化HTTP客户端管理器")
        self._initialized = True
    
    async def cleanup(self) -> None:
        """清理资源"""
        if not self._initialized:
            return
        
        logger.info("清理HTTP客户端管理器资源")
        if self._session and not self._session.closed:
            await self._session.close()
            self._session = None
        
        self._initialized = False

# 创建全局实例
http_client = HttpClientManager()
