from pydantic import BaseModel, Field, ConfigDict
from typing import Dict, Any, Optional
from datetime import datetime, timezone

class StreamerInfo(BaseModel):
    platform_name: str # Technical name, e.g., "bilibili"
    platform_display_name: Optional[str] = None # User-friendly, e.g., "哔哩哔哩"
    room_id: str
    anchor_name: str
    title: str
    live_status: bool
    stream_url: str # URL to the stream itself, or the room page
    cover_image_url: Optional[str] = None
    viewer_count: Optional[int] = None
    start_time: Optional[datetime] = None # UTC datetime if live

    model_config = ConfigDict(from_attributes=True) # Pydantic V2 compatibility

class LiveStatusChangedEvent(BaseModel):
    event_type: str # e.g., "live_start", "live_end", "title_change"
    user_id: str 
    streamer_info: StreamerInfo
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    model_config = ConfigDict(from_attributes=True) # Pydantic V2 compatibility
