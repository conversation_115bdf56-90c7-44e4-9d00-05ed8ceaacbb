// 主要的JavaScript功能
document.addEventListener('DOMContentLoaded', function() {
    // 获取URL参数中的uid
    const urlParams = new URLSearchParams(window.location.search);
    const uid = urlParams.get('uid');

    if (!uid) {
        showNotification('请通过正确的链接访问页面', 'error');
        return;
    }

    // 初始化筛选和排序
    initFilters();

    // 加载用户订阅的主播列表
    loadSubscriptions(uid);

    // 添加订阅表单提交处理
    const addForm = document.getElementById('add-subscription-form');
    if (addForm) {
        addForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const platform = document.getElementById('platform').value;
            const roomId = document.getElementById('room-id').value;

            if (!platform) {
                showNotification('请选择直播平台', 'warning');
                return;
            }

            if (!roomId) {
                showNotification('请输入房间号', 'warning');
                return;
            }

            addSubscription(uid, platform, roomId);
        });
    }
});

// 移除了主题切换功能

// 初始化筛选和排序
function initFilters() {
    const statusFilter = document.getElementById('status-filter');
    const platformFilter = document.getElementById('platform-filter');
    const sortBy = document.getElementById('sort-by');

    // 添加事件监听器
    statusFilter.addEventListener('change', applyFilters);
    platformFilter.addEventListener('change', applyFilters);
    sortBy.addEventListener('change', applyFilters);
}

// 移除了刷新按钮和倒计时功能

// 全局变量存储所有主播数据
let allStreamers = [];

// 加载订阅列表
async function loadSubscriptions(uid) {
    try {
        // 显示加载状态
        const grid = document.querySelector('.streamer-grid');
        grid.innerHTML = `
            <div class="loading">
                <div class="loading-spinner"></div>
                <p>正在加载主播数据...</p>
            </div>
        `;

        const response = await fetch(`/api/subscriptions?uid=${uid}`);
        const data = await response.json();

        if (data.success) {
            // 存储所有主播数据
            allStreamers = data.subscriptions;

            // 更新统计信息
            updateStats(allStreamers);

            // 应用筛选器并渲染主播
            applyFilters();
        } else {
            showNotification(data.message || '加载失败', 'error');
            grid.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-exclamation-circle"></i>
                    <p>加载失败，请刷新重试</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('加载订阅失败:', error);
        showNotification('加载订阅失败，请稍后重试', 'error');

        const grid = document.querySelector('.streamer-grid');
        grid.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-exclamation-circle"></i>
                <p>加载失败，请刷新重试</p>
            </div>
        `;
    }
}

// 更新统计信息
function updateStats(streamers) {
    const totalStreamersElement = document.getElementById('total-streamers');
    const liveStreamersElement = document.getElementById('live-streamers');

    const totalCount = streamers.length;
    const liveCount = streamers.filter(streamer => streamer.live_status).length;

    totalStreamersElement.textContent = totalCount;
    liveStreamersElement.textContent = liveCount;
}

// 应用筛选和排序
function applyFilters() {
    const statusFilter = document.getElementById('status-filter').value;
    const platformFilter = document.getElementById('platform-filter').value;
    const sortBy = document.getElementById('sort-by').value;

    // 筛选主播
    let filteredStreamers = allStreamers.filter(streamer => {
        // 状态筛选
        if (statusFilter === 'live' && !streamer.live_status) return false;
        if (statusFilter === 'offline' && streamer.live_status) return false;

        // 平台筛选
        if (platformFilter !== 'all' && streamer.platform !== platformFilter) return false;

        return true;
    });

    // 排序主播
    filteredStreamers.sort((a, b) => {
        if (sortBy === 'status') {
            // 先按直播状态排序（直播中的在前）
            if (a.live_status !== b.live_status) {
                return b.live_status ? 1 : -1;
            }
        } else if (sortBy === 'name') {
            // 按主播名称排序
            return a.anchor_name.localeCompare(b.anchor_name, 'zh-CN');
        } else if (sortBy === 'platform') {
            // 按平台排序
            return a.platform.localeCompare(b.platform);
        }

        // 默认按直播状态排序
        return 0;
    });

    // 渲染筛选和排序后的主播
    renderStreamers(filteredStreamers);
}

// 渲染主播卡片
function renderStreamers(streamers) {
    const grid = document.querySelector('.streamer-grid');
    grid.innerHTML = ''; // 清空现有内容

    if (streamers.length === 0) {
        grid.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-search"></i>
                <p>没有找到符合条件的主播</p>
            </div>
        `;
        return;
    }

    streamers.forEach(streamer => {
        const card = createStreamerCard(streamer);
        grid.appendChild(card);
    });
}

// 创建主播卡片
function createStreamerCard(streamer) {
    const card = document.createElement('div');
    card.className = streamer.live_status ? 'streamer-card live' : 'streamer-card';
    card.dataset.platform = streamer.platform;
    card.dataset.status = streamer.live_status ? 'live' : 'offline';

    // 获取平台中文名称
    const platformNames = {
        'bilibili': '哔哩哔哩',
        'douyu': '斗鱼',
        'huya': '虎牙'
    };

    const platformName = platformNames[streamer.platform] || streamer.platform;

    card.innerHTML = `
        <div class="streamer-info">
            <div class="streamer-header">
                <h3 class="streamer-name">${streamer.anchor_name}</h3>
                <div class="streamer-platform">${platformName}</div>
            </div>

            <div class="streamer-status">
                <span class="status-indicator ${streamer.live_status ? 'live' : ''}"></span>
                <span class="status-text ${streamer.live_status ? 'live' : ''}">${streamer.live_status ? '直播中' : '未开播'}</span>
            </div>

            <p class="streamer-title">${streamer.title || '暂无标题'}</p>
        </div>

        <div class="card-divider"></div>

        <div class="card-buttons">
            <button class="button secondary" onclick="showSettings('${streamer.room_id}', '${streamer.platform}', '${streamer.anchor_name}')">
                <i class="fas fa-cog"></i> 设置
            </button>
            <button class="button danger" onclick="showUnsubscribeConfirm('${streamer.room_id}', '${streamer.platform}', '${streamer.anchor_name}')">
                <i class="fas fa-trash-alt"></i> 取消订阅
            </button>
            <a href="${streamer.url}" target="_blank" class="button primary">
                <i class="fas fa-${streamer.live_status ? 'play' : 'home'}"></i> ${streamer.live_status ? '观看直播' : '查看主页'}
            </a>
        </div>
    `;

    return card;
}

// 显示取消订阅确认对话框
function showUnsubscribeConfirm(roomId, platform, anchorName) {
    // 创建对话框
    const overlay = document.createElement('div');
    overlay.className = 'dialog-overlay';

    const dialog = document.createElement('div');
    dialog.className = 'dialog';

    dialog.innerHTML = `
        <h3 class="dialog-title">确认取消订阅</h3>
        <p class="dialog-message">确定要取消订阅 <strong>${anchorName}</strong> 的直播间吗？取消订阅后将不再收到该主播的直播通知。</p>
        <div class="dialog-buttons">
            <button class="button secondary" onclick="closeDialog(this)">
                <i class="fas fa-times"></i> 取消
            </button>
            <button class="button danger" onclick="confirmUnsubscribe('${roomId}', '${platform}', this)">
                <i class="fas fa-check"></i> 确认取消
            </button>
        </div>
    `;

    overlay.appendChild(dialog);
    document.body.appendChild(overlay);

    // 点击遮罩层关闭对话框
    overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
            closeDialog(overlay);
        }
    });

    // 添加ESC键关闭对话框
    const escHandler = (e) => {
        if (e.key === 'Escape') {
            closeDialog(overlay);
            document.removeEventListener('keydown', escHandler);
        }
    };
    document.addEventListener('keydown', escHandler);
}

// 关闭对话框
function closeDialog(element) {
    const overlay = element.closest('.dialog-overlay');
    if (overlay) {
        // 添加消失动画
        overlay.style.opacity = '0';
        setTimeout(() => {
            overlay.remove();
        }, 300);
    }
}

// 确认取消订阅
async function confirmUnsubscribe(roomId, platform, element) {
    const overlay = element.closest('.dialog-overlay');
    if (overlay) {
        closeDialog(overlay);
        await unsubscribe(roomId, platform);
    }
}

// 添加订阅
async function addSubscription(uid, platform, roomId) {
    try {
        // 清除表单内容
        const platformSelect = document.getElementById('platform');
        const roomIdInput = document.getElementById('room-id');

        // 显示加载状态
        const submitButton = document.querySelector('#add-subscription-form button[type="submit"]');
        const originalText = submitButton.innerHTML;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 添加中...';
        submitButton.disabled = true;

        const response = await fetch('/api/subscribe', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ uid, platform, room_id: roomId })
        });

        const data = await response.json();

        if (data.success) {
            showNotification('订阅成功', 'success');
            loadSubscriptions(uid); // 重新加载列表

            // 清除表单内容
            platformSelect.value = '';
            roomIdInput.value = '';
        } else {
            showNotification(data.message || '订阅失败', 'error');
        }

        // 恢复按钮状态
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;
    } catch (error) {
        console.error('添加订阅失败:', error);
        showNotification('添加订阅失败，请稍后重试', 'error');

        // 恢复按钮状态
        const submitButton = document.querySelector('#add-subscription-form button[type="submit"]');
        submitButton.innerHTML = '<i class="fas fa-plus"></i> 添加订阅';
        submitButton.disabled = false;
    }
}

// 取消订阅
async function unsubscribe(roomId, platform) {
    const uid = new URLSearchParams(window.location.search).get('uid');

    try {
        const response = await fetch('/api/unsubscribe', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ uid, platform, room_id: roomId })
        });

        const data = await response.json();

        if (data.success) {
            showNotification('取消订阅成功', 'success');
            loadSubscriptions(uid); // 重新加载列表
        } else {
            showNotification(data.message || '取消订阅失败', 'error');
        }
    } catch (error) {
        console.error('取消订阅失败:', error);
        showNotification('取消订阅失败，请稍后重试', 'error');
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    // 移除现有的通知
    const existingNotifications = document.querySelectorAll('.notification:not(#notification-template)');
    existingNotifications.forEach(notification => {
        notification.remove();
    });

    // 克隆通知模板
    const template = document.getElementById('notification-template');
    const notification = template.cloneNode(true);
    notification.removeAttribute('id');
    notification.style.display = 'flex';

    // 设置图标和颜色
    const iconElement = notification.querySelector('.notification-icon i');

    if (type === 'success') {
        iconElement.className = 'fas fa-check-circle';
        notification.style.borderLeftColor = 'var(--success-color)';
        notification.querySelector('.notification-icon').style.color = 'var(--success-color)';
        notification.querySelector('.notification-icon').style.background = 'rgba(76, 201, 240, 0.1)';
    } else if (type === 'error') {
        iconElement.className = 'fas fa-exclamation-circle';
        notification.style.borderLeftColor = 'var(--danger-color)';
        notification.querySelector('.notification-icon').style.color = 'var(--danger-color)';
        notification.querySelector('.notification-icon').style.background = 'rgba(230, 57, 70, 0.1)';
    } else if (type === 'warning') {
        iconElement.className = 'fas fa-exclamation-triangle';
        notification.style.borderLeftColor = 'var(--warning-color)';
        notification.querySelector('.notification-icon').style.color = 'var(--warning-color)';
        notification.querySelector('.notification-icon').style.background = 'rgba(247, 37, 133, 0.1)';
    }

    // 设置消息内容
    notification.querySelector('.notification-message').textContent = message;

    // 添加到文档
    document.body.appendChild(notification);

    // 添加关闭按钮事件
    notification.querySelector('.notification-close').addEventListener('click', () => {
        notification.style.transform = 'translateX(100%)';
        notification.style.opacity = '0';
        setTimeout(() => {
            notification.remove();
        }, 300);
    });

    // 5秒后自动关闭
    setTimeout(() => {
        if (document.body.contains(notification)) {
            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    notification.remove();
                }
            }, 300);
        }
    }, 5000);
}

// 显示设置对话框
async function showSettings(roomId, platform, anchorName) {
    const uid = new URLSearchParams(window.location.search).get('uid');

    try {
        // 获取用户订阅列表
        const response = await fetch(`/api/subscriptions?uid=${uid}`);
        const data = await response.json();

        if (!data.success) {
            showNotification('获取设置失败', 'error');
            return;
        }

        // 查找当前主播的订阅信息
        let subscription = null;
        for (const sub of data.subscriptions) {
            if (sub.platform === platform && sub.room_id === roomId) {
                subscription = sub;
                break;
            }
        }

        if (!subscription) {
            showNotification('未找到该主播的订阅信息', 'error');
            return;
        }

        // 获取设置，如果没有则使用默认设置
        const settings = subscription.settings || {
            notify_live_start: true,  // 开播通知
            notify_live_end: true,    // 下播通知
            notify_title: true,       // 标题更新通知
            quiet_hours: {
                enabled: false,
                start: '23:00',
                end: '07:00'
            }
        };

        // 创建对话框
        const overlay = document.createElement('div');
        overlay.className = 'dialog-overlay';

        const dialog = document.createElement('div');
        dialog.className = 'dialog settings-dialog';

        // 获取平台中文名称
        const platformNames = {
            'bilibili': '哔哩哔哩',
            'douyu': '斗鱼',
            'huya': '虎牙'
        };

        const platformName = platformNames[platform] || platform;

        dialog.innerHTML = `
            <h3 class="dialog-title">
                <i class="fas fa-bell"></i> ${anchorName} 的通知设置
                <div class="dialog-subtitle">${platformName} · 房间号: ${roomId}</div>
            </h3>

            <div class="settings-form">
                <div class="setting-item">
                    <label class="switch">
                        <input type="checkbox" id="notify-live-start" ${settings.notify_live_start ? 'checked' : ''}>
                        <span class="slider"></span>
                    </label>
                    <span class="setting-label">开播通知</span>
                </div>

                <div class="setting-item">
                    <label class="switch">
                        <input type="checkbox" id="notify-live-end" ${settings.notify_live_end ? 'checked' : ''}>
                        <span class="slider"></span>
                    </label>
                    <span class="setting-label">下播通知</span>
                </div>

                <div class="setting-item">
                    <label class="switch">
                        <input type="checkbox" id="notify-title" ${settings.notify_title ? 'checked' : ''}>
                        <span class="slider"></span>
                    </label>
                    <span class="setting-label">标题更新通知</span>
                </div>

                <div class="setting-item">
                    <label class="switch">
                        <input type="checkbox" id="quiet-hours-enabled" ${settings.quiet_hours.enabled ? 'checked' : ''}>
                        <span class="slider"></span>
                    </label>
                    <span class="setting-label">免打扰时段</span>
                </div>

                <div class="quiet-hours-settings ${settings.quiet_hours.enabled ? '' : 'hidden'}">
                    <div class="quiet-hours-header">
                        <h4>免打扰时间设置</h4>
                    </div>
                    <div class="quiet-hours-description">
                        在指定的时间段内，不会发送任何通知，避免打扰您的休息或工作。
                    </div>
                    <div class="time-range-visual">
                        <div class="time-range-active" id="time-range-indicator"></div>
                    </div>
                    <div class="time-pickers-container">
                        <div class="time-picker">
                            <label>开始时间</label>
                            <input type="time" id="quiet-start" value="${settings.quiet_hours.start}">
                        </div>
                        <div class="time-range-separator">至</div>
                        <div class="time-picker">
                            <label>结束时间</label>
                            <input type="time" id="quiet-end" value="${settings.quiet_hours.end}">
                        </div>
                    </div>
                    <div class="quiet-hours-presets">
                        <button type="button" class="preset-button" data-start="23:00" data-end="07:00">睡眠 (23:00-07:00)</button>
                        <button type="button" class="preset-button" data-start="09:00" data-end="18:00">工作 (09:00-18:00)</button>
                        <button type="button" class="preset-button" data-start="12:00" data-end="14:00">午休 (12:00-14:00)</button>
                    </div>
                </div>
            </div>

            <div class="dialog-buttons">
                <button class="button secondary" onclick="closeDialog(this)">
                    <i class="fas fa-times"></i> 取消
                </button>
                <button class="button primary" onclick="saveSettings('${roomId}', '${platform}', this)">
                    <i class="fas fa-save"></i> 保存
                </button>
            </div>
        `;

        overlay.appendChild(dialog);
        document.body.appendChild(overlay);

        // 点击遮罩层关闭对话框
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                closeDialog(overlay);
            }
        });

        // 确保对话框在可视区域内
        setTimeout(() => {
            const dialog = overlay.querySelector('.dialog');
            if (dialog) {
                // 如果对话框高度超过视口高度的 80%，滚动到顶部
                if (dialog.offsetHeight > window.innerHeight * 0.8) {
                    dialog.scrollTop = 0;
                }
            }
        }, 100);

        // 添加ESC键关闭对话框
        const escHandler = (e) => {
            if (e.key === 'Escape') {
                closeDialog(overlay);
                document.removeEventListener('keydown', escHandler);
            }
        };
        document.addEventListener('keydown', escHandler);

        // 免打扰时段切换
        const quietHoursCheckbox = document.getElementById('quiet-hours-enabled');
        const quietHoursSettings = document.querySelector('.quiet-hours-settings');

        quietHoursCheckbox.addEventListener('change', () => {
            if (quietHoursCheckbox.checked) {
                quietHoursSettings.classList.remove('hidden');
            } else {
                quietHoursSettings.classList.add('hidden');
            }
        });

        // 预设时间按钮功能
        const presetButtons = document.querySelectorAll('.preset-button');
        const startTimeInput = document.getElementById('quiet-start');
        const endTimeInput = document.getElementById('quiet-end');
        const timeRangeIndicator = document.getElementById('time-range-indicator');

        // 更新时间范围可视化
        function updateTimeRangeVisual(startTime, endTime) {
            // 将时间转换为分钟数
            const startParts = startTime.split(':');
            const endParts = endTime.split(':');

            const startMinutes = parseInt(startParts[0]) * 60 + parseInt(startParts[1]);
            const endMinutes = parseInt(endParts[0]) * 60 + parseInt(endParts[1]);

            // 计算百分比位置
            let startPercent, widthPercent;

            if (endMinutes > startMinutes) {
                // 正常时间范围（例如 9:00-18:00）
                startPercent = (startMinutes / 1440) * 100;
                widthPercent = ((endMinutes - startMinutes) / 1440) * 100;
            } else {
                // 跨天时间范围（例如 22:00-8:00）
                startPercent = (startMinutes / 1440) * 100;
                widthPercent = ((1440 - startMinutes + endMinutes) / 1440) * 100;
            }

            // 更新可视化指示器
            timeRangeIndicator.style.left = startPercent + '%';
            timeRangeIndicator.style.width = widthPercent + '%';
        }

        // 初始化时间范围可视化
        updateTimeRangeVisual(startTimeInput.value, endTimeInput.value);

        // 监听时间输入变化
        startTimeInput.addEventListener('change', () => {
            updateTimeRangeVisual(startTimeInput.value, endTimeInput.value);
        });

        startTimeInput.addEventListener('input', () => {
            updateTimeRangeVisual(startTimeInput.value, endTimeInput.value);
        });

        endTimeInput.addEventListener('change', () => {
            updateTimeRangeVisual(startTimeInput.value, endTimeInput.value);
        });

        endTimeInput.addEventListener('input', () => {
            updateTimeRangeVisual(startTimeInput.value, endTimeInput.value);
        });

        // 添加点击事件以改善移动端体验
        document.querySelectorAll('.time-picker').forEach(picker => {
            const label = picker.querySelector('label');
            const input = picker.querySelector('input');

            if (label && input) {
                label.addEventListener('click', () => {
                    input.focus();
                });
            }
        });

        presetButtons.forEach(button => {
            button.addEventListener('click', () => {
                const startTime = button.getAttribute('data-start');
                const endTime = button.getAttribute('data-end');

                startTimeInput.value = startTime;
                endTimeInput.value = endTime;

                // 更新时间范围可视化
                updateTimeRangeVisual(startTime, endTime);

                // 高亮当前选中的预设
                presetButtons.forEach(btn => {
                    btn.style.backgroundColor = '';
                    btn.style.borderColor = '';
                    btn.style.color = '';
                });

                button.style.backgroundColor = 'rgba(67, 97, 238, 0.1)';
                button.style.borderColor = 'var(--primary-color)';
                button.style.color = 'var(--primary-color)';
            });
        });
    } catch (error) {
        console.error('加载设置失败:', error);
        showNotification('加载设置失败，请稍后重试', 'error');
    }
}

// 保存设置
async function saveSettings(roomId, platform, element) {
    const uid = new URLSearchParams(window.location.search).get('uid');
    const notifyLiveStart = document.getElementById('notify-live-start').checked;
    const notifyLiveEnd = document.getElementById('notify-live-end').checked;
    const notifyTitle = document.getElementById('notify-title').checked;
    const quietHoursEnabled = document.getElementById('quiet-hours-enabled').checked;
    const quietStart = document.getElementById('quiet-start').value;
    const quietEnd = document.getElementById('quiet-end').value;

    // 在移动端上，确保按钮可见
    if (window.innerWidth <= 768) {
        const dialog = element.closest('.dialog');
        if (dialog) {
            // 滚动到底部显示按钮
            dialog.scrollTop = dialog.scrollHeight;
        }
    }

    // 验证时间格式
    if (quietHoursEnabled) {
        if (!quietStart || !quietEnd) {
            showNotification('请设置有效的免打扰时间段', 'warning');
            return;
        }
    }

    const settings = {
        notify_live_start: notifyLiveStart,
        notify_live_end: notifyLiveEnd,
        notify_title: notifyTitle,
        quiet_hours: {
            enabled: quietHoursEnabled,
            start: quietStart,
            end: quietEnd
        }
    };

    try {
        // 显示保存中状态
        const saveButton = element.closest('.dialog-buttons').querySelector('.button.primary');
        const originalText = saveButton.innerHTML;
        saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
        saveButton.disabled = true;

        const response = await fetch('/api/subscription/settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ uid, platform, room_id: roomId, settings })
        });

        const data = await response.json();

        if (data.success) {
            showNotification('设置已保存', 'success');
            closeDialog(element);
        } else {
            showNotification(data.message || '保存设置失败', 'error');
            saveButton.innerHTML = originalText;
            saveButton.disabled = false;
        }
    } catch (error) {
        console.error('保存设置失败:', error);
        showNotification('保存设置失败，请稍后重试', 'error');

        // 恢复按钮状态
        const saveButton = element.closest('.dialog-buttons').querySelector('.button.primary');
        saveButton.innerHTML = '<i class="fas fa-save"></i> 保存';
        saveButton.disabled = false;
    }
}



// 移除了自动刷新功能
