"""
应用入口文件 - 异步版本
"""
import os
import logging
from aiohttp import web

from app.api.routes import routes as api_routes
from app.web.routes import routes as web_routes
from app.core.container import container
from app.core.monitor import AsyncMonitor
from app.core.storage import AsyncStorage
from app.utils.notifier import AsyncWxPusher
from app.utils.config_loader import ConfigLoader
from app.utils.logger import setup_logger
from app.utils.http_client import http_client

logger = setup_logger('app')

def load_app_config():
    """加载应用配置

    Returns:
        Dict[str, Any]: 配置数据
    """
    config_dir = os.path.join(os.path.dirname(__file__), 'config')
    config_file = os.path.join(config_dir, 'config.yaml')

    # 如果配置文件存在，则加载配置
    if os.path.exists(config_file):
        config = ConfigLoader.load_config(config_file, env_prefix='LIVE_MONITOR_')
        logger.info(f"从 {config_file} 加载配置")
    else:
        config = {}
        logger.info("使用默认配置")

    return config

async def init_app():
    """初始化应用"""
    # 创建应用
    app = web.Application()

    # 加载配置
    config = load_app_config()

    # 先注册配置，这样其他组件在初始化时可以使用
    container.register('config', config)

    # 初始化依赖注入容器
    storage = AsyncStorage()
    notifier = AsyncWxPusher()
    monitor = AsyncMonitor(storage, notifier)

    # 注册实例到容器
    container.register('storage', storage)
    container.register('notifier', notifier)
    container.register('monitor', monitor)
    container.register('http_client', http_client)
    container.register('config', config)

    # 初始化所有组件
    await container.init_all()

    # 添加路由
    app.add_routes(api_routes)
    app.add_routes(web_routes)

    # 启动监控器
    await monitor.start()

    # 添加关闭处理
    async def on_shutdown(_):
        # 停止监控器
        await monitor.stop()
        # 清理所有组件
        await container.cleanup_all()

    app.on_shutdown.append(on_shutdown)

    return app

def main():
    """主函数"""
    # 设置根日志记录器的级别为WARNING，只显示警告和错误信息
    logging.basicConfig(level=logging.WARNING)

    # 禁用aiohttp的访问日志
    logging.getLogger('aiohttp.access').setLevel(logging.ERROR)

    # 从配置中获取系统配置
    config = load_app_config()
    system_config = config.get('system', {'host': '0.0.0.0', 'port': 58551})

    # 自定义启动信息打印函数，只显示简洁的启动信息
    def print_startup_info(msg):
        if "Running on" in msg:
            # 提取启动信息，不使用美化样式
            url = msg.split("Running on")[1].strip()
            print(f"\n监控服务已启动: {url}")
            print(f"访问 Web 界面: http://{system_config.get('host', '0.0.0.0')}:{system_config.get('port', 58551)}/?uid=YOUR_USER_ID")
            print(f"按 Ctrl+C 停止服务\n")

    # 启动应用
    web.run_app(
        init_app(),
        host=system_config.get('host', '0.0.0.0'),
        port=system_config.get('port', 58551),
        access_log=None,  # 禁用访问日志
        print=print_startup_info  # 使用自定义打印函数
    )

if __name__ == '__main__':
    main()
