<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直播监控中心</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-broadcast-tower"></i>
                    <h1>直播监控中心</h1>
                </div>
                <!-- 移除了主题切换按钮 -->
            </div>
        </header>

        <main class="main-content">
            <!-- 统计卡片 -->
            <div class="stats-container">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <h3>订阅主播</h3>
                        <p id="total-streamers">加载中...</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon live">
                        <i class="fas fa-video"></i>
                    </div>
                    <div class="stat-info">
                        <h3>正在直播</h3>
                        <p id="live-streamers">加载中...</p>
                    </div>
                </div>
                <!-- 移除了刷新倒计时卡片 -->
            </div>

            <!-- 订阅管理面板 -->
            <div class="subscription-panel">
                <div class="panel-header">
                    <h2><i class="fas fa-plus-circle"></i> 添加订阅</h2>
                </div>
                <form id="add-subscription-form" class="add-subscription-form">
                    <div class="form-group">
                        <select id="platform" class="form-input" required>
                            <option value="">选择平台</option>
                            <option value="bilibili">哔哩哔哩</option>
                            <option value="douyu">斗鱼</option>
                            <option value="huya">虎牙</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <input type="text" id="room-id" class="form-input" placeholder="输入房间号" required>
                    </div>
                    <button type="submit" class="button primary">
                        <i class="fas fa-plus"></i> 添加订阅
                    </button>
                </form>
            </div>

            <!-- 筛选和排序 -->
            <div class="filter-container">
                <div class="filter-group">
                    <label for="status-filter">状态:</label>
                    <select id="status-filter" class="filter-select">
                        <option value="all">全部</option>
                        <option value="live">直播中</option>
                        <option value="offline">未直播</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="platform-filter">平台:</label>
                    <select id="platform-filter" class="filter-select">
                        <option value="all">全部</option>
                        <option value="bilibili">哔哩哔哩</option>
                        <option value="douyu">斗鱼</option>
                        <option value="huya">虎牙</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="sort-by">排序:</label>
                    <select id="sort-by" class="filter-select">
                        <option value="status">直播状态</option>
                        <option value="name">主播名称</option>
                        <option value="platform">平台</option>
                    </select>
                </div>
                <!-- 移除了刷新按钮 -->
            </div>

            <!-- 主播列表 -->
            <div class="streamer-container">
                <h2 class="section-title">我的订阅</h2>
                <div class="streamer-grid">
                    <!-- 加载动画 -->
                    <div class="loading">
                        <div class="loading-spinner"></div>
                        <p>正在加载主播数据...</p>
                    </div>
                </div>
            </div>
        </main>

        <!-- 移除了页脚 -->
    </div>

    <!-- 通知模板 -->
    <div id="notification-template" class="notification" style="display: none;">
        <div class="notification-icon">
            <i class="fas fa-info-circle"></i>
        </div>
        <div class="notification-content">
            <p class="notification-message"></p>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <script src="/static/js/main.js"></script>
</body>
</html>
