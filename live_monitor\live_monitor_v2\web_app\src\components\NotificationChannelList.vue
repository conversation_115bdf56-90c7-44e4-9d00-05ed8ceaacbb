<template>
  <div class="notification-channel-list">
    <h3>Notification Channels</h3>

    <div v_if="subscriptionStore.isLoading || channelStore.isLoading" class="loading">Loading channels...</div>
    <div v_if="channelStore.error" class="error-message">Error loading channels: {{ channelStore.error }}</div>
    <div v_if="subscriptionStore.error && !subscriptionStore.userId" class="error-message">
      Error with user session: {{ subscriptionStore.error }}. Please set a User ID.
    </div>

    <div v_if="!channelStore.isLoading && !channelStore.error && channels.length === 0 && subscriptionStore.userId">
      No notification channels found for this user.
    </div>

    <ul v_if="channels.length > 0">
      <li v-for="channel in channels" :key="channel.channel_type">
        <span>Type: {{ channel.channel_type }}</span>
        <span>UID: {{ channel.channel_uid }}</span>
        <button @click="editChannel(channel)">Edit</button>
        <button @click="confirmDeleteChannel(channel.channel_type)" :disabled="channelStore.isLoading">
          {{ channelStore.isLoading && currentActionType === 'delete' && currentActionChannelType === channel.channel_type ? 'Deleting...' : 'Delete' }}
        </button>
      </li>
    </ul>

    <div v_if="!subscriptionStore.userId && !subscriptionStore.isLoading" class="info-message">
      Please set a User ID to manage notification channels.
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useNotificationChannelStore } from '@/stores/notificationChannels';
import { useSubscriptionStore } from '@/stores/subscriptions'; // To get userId
import type { NotificationChannel } from '@/types';

const channelStore = useNotificationChannelStore();
const subscriptionStore = useSubscriptionStore();

// To track which channel is being deleted for loading state on button
const currentActionType = ref<'edit' | 'delete' | null>(null);
const currentActionChannelType = ref<string | null>(null);

// Expose channels from the store
const channels = computed(() => channelStore.channels);

// Define emits for parent component interaction
const emit = defineEmits<{
  (e: 'editChannel', channel: NotificationChannel): void;
}>();

// Fetch channels when component is mounted or userId changes
onMounted(() => {
  if (subscriptionStore.userId) {
    channelStore.fetchChannels(subscriptionStore.userId);
  }
});

watch(() => subscriptionStore.userId, (newUserId) => {
  if (newUserId) {
    channelStore.fetchChannels(newUserId);
  } else {
    // Clear channels if userId is removed
    channelStore.$patch({ channels: [], error: null });
  }
});

function editChannel(channel: NotificationChannel) {
  emit('editChannel', channel);
}

async function confirmDeleteChannel(channelType: string) {
  if (!subscriptionStore.userId) {
    channelStore.error = "User ID is not available. Cannot delete channel.";
    return;
  }
  if (window.confirm(`Are you sure you want to delete the channel of type "${channelType}"?`)) {
    currentActionType.value = 'delete';
    currentActionChannelType.value = channelType;
    try {
      await channelStore.deleteChannel(subscriptionStore.userId, channelType);
      // The store action fetchChannels on success, so list updates automatically.
    } catch (e: any) {
      // Error is already set in the store, but can be handled here if needed
      console.error("Failed to delete channel:", e.message);
    } finally {
      currentActionType.value = null;
      currentActionChannelType.value = null;
    }
  }
}
</script>

<style scoped>
.notification-channel-list {
  margin-top: 20px;
  padding: 15px;
  border: 1px solid #ccc;
  border-radius: 5px;
}
.loading, .error-message, .info-message {
  margin-bottom: 10px;
  padding: 8px;
  border-radius: 3px;
}
.loading {
  background-color: #e0e0e0;
}
.error-message {
  color: #D8000C;
  background-color: #FFD2D2;
}
.info-message {
  color: #00529B;
  background-color: #BDE5F8;
}
ul {
  list-style-type: none;
  padding: 0;
}
li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #eee;
}
li:last-child {
  border-bottom: none;
}
li span {
  margin-right: 10px;
}
button {
  margin-left: 10px;
  padding: 5px 10px;
  cursor: pointer;
}
button:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}
</style>
