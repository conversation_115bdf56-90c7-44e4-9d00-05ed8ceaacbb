<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useSubscriptionStore } from '@/stores/subscriptions'
import type { SubscriptionSettings } from '@/types'

const store = useSubscriptionStore()

const subscriptions = computed(() => store.subscriptions)
const globalIsLoading = computed(() => store.isLoading) // Global loading for fetching list
const error = computed(() => store.error) // Global error

// Local state for managing editable settings and per-item loading
const editableSettingsMap = ref<Record<number, SubscriptionSettings>>({})
const updatingSubscriptionId = ref<number | null>(null) // ID of sub currently being updated

// Initialize or update editableSettingsMap when subscriptions change
watch(subscriptions, (newSubs) => {
  newSubs.forEach(sub => {
    if (!editableSettingsMap.value[sub.id] ||
        JSON.stringify(editableSettingsMap.value[sub.id]) !== JSON.stringify(sub.settings)) {
      // Initialize with a deep copy of sub.settings to avoid direct mutation
      editableSettingsMap.value[sub.id] = { ...sub.settings };
    }
  });
  // Clean up map entries for subscriptions that no longer exist
  Object.keys(editableSettingsMap.value).forEach(idKey => {
    const id = parseInt(idKey, 10);
    if (!newSubs.some(sub => sub.id === id)) {
      delete editableSettingsMap.value[id];
    }
  });
}, { deep: true, immediate: true });


async function handleUpdateSettings(subscriptionId: number) {
  if (!editableSettingsMap.value[subscriptionId]) return;

  updatingSubscriptionId.value = subscriptionId;
  store.error = null; // Clear previous global errors before new action

  try {
    await store.updateSubscriptionSettings(subscriptionId, editableSettingsMap.value[subscriptionId]);
    // If successful, the store should update the subscription, and the watcher will sync editableSettingsMap.
    // Optionally, provide success feedback here.
  } catch (e) {
    // Error is already set in the store by updateSubscriptionSettings action
    // console.error("Update settings failed:", e);
    // Revert optimistic updates if any, or simply rely on watcher to re-sync with store state on error
    // For simplicity, we let the watcher handle re-syncing if the store's state didn't actually change.
  } finally {
    updatingSubscriptionId.value = null;
  }
}

function handleDelete(subscriptionId: number) {
  if (confirm('Are you sure you want to delete this subscription?')) {
    store.deleteSubscription(subscriptionId)
    // Remove from editable settings map if it exists
    if (editableSettingsMap.value[subscriptionId]) {
      delete editableSettingsMap.value[subscriptionId];
    }
  }
}
</script>

<template>
  <div class="subscription-list">
    <h2>My Subscriptions</h2>
    <div v-if="globalIsLoading && subscriptions.length === 0" class="loading">Loading subscriptions...</div>
    <!-- Display global error if it's not specific to an update action or if list is empty -->
    <div v-if="error && (!updatingSubscriptionId || subscriptions.length === 0)" class="error-message global-error-display">{{ error }}</div>
    
    <ul v-if="subscriptions.length > 0">
      <li v-for="sub in subscriptions" :key="sub.id" class="subscription-item">
        <div class="sub-main-info">
          <div class="sub-details">
            <span class="platform">{{ sub.platform_name }}</span>
            <span class="room-id">Room ID: {{ sub.room_id }}</span>
          </div>
          <button
            @click="handleDelete(sub.id)"
            :disabled="updatingSubscriptionId === sub.id"
            class="delete-btn">
            Delete
          </button>
        </div>

        <div class="settings-panel" v-if="editableSettingsMap[sub.id]">
          <p class="settings-title">Notification Settings:</p>
          <div class="setting-item">
            <input
              type="checkbox"
              :id="'live-start-' + sub.id"
              v-model="editableSettingsMap[sub.id].notify_live_start"
              :disabled="updatingSubscriptionId === sub.id" />
            <label :for="'live-start-' + sub.id">Notify on Live Start</label>
          </div>
          <div class="setting-item">
            <input
              type="checkbox"
              :id="'live-end-' + sub.id"
              v-model="editableSettingsMap[sub.id].notify_live_end"
              :disabled="updatingSubscriptionId === sub.id" />
            <label :for="'live-end-' + sub.id">Notify on Live End</label>
          </div>
          <div class="setting-item">
            <input
              type="checkbox"
              :id="'title-change-' + sub.id"
              v-model="editableSettingsMap[sub.id].notify_title_change"
              :disabled="updatingSubscriptionId === sub.id" />
            <label :for="'title-change-' + sub.id">Notify on Title Change</label>
          </div>
          <button
            @click="handleUpdateSettings(sub.id)"
            :disabled="updatingSubscriptionId === sub.id || JSON.stringify(editableSettingsMap[sub.id]) === JSON.stringify(sub.settings)"
            class="update-settings-btn">
            {{ updatingSubscriptionId === sub.id ? 'Saving...' : 'Update Settings' }}
          </button>
           <!-- Per-item error display for update actions -->
          <div v-if="error && updatingSubscriptionId === sub.id" class="error-message item-error-display">
            Failed to update: {{ error }}
          </div>
        </div>
      </li>
    </ul>
    <p v-else-if="!globalIsLoading && !error">No subscriptions yet. Add one above!</p>
  </div>
</template>

<style scoped>
.subscription-list {
  margin-top: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.subscription-list h2 {
  margin-top: 0;
  margin-bottom: 20px; /* Increased margin */
  color: #333;
  text-align: center;
}

.loading {
  text-align: center;
  padding: 10px;
  margin-bottom: 10px;
}
.error-message { /* General class for error messages */
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  color: #D8000C; /* Dark red */
  background-color: #FFD2D2; /* Light red */
  border: 1px solid #D8000C;
}
.global-error-display { /* For global errors not tied to a specific item action */
  text-align: center;
}
.item-error-display { /* For errors specific to an item (e.g. update failed) */
  margin-top: 10px;
  font-size: 0.9em;
}


ul {
  list-style-type: none;
  padding: 0;
}

.subscription-item {
  padding: 15px; /* Increased padding */
  margin-bottom: 12px; /* Increased margin */
  background-color: #fff;
  border: 1px solid #ddd; /* Slightly darker border */
  border-radius: 6px; /* Slightly more rounded corners */
  transition: box-shadow 0.2s ease;
}

.subscription-item:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.05); /* Subtle shadow on hover */
}

.sub-main-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px; /* Space between main info and settings panel */
}

.sub-details {
  display: flex;
  flex-direction: column;
}

.platform {
  font-weight: bold;
  color: #007bff;
  margin-bottom: 4px;
}

.room-id {
  font-size: 0.9em;
  color: #555;
}

.settings-panel {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #eee;
}

.settings-title {
  font-weight: bold;
  margin-bottom: 8px;
  font-size: 0.95em;
  color: #444;
}

.setting-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px; /* Increased margin for better spacing */
}

.setting-item input[type="checkbox"] {
  margin-right: 8px;
  transform: scale(1.1); /* Slightly larger checkboxes */
  cursor: pointer;
}
.setting-item input[type="checkbox"]:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}


.setting-item label {
  font-size: 0.9em;
  color: #333;
  cursor: pointer;
}


.delete-btn, .update-settings-btn {
  padding: 8px 12px; /* Uniform padding */
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.9em; /* Slightly smaller font */
}

.delete-btn {
  background-color: #dc3545; /* Bootstrap danger red */
}

.delete-btn:hover {
  background-color: #c82333;
}
.delete-btn:disabled {
  background-color: #dc3545;
  opacity: 0.5;
  cursor: not-allowed;
}

.update-settings-btn {
  background-color: #28a745; /* Bootstrap success green */
  margin-top: 10px; /* Space above the button */
}

.update-settings-btn:hover {
  background-color: #218838;
}

.update-settings-btn:disabled {
  background-color: #28a745; /* Keep color but change opacity and cursor */
  opacity: 0.5;
  cursor: not-allowed;
}

/* Ensure v-else-if is valid - should be v-else */
p[v.else-if="!isLoading && !error"] {
  /* This selector is illustrative of the v-else-if, actual template uses v-else-if */
  text-align: center;
  color: #666;
  margin-top: 20px;
}
</style>
  text-align: center;
  padding: 10px;
  margin-bottom: 10px;
}

.error-message {
  color: red;
  background-color: #ffe0e0;
  border: 1px solid red;
  border-radius: 4px;
}

ul {
  list-style-type: none;
  padding: 0;
}

.subscription-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  margin-bottom: 8px;
  background-color: #fff;
  border: 1px solid #eee;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.subscription-item:hover {
  background-color: #f0f0f0;
}

.sub-info {
  display: flex;
  flex-direction: column; /* Stack platform and room_id vertically */
}

.platform {
  font-weight: bold;
  color: #007bff; /* Example color */
  margin-bottom: 4px; /* Space between platform and room_id */
}

.room-id {
  font-size: 0.9em;
  color: #555;
}

.settings {
  font-size: 0.8em;
  color: #777;
  margin-top: 5px;
  white-space: pre-wrap; /* So JSON string wraps */
  background-color: #e9e9e9;
  padding: 5px;
  border-radius: 3px;
}

.delete-btn {
  padding: 6px 12px;
  background-color: #ff4d4d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.delete-btn:hover {
  background-color: #cc0000;
}
</style>
