import asyncio
import unittest
from unittest.mock import patch, As<PERSON><PERSON><PERSON>, MagicMock, ANY
from typing import Dict, Any, Optional, List
from email.message import EmailMessage # For asserting message content

# Adjust the import path based on your project structure and PYTHONPATH
from services.notification_service.notifiers.email_notifier import EmailNotifier
import aiosmtplib # For SMTPException

class TestEmailNotifier(unittest.IsolatedAsyncioTestCase):

    def _get_base_config(self, **kwargs) -> Dict[str, Any]:
        config = {
            "smtp_host": "smtp.test.com",
            "smtp_port": 587,
            "smtp_user": "<EMAIL>",
            "smtp_password": "password",
            "sender_email": "<EMAIL>",
            "use_tls": True, # Default to True for port 587
            "use_ssl": False,
        }
        config.update(kwargs)
        return config

    async def test_init_configuration(self):
        config = self._get_base_config()
        notifier = EmailNotifier(config=config)
        await notifier.init() # Call init, though it's minimal

        self.assertEqual(notifier.smtp_host, "smtp.test.com")
        self.assertEqual(notifier.smtp_port, 587)
        self.assertEqual(notifier.smtp_user, "<EMAIL>")
        self.assertEqual(notifier.smtp_password, "password")
        self.assertEqual(notifier.sender_email, "<EMAIL>")
        self.assertTrue(notifier.use_tls)
        self.assertFalse(notifier.use_ssl)

    async def test_init_ssl_configuration(self):
        # Test SSL on port 465 implies use_ssl=True, use_tls=False
        config_ssl = self._get_base_config(smtp_port=465, use_ssl=True, use_tls=False) # Explicit
        notifier_ssl = EmailNotifier(config=config_ssl)
        await notifier_ssl.init()
        self.assertTrue(notifier_ssl.use_ssl)
        self.assertFalse(notifier_ssl.use_tls)

        # Test if only use_ssl=True is provided, port should ideally be 465
        config_ssl_only = self._get_base_config(use_ssl=True, use_tls=None, smtp_port=465) # TLS not specified
        notifier_ssl_only = EmailNotifier(config=config_ssl_only)
        await notifier_ssl_only.init()
        self.assertTrue(notifier_ssl_only.use_ssl)
        self.assertFalse(notifier_ssl_only.use_tls) # Should default to False if SSL is true


    @patch('services.notification_service.notifiers.email_notifier.aiosmtplib.send', new_callable=AsyncMock)
    async def test_send_email_success_plain_text_tls(self, mock_aiosmtplib_send):
        config = self._get_base_config(use_tls=True, use_ssl=False, smtp_port=587)
        notifier = EmailNotifier(config=config)
        await notifier.init()

        title = "Test Email Subject"
        body = "This is the plain text body of the email."
        recipients = ["<EMAIL>", "<EMAIL>"]

        await notifier.send(title, body, recipients)

        self.assertEqual(mock_aiosmtplib_send.call_count, len(recipients))
        
        first_call_args = mock_aiosmtplib_send.call_args_list[0]
        msg_sent_to_recipient1: EmailMessage = first_call_args.args[0]
        
        self.assertEqual(msg_sent_to_recipient1["Subject"], title)
        self.assertEqual(msg_sent_to_recipient1["From"], config["sender_email"])
        self.assertEqual(msg_sent_to_recipient1["To"], recipients[0])
        self.assertEqual(msg_sent_to_recipient1.get_content().strip(), body) # strip for potential extra newlines
        self.assertEqual(msg_sent_to_recipient1.get_content_maintype(), "text")
        self.assertEqual(msg_sent_to_recipient1.get_content_subtype(), "plain")


        send_kwargs = first_call_args.kwargs
        self.assertEqual(send_kwargs["hostname"], config["smtp_host"])
        self.assertEqual(send_kwargs["port"], config["smtp_port"])
        self.assertEqual(send_kwargs["username"], config["smtp_user"])
        self.assertEqual(send_kwargs["password"], config["smtp_password"])
        self.assertTrue(send_kwargs["use_tls"]) # Explicitly True for port 587 with TLS

    @patch('services.notification_service.notifiers.email_notifier.aiosmtplib.send', new_callable=AsyncMock)
    async def test_send_email_success_html_content_ssl(self, mock_aiosmtplib_send):
        # Test with SSL on port 465
        config = self._get_base_config(smtp_port=465, use_ssl=True, use_tls=False)
        notifier = EmailNotifier(config=config)
        await notifier.init()

        title = "HTML Email"
        plain_body_fallback = "Please enable HTML." # Not explicitly set by SUT, but good to know
        html_body = "<p>This is an <strong>HTML</strong> email.</p>"
        recipients = ["<EMAIL>"]
        details = {"html_content": html_body}

        await notifier.send(title, "This is plain text (should be ignored)", recipients, details=details)
        
        mock_aiosmtplib_send.assert_called_once()
        call_args = mock_aiosmtplib_send.call_args
        msg_sent: EmailMessage = call_args.args[0]

        self.assertEqual(msg_sent["Subject"], title)
        self.assertEqual(msg_sent["To"], recipients[0])
        self.assertTrue(msg_sent.is_multipart()) # Should be multipart/alternative
        
        html_part_found = False
        for part in msg_sent.iter_parts():
            if part.get_content_type() == "text/html":
                html_part_found = True
                self.assertEqual(part.get_payload(decode=True).decode().strip(), html_body)
                break
        self.assertTrue(html_part_found, "HTML part not found in multipart email")

        send_kwargs = call_args.kwargs
        self.assertEqual(send_kwargs["hostname"], config["smtp_host"])
        self.assertEqual(send_kwargs["port"], config["smtp_port"])
        # For SSL on port 465, use_tls should be False as SSL is implicit for the connection
        self.assertFalse(send_kwargs.get("use_tls", False)) 


    @patch('services.notification_service.notifiers.email_notifier.aiosmtplib.send', new_callable=AsyncMock)
    async def test_send_email_smtp_error(self, mock_aiosmtplib_send):
        config = self._get_base_config()
        notifier = EmailNotifier(config=config)
        await notifier.init()

        mock_aiosmtplib_send.side_effect = aiosmtplib.SMTPException("Simulated SMTP Error", code=550)

        with self.assertLogs(logger='services.notification_service.notifiers.email_notifier', level='ERROR') as cm:
            await notifier.send("Error Test", "Body", ["<EMAIL>"])
        
        self.assertTrue(any("SMTP error sending email" in log for log in cm.output))
        self.assertTrue(any("550 Simulated SMTP Error" in log for log in cm.output))
        mock_aiosmtplib_send.assert_called_once()

    async def test_send_email_missing_config(self):
        # Test without smtp_host
        notifier_no_host = EmailNotifier(config=self._get_base_config(smtp_host=None))
        await notifier_no_host.init() # init logs error but doesn't prevent send attempt
        with self.assertLogs(logger='services.notification_service.notifiers.email_notifier', level='ERROR') as cm:
            await notifier_no_host.send("No Host", "Body", ["<EMAIL>"])
        self.assertTrue(any("SMTP host or sender email not configured" in log for log in cm.output))

        # Test without sender_email
        notifier_no_sender = EmailNotifier(config=self._get_base_config(sender_email=None))
        await notifier_no_sender.init()
        with self.assertLogs(logger='services.notification_service.notifiers.email_notifier', level='ERROR') as cm:
            await notifier_no_sender.send("No Sender", "Body", ["<EMAIL>"])
        self.assertTrue(any("SMTP host or sender email not configured" in log for log in cm.output))


    @patch('services.notification_service.notifiers.email_notifier.aiosmtplib.send', new_callable=AsyncMock)
    async def test_send_email_no_recipients(self, mock_aiosmtplib_send):
        config = self._get_base_config()
        notifier = EmailNotifier(config=config)
        await notifier.init()

        with self.assertLogs(logger='services.notification_service.notifiers.email_notifier', level='WARNING') as cm:
            await notifier.send("No Recipients", "Body", [])
        
        self.assertTrue(any("No target_users (recipient emails) provided" in log for log in cm.output))
        mock_aiosmtplib_send.assert_not_called()

    async def test_close_method(self):
        # Close method is minimal as aiosmtplib.send handles connection per call
        config = self._get_base_config()
        notifier = EmailNotifier(config=config)
        await notifier.init() # Does minimal logging
        with self.assertLogs(logger='services.notification_service.notifiers.email_notifier', level='INFO') as cm:
            await notifier.close()
        self.assertTrue(any("closed (no persistent client)" in log for log in cm.output))


if __name__ == "__main__":
    unittest.main()
