from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status, Request
from fastapi.responses import JSONResponse
import httpx
from typing import List, Dict, Any, Optional

from . import schemas
from .config import settings # Import the settings instance

app = FastAPI(title="API Gateway V2")

# --- HTTPX Client Dependency ---
async def get_http_client() -> httpx.AsyncClient:
    async with httpx.AsyncClient() as client:
        yield client

# --- Error Handling ---
async def forward_request(
    client: httpx.AsyncClient,
    method: str,
    url: str,
    json_data: Optional[Dict[str, Any]] = None,
    params: Optional[Dict[str, Any]] = None,
    expected_status: int = status.HTTP_200_OK
) -> Any:
    try:
        response = await client.request(method, url, json=json_data, params=params, timeout=10.0)
        
        if response.status_code == expected_status:
            if response.content: 
                if expected_status == status.HTTP_204_NO_CONTENT:
                    return None 
                try:
                    return response.json()
                except ValueError: 
                    raise HTTPException(
                        status_code=status.HTTP_502_BAD_GATEWAY,
                        detail="Invalid JSON response from downstream service.",
                        headers={"X-Downstream-Service-Url": url, "X-Downstream-Service-Status": str(response.status_code)}
                    )
            return None if expected_status == status.HTTP_204_NO_CONTENT else (response.json() if response.content else None)

        error_detail_key = "detail" 
        error_message = f"Error from downstream service at {url}. Status: {response.status_code}"
        downstream_error_content = None
        try:
            downstream_error_content = response.json()
            if isinstance(downstream_error_content, dict) and error_detail_key in downstream_error_content:
                error_message = downstream_error_content[error_detail_key]
        except ValueError:
            downstream_error_content = response.text if response.content else "No error content from downstream service."
            error_message = downstream_error_content 

        gateway_error_detail = {
            "message": "Downstream service request failed.",
            "downstream_url": url,
            "downstream_status_code": response.status_code,
            "downstream_response": downstream_error_content
        }
        
        forwarded_status_code = response.status_code if 400 <= response.status_code < 600 else status.HTTP_502_BAD_GATEWAY
        
        raise HTTPException(
            status_code=forwarded_status_code,
            detail=gateway_error_detail
        )

    except httpx.RequestError as exc: 
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Service unavailable or network error connecting to {url}: {str(exc)}",
        )
    except Exception as e: 
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred while request forwarding: {str(e)}",
        )


# --- Subscription Endpoints ---

@app.post("/api/v2/users/{user_id}/subscriptions", response_model=schemas.Subscription, status_code=status.HTTP_201_CREATED)
async def create_user_subscription(
    user_id: str, 
    subscription_in: schemas.SubscriptionCreate, 
    client: httpx.AsyncClient = Depends(get_http_client)
):
    if subscription_in.user_id != user_id:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="User ID in path does not match User ID in body.")
    
    storage_url = f"{settings.STORAGE_SERVICE_URL}/users/{user_id}/subscriptions"
    return await forward_request(
        client, "POST", storage_url, json_data=subscription_in.model_dump(),
        expected_status=status.HTTP_201_CREATED
    )

@app.get("/api/v2/users/{user_id}/subscriptions", response_model=List[schemas.Subscription])
async def read_user_subscriptions(user_id: str, client: httpx.AsyncClient = Depends(get_http_client)):
    storage_url = f"{settings.STORAGE_SERVICE_URL}/users/{user_id}/subscriptions"
    return await forward_request(client, "GET", storage_url)

@app.delete("/api/v2/users/{user_id}/subscriptions/{subscription_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user_subscription(
    user_id: str, 
    subscription_id: int, 
    client: httpx.AsyncClient = Depends(get_http_client)
):
    storage_url = f"{settings.STORAGE_SERVICE_URL}/users/{user_id}/subscriptions/{subscription_id}"
    await forward_request(client, "DELETE", storage_url, expected_status=status.HTTP_204_NO_CONTENT)
    return None 

@app.put("/api/v2/users/{user_id}/subscriptions/{subscription_id}", response_model=schemas.Subscription)
async def update_user_subscription_settings(
    user_id: str, 
    subscription_id: int, 
    settings_in: schemas.SubscriptionSettings, 
    client: httpx.AsyncClient = Depends(get_http_client)
):
    storage_url = f"{settings.STORAGE_SERVICE_URL}/users/{user_id}/subscriptions/{subscription_id}"
    return await forward_request(
        client, "PUT", storage_url, json_data=settings_in.model_dump(),
        expected_status=status.HTTP_200_OK 
    )

# --- Notification Channel Endpoints ---

@app.put("/api/v2/users/{user_id}/notification_channels/{channel_type}", response_model=schemas.NotificationChannel)
async def set_user_notification_channel(
    user_id: str, 
    channel_type: str, 
    channel_data_in: schemas.NotificationChannelCreate, 
    client: httpx.AsyncClient = Depends(get_http_client)
):
    if channel_data_in.user_id != user_id:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="User ID in path does not match User ID in body.")
    if channel_data_in.channel_type != channel_type:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Channel type in path does not match channel type in body.")
        
    storage_url = f"{settings.STORAGE_SERVICE_URL}/users/{user_id}/notification_channels/{channel_type}"
    return await forward_request(
        client, "PUT", storage_url, json_data=channel_data_in.model_dump(),
        expected_status=status.HTTP_200_OK
    )

@app.get("/api/v2/users/{user_id}/notification_channels", response_model=List[schemas.NotificationChannel])
async def read_user_notification_channels(user_id: str, client: httpx.AsyncClient = Depends(get_http_client)):
    storage_url = f"{settings.STORAGE_SERVICE_URL}/users/{user_id}/notification_channels"
    return await forward_request(client, "GET", storage_url)

@app.get("/api/v2/users/{user_id}/notification_channels/{channel_type}", response_model=schemas.NotificationChannel)
async def read_user_notification_channel(
    user_id: str, 
    channel_type: str, 
    client: httpx.AsyncClient = Depends(get_http_client)
):
    storage_url = f"{settings.STORAGE_SERVICE_URL}/users/{user_id}/notification_channels/{channel_type}"
    return await forward_request(client, "GET", storage_url)

# --- Message Template Endpoints ---

@app.post("/api/v2/templates", response_model=schemas.MessageTemplate, status_code=status.HTTP_201_CREATED)
async def create_message_template(
    template_in: schemas.MessageTemplateCreate,
    client: httpx.AsyncClient = Depends(get_http_client)
):
    storage_url = f"{settings.STORAGE_SERVICE_URL}/templates"
    return await forward_request(
        client, "POST", storage_url, json_data=template_in.model_dump(),
        expected_status=status.HTTP_201_CREATED
    )

@app.get("/api/v2/templates", response_model=List[schemas.MessageTemplate])
async def list_message_templates(
    user_id: Optional[str] = None,
    template_type: Optional[schemas.TemplateTypeEnum] = None,
    client: httpx.AsyncClient = Depends(get_http_client)
):
    params = {}
    if user_id is not None:
        params["user_id"] = user_id
    if template_type is not None:
        params["template_type"] = template_type.value # Pass enum value
    
    storage_url = f"{settings.STORAGE_SERVICE_URL}/templates"
    return await forward_request(client, "GET", storage_url, params=params)

@app.get("/api/v2/templates/{template_id}", response_model=schemas.MessageTemplate)
async def get_message_template(
    template_id: int,
    client: httpx.AsyncClient = Depends(get_http_client)
):
    storage_url = f"{settings.STORAGE_SERVICE_URL}/templates/{template_id}"
    return await forward_request(client, "GET", storage_url)

@app.put("/api/v2/templates/{template_id}", response_model=schemas.MessageTemplate)
async def update_message_template(
    template_id: int,
    template_in: schemas.MessageTemplateUpdate,
    client: httpx.AsyncClient = Depends(get_http_client)
):
    storage_url = f"{settings.STORAGE_SERVICE_URL}/templates/{template_id}"
    return await forward_request(
        client, "PUT", storage_url, json_data=template_in.model_dump(exclude_unset=True),
        expected_status=status.HTTP_200_OK
    )

@app.delete("/api/v2/templates/{template_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_message_template(
    template_id: int,
    client: httpx.AsyncClient = Depends(get_http_client)
):
    storage_url = f"{settings.STORAGE_SERVICE_URL}/templates/{template_id}"
    await forward_request(client, "DELETE", storage_url, expected_status=status.HTTP_204_NO_CONTENT)
    return None

# --- Health Check ---
@app.get("/health", status_code=status.HTTP_200_OK)
async def health_check():
    return {"status": "ok", "service": "API Gateway V2"}
