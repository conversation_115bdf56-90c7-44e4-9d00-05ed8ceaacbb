import httpx
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timezone # For start_time

# Assuming PlatformAdapterV1 is in libs.plugin_interfaces relative to a common root for services
# Adjust path if necessary based on how PYTHONPATH or service structure is handled.
# For now, using a placeholder that might work in a monorepo setup.
from libs.plugin_interfaces.platform_adapter_v1 import PlatformAdapterV1

logger = logging.getLogger(__name__) # Standard Python logger

# Default Bilibili API URLs (can be overridden by config passed to __init__)
DEFAULT_BILIBILI_API_URL = "https://api.live.bilibili.com/room/v1/Room/get_info?room_id={}"
DEFAULT_BILIBILI_USER_API_URL = "https://api.live.bilibili.com/live_user/v1/Master/info?uid={}"
DEFAULT_BILIBILI_ROOM_URL_TEMPLATE = "https://live.bilibili.com/{}"
DEFAULT_BILIBILI_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Referer': 'https://live.bilibili.com/',
    'Accept': 'application/json, text/plain, */*',
}

class BilibiliAdapter(PlatformAdapterV1):
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(platform_name="bilibili", config=config)
        self.api_url_template = self.config.get("api_url_template", DEFAULT_BILIBILI_API_URL)
        self.user_api_url_template = self.config.get("user_api_url_template", DEFAULT_BILIBILI_USER_API_URL)
        self.room_url_template = self.config.get("room_url_template", DEFAULT_BILIBILI_ROOM_URL_TEMPLATE)
        self.headers = self.config.get("headers", DEFAULT_BILIBILI_HEADERS)
        self.http_client: Optional[httpx.AsyncClient] = None

    async def init(self) -> None:
        """Initialize the HTTP client."""
        if self.http_client is None:
            self.http_client = httpx.AsyncClient(headers=self.headers, timeout=10.0)
        logger.info(f"BilibiliAdapter for '{self.platform_name}' initialized.")

    async def _fetch_json(self, url: str) -> Optional[Dict[str, Any]]:
        if not self.http_client:
            await self.init() # Ensure client is initialized
        
        if not self.http_client: # Should not happen if init works
            logger.error("HTTP client not initialized in BilibiliAdapter.")
            return None

        try:
            response = await self.http_client.get(url)
            response.raise_for_status()  # Raise an exception for bad status codes (4xx or 5xx)
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error fetching {url}: {e.response.status_code} - {e.response.text}")
        except httpx.RequestError as e:
            logger.error(f"Request error fetching {url}: {e}")
        except Exception as e: # Includes JSONDecodeError
            logger.error(f"Generic error fetching or parsing JSON from {url}: {e}")
        return None

    async def get_streamer_status(self, room_id: str) -> Optional[Dict[str, Any]]:
        """Fetch Bilibili streamer status."""
        room_api_url = self.api_url_template.format(room_id)
        room_data = await self._fetch_json(room_api_url)

        if not room_data or room_data.get("code") != 0:
            error_msg = room_data.get("message", "Failed to fetch room data or unexpected code.") if room_data else "No data from room API."
            logger.warning(f"Bilibili: Could not get room info for room_id {room_id}. Message: {error_msg}")
            return None

        room_info_data = room_data.get("data", {})
        uid = room_info_data.get("uid")
        if not uid:
            logger.warning(f"Bilibili: UID not found in room_info for room_id {room_id}.")
            return None

        user_api_url = self.user_api_url_template.format(uid)
        user_data = await self._fetch_json(user_api_url)

        if not user_data or user_data.get("code") != 0:
            error_msg = user_data.get("message", "Failed to fetch user data or unexpected code.") if user_data else "No data from user API."
            logger.warning(f"Bilibili: Could not get user info for uid {uid} (room_id {room_id}). Message: {error_msg}")
            return None
        
        user_info_data = user_data.get("data", {}).get("info", {})
        anchor_name = user_info_data.get("uname", "Unknown Anchor")
        
        live_status_code = room_info_data.get("live_status") # 0: offline, 1: live, 2:轮播
        is_live = live_status_code == 1

        # Attempt to get start_time if live
        start_time_unix = None
        if is_live and "live_time" in room_info_data: # live_time might be a string like "YYYY-MM-DD HH:MM:SS" or unix timestamp
            # The old code didn't parse live_time, but if it's available and needed:
            # Example: if live_time is a unix timestamp (seconds)
            # start_time_unix = room_info_data.get("live_time_unix_ts_seconds_example") 
            # For Bilibili, 'live_time' from room_info is often 0 or not a direct timestamp when live.
            # 'live_start_time' from room_play_info might be better if available:
            # https://api.live.bilibili.com/xlive/web-room/v1/index/getRoomPlayInfo?room_id=...&protocol=...&format=...&codec=...&qn=...&platform=web
            # For now, keeping it simple and not fetching this additional URL.
            # If 'live_time' field in room_info_data is indeed a unix timestamp when live:
            live_start_timestamp_str = str(room_info_data.get("live_time", 0)) # Assuming it's a string like "2023-10-01 10:00:00" or similar
            # Bilibili actual API for room_info 'live_time' is often 0 when live.
            # The 'live_status' is the primary indicator.
            # A more reliable start time might need another API call or be less precise.
            # For now, if live, we can use current time as a rough proxy or leave it None if not easily available.
            # The old code did not provide start_time.
            start_time = datetime.now(timezone.utc) if is_live else None # Placeholder
        else:
            start_time = None

        status_dict: Dict[str, Any] = {
            "platform_name": self.platform_name,
            "room_id": str(room_id),
            "anchor_name": anchor_name,
            "title": room_info_data.get("title", "Unknown Title"),
            "live_status": is_live,
            "stream_url": self.room_url_template.format(room_id),
            "cover_image_url": room_info_data.get("cover_from_user") or room_info_data.get("keyframe"), # Example fields
            "viewer_count": room_info_data.get("online"), # 'online' field is usually viewer count
            "start_time": start_time,
        }
        return status_dict

    def get_platform_display_name(self) -> str:
        """Return a user-friendly display name for the platform."""
        return self.config.get("display_name", "Bilibili")

    async def close(self) -> None:
        """Close the HTTP client."""
        if self.http_client:
            await self.http_client.aclose()
            self.http_client = None
        logger.info(f"BilibiliAdapter for '{self.platform_name}' closed.")
