"""
缓存工具模块
"""
import time
import asyncio
import functools
from typing import Dict, Any, Callable, Optional, Tuple, Awaitable

from app.core.container import container
from .logger import setup_logger

logger = setup_logger('utils.cache')

# 缓存存储
_cache: Dict[str, Tuple[float, Any]] = {}
# 缓存锁，防止并发更新缓存
_cache_lock = asyncio.Lock()

def async_cache(ttl: int = None):
    """
    异步函数缓存装饰器

    Args:
        ttl: 缓存生存时间（秒），如果为None则使用配置文件中的设置

    Returns:
        装饰后的函数
    """
    # 获取配置
    try:
        config = container.get('config')
        cache_config = config.get('cache', {})
        default_ttl = cache_config.get('ttl', 60)
        cache_enabled = cache_config.get('enabled', True)
    except Exception:
        default_ttl = 60
        cache_enabled = True

    # 使用提供的ttl或默认值
    actual_ttl = ttl if ttl is not None else default_ttl
    def decorator(func: Callable[..., Awaitable[Any]]) -> Callable[..., Awaitable[Any]]:
        """
        缓存装饰器函数

        Args:
            func: 要装饰的异步函数

        Returns:
            Callable: 装饰后的函数
        """
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            """
            包装原函数的异步函数，添加缓存功能

            Args:
                *args: 传递给原函数的位置参数
                **kwargs: 传递给原函数的关键字参数

            Returns:
                Any: 原函数的返回值或缓存的结果
            """

            # 如果缓存禁用，直接执行原函数
            if not cache_enabled:
                return await func(*args, **kwargs)

            # 生成缓存键
            cache_key = _generate_cache_key(func, args, kwargs)

            # 尝试从缓存获取结果
            cached_result = await _get_from_cache(cache_key, actual_ttl)
            if cached_result is not None:
                logger.debug(f"缓存命中: {cache_key}")
                return cached_result

            # 缓存未命中，执行原函数
            logger.debug(f"缓存未命中: {cache_key}")
            result = await func(*args, **kwargs)

            # 将结果存入缓存
            if result is not None:
                await _save_to_cache(cache_key, result)

            return result
        return wrapper
    return decorator

async def _get_from_cache(key: str, ttl: int) -> Optional[Any]:
    """
    从缓存中获取数据

    Args:
        key: 缓存键
        ttl: 缓存生存时间（秒）

    Returns:
        缓存数据，如果不存在或已过期则返回None
    """
    if key in _cache:
        timestamp, data = _cache[key]
        if time.time() - timestamp < ttl:
            return data
        else:
            # 缓存已过期，异步清理
            asyncio.create_task(_clean_expired_cache())
    return None

async def _save_to_cache(key: str, data: Any) -> None:
    """
    将数据保存到缓存

    Args:
        key: 缓存键
        data: 缓存数据
    """
    # 获取缓存大小限制
    try:
        config = container.get('config')
        cache_config = config.get('cache', {})
        max_size = cache_config.get('max_size', 1000)
    except Exception:
        max_size = 1000

    async with _cache_lock:
        _cache[key] = (time.time(), data)

    # 如果缓存过大，异步清理
    if len(_cache) > max_size:
        asyncio.create_task(_clean_expired_cache())

async def _clean_expired_cache(ttl: int = None) -> None:
    """
    清理过期缓存

    Args:
        ttl: 缓存生存时间（秒），如果为None则使用配置文件中的设置
    """
    # 获取配置
    try:
        config = container.get('config')
        cache_config = config.get('cache', {})
        default_ttl = cache_config.get('ttl', 300)
    except Exception:
        default_ttl = 300

    # 使用提供的ttl或默认值
    actual_ttl = ttl if ttl is not None else default_ttl
    async with _cache_lock:
        now = time.time()
        expired_keys = [k for k, (timestamp, _) in _cache.items() if now - timestamp > actual_ttl]
        for k in expired_keys:
            del _cache[k]

    if expired_keys:
        logger.debug(f"已清理 {len(expired_keys)} 个过期缓存")

def _generate_cache_key(func: Callable, args: Tuple[Any, ...], kwargs: Dict[str, Any]) -> str:
    """
    生成缓存键

    Args:
        func: 函数
        args: 位置参数
        kwargs: 关键字参数

    Returns:
        缓存键
    """
    # 使用函数名、模块名和参数生成缓存键
    key_parts = [func.__module__, func.__name__]

    # 添加位置参数
    for arg in args:
        key_parts.append(str(arg))

    # 添加关键字参数（按键排序）
    for k in sorted(kwargs.keys()):
        key_parts.append(f"{k}={kwargs[k]}")

    return ":".join(key_parts)

def clear_cache() -> None:
    """清空缓存"""
    global _cache
    _cache = {}
