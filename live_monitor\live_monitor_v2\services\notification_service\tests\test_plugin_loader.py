import unittest
from unittest.mock import patch, MagicMock, mock_open
import os
import importlib
import inspect 

from libs.plugin_interfaces.base_notifier_v1 import BaseNotifierV1
# Assuming config models are accessible for testing
from app.config import Settings, NotifierPluginConfig, MVP_NOTIFIERS_CONFIG # From notification_service.app.config
from app.plugin_loader import load_notifiers # The function to test

# --- Mock Notifier Classes ---
class MockNotifierTypeA(BaseNotifierV1):
    # Class attribute to indicate its type for config lookup
    # The loader uses notifier_instance.notifier_type which is set in BaseNotifierV1 constructor
    def __init__(self, config: dict):
        super().__init__(notifier_type="type_a", config=config)
        self.initialized_with_specific_settings = config
        self.init_called = False
        self.send_called_count = 0

    async def init(self) -> None: self.init_called = True
    async def send(self, title: str, body: str, target_users: list, details: dict = None) -> None: 
        self.send_called_count +=1
    async def close(self) -> None: pass

class MockNotifierTypeB(BaseNotifierV1):
    def __init__(self, config: dict):
        super().__init__(notifier_type="type_b", config=config)
        self.specific_settings_received = config
    async def init(self) -> None: pass
    async def send(self, title: str, body: str, target_users: list, details: dict = None) -> None: pass
    async def close(self) -> None: pass

class NotANotifier: # Not inheriting from BaseNotifierV1
    notifier_type = "invalid" # For config lookup
    def __init__(self, config: dict): pass


class TestNotificationPluginLoader(unittest.TestCase):

    def setUp(self):
        # Base settings for plugin loader to use.
        # MVP_NOTIFIERS_CONFIG is what the loader will iterate through.
        # NOTIFIER_PLUGIN_DIR is used to construct module import paths.
        self.patch_settings = patch('app.plugin_loader.settings', MagicMock(spec=Settings))
        self.mock_settings = self.patch_settings.start()
        self.mock_settings.NOTIFIER_PLUGIN_DIR = "mock_notifiers_package" # Python import path base

        # This is the list of NotifierPluginConfig that the loader will use
        self.patch_mvp_config = patch('app.plugin_loader.MVP_NOTIFIERS_CONFIG', []) # Start with empty
        self.mock_mvp_config_list = self.patch_mvp_config.start()


    def tearDown(self):
        self.patch_settings.stop()
        self.patch_mvp_config.stop()


    @patch('importlib.import_module')
    def test_load_successful_notifiers_with_config(self, mock_import_module):
        # Configure MVP_NOTIFIERS_CONFIG for this test
        self.mock_mvp_config_list.extend([
            NotifierPluginConfig(type="type_a", enabled=True, config={"key1": "value1"}),
            NotifierPluginConfig(type="type_b", enabled=True, config={"key2": "value2"}),
        ])
        
        # --- Mocking importlib.import_module and getattr ---
        def import_module_side_effect(module_path):
            mock_module = MagicMock()
            if module_path == "mock_notifiers_package.type_a_notifier": # Inferred module name
                # The loader uses getattr(module, ClassName)
                mock_module.TypeANotifier = MockNotifierTypeA # Inferred class name
            elif module_path == "mock_notifiers_package.type_b_notifier":
                mock_module.TypeBNotifier = MockNotifierTypeB
            else:
                raise ImportError(f"Test mock cannot import {module_path}")
            return mock_module
        
        mock_import_module.side_effect = import_module_side_effect
        
        loaded_notifiers = load_notifiers(
            plugin_dir_setting=self.mock_settings.NOTIFIER_PLUGIN_DIR,
            configured_notifiers=self.mock_mvp_config_list
        )

        self.assertEqual(len(loaded_notifiers), 2)
        self.assertIn("type_a", loaded_notifiers)
        self.assertIsInstance(loaded_notifiers["type_a"], MockNotifierTypeA)
        self.assertEqual(loaded_notifiers["type_a"].initialized_with_specific_settings, {"key1": "value1"})
        
        self.assertIn("type_b", loaded_notifiers)
        self.assertIsInstance(loaded_notifiers["type_b"], MockNotifierTypeB)
        self.assertEqual(loaded_notifiers["type_b"].specific_settings_received, {"key2": "value2"})

    @patch('importlib.import_module')
    def test_load_notifier_disabled_in_config(self, mock_import_module):
        self.mock_mvp_config_list.append(
            NotifierPluginConfig(type="type_a", enabled=False, config={})
        )
        
        # import_module should not even be called if it's disabled first
        loaded_notifiers = load_notifiers(
            plugin_dir_setting=self.mock_settings.NOTIFIER_PLUGIN_DIR,
            configured_notifiers=self.mock_mvp_config_list
        )
        self.assertEqual(len(loaded_notifiers), 0)
        mock_import_module.assert_not_called()

    @patch('importlib.import_module', side_effect=ImportError("Simulated module import failure"))
    def test_import_module_failure(self, mock_import_module):
        self.mock_mvp_config_list.append(
            NotifierPluginConfig(type="type_fail_import", enabled=True, config={})
        )
        
        with self.assertLogs(logger='app.plugin_loader', level='ERROR') as cm:
            loaded_notifiers = load_notifiers(
                plugin_dir_setting=self.mock_settings.NOTIFIER_PLUGIN_DIR,
                configured_notifiers=self.mock_mvp_config_list
            )
        
        self.assertEqual(len(loaded_notifiers), 0)
        self.assertTrue(any("Failed to import notifier module" in log for log in cm.output))

    @patch('importlib.import_module')
    def test_load_class_not_found_in_module(self, mock_import_module):
        self.mock_mvp_config_list.append(
             NotifierPluginConfig(type="type_class_missing", enabled=True, class_name="NonExistentNotifier", config={})
        )

        mock_module = MagicMock()
        # Simulate the module not having the class 'NonExistentNotifier'
        # getattr(mock_module, "NonExistentNotifier", None) will return None
        mock_import_module.return_value = mock_module
        
        with self.assertLogs(logger='app.plugin_loader', level='ERROR') as cm:
            loaded_notifiers = load_notifiers(
                plugin_dir_setting=self.mock_settings.NOTIFIER_PLUGIN_DIR,
                configured_notifiers=self.mock_mvp_config_list
            )

        self.assertEqual(len(loaded_notifiers), 0)
        self.assertTrue(any("Class 'NonExistentNotifier' not found" in log for log in cm.output))


    @patch('importlib.import_module')
    def test_load_invalid_notifier_class_type(self, mock_import_module):
        # Configure a notifier type whose class does not inherit from BaseNotifierV1
        self.mock_mvp_config_list.append(
            NotifierPluginConfig(type="invalid_type", enabled=True, class_name="NotANotifierClass", config={})
        )

        mock_module_invalid = MagicMock()
        mock_module_invalid.NotANotifierClass = NotANotifier # This class does not inherit BaseNotifierV1
        mock_import_module.return_value = mock_module_invalid
        
        with self.assertLogs(logger='app.plugin_loader', level='ERROR') as cm:
            loaded_notifiers = load_notifiers(
                plugin_dir_setting=self.mock_settings.NOTIFIER_PLUGIN_DIR,
                configured_notifiers=self.mock_mvp_config_list
            )
        
        self.assertEqual(len(loaded_notifiers), 0)
        self.assertTrue(any("does not correctly implement BaseNotifierV1" in log for log in cm.output))

    def test_load_no_notifiers_configured(self):
        # MVP_NOTIFIERS_CONFIG is empty by default in setUp for this test
        with self.assertLogs(logger='app.plugin_loader', level='WARNING') as cm:
            loaded_notifiers = load_notifiers(
                plugin_dir_setting=self.mock_settings.NOTIFIER_PLUGIN_DIR,
                configured_notifiers=self.mock_mvp_config_list # Empty list
            )
        self.assertEqual(len(loaded_notifiers), 0)
        self.assertTrue(any("No notifiers configured to be loaded." in log for log in cm.output))


if __name__ == "__main__":
    unittest.main()
