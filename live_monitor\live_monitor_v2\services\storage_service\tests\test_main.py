import unittest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from typing import Dict, Any # For type hinting

from app.main import app # Main FastAPI app
from app.database import Base, get_db # To override get_db
from app.schemas import SubscriptionSettings, TemplateTypeEnum # For PUT request and template type

# --- Test Database Setup (In-memory SQLite) ---
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db" # Use a unique name for this test DB
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Dependency override for tests
def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

class TestStorageServiceAPI(unittest.TestCase):

    @classmethod
    def setUpClass(cls):
        Base.metadata.create_all(bind=engine)
        cls.client = TestClient(app)

    @classmethod
    def tearDownClass(cls):
        Base.metadata.drop_all(bind=engine)

    def setUp(self):
        Base.metadata.drop_all(bind=engine) 
        Base.metadata.create_all(bind=engine)

    # --- Subscription Tests ---
    def test_create_and_get_subscription(self):
        user_id = "test_user_sub"
        response = self.client.post(
            f"/users/{user_id}/subscriptions",
            json={"user_id": user_id, "platform_name": "bilibili", "room_id": "123", 
                  "settings": {"notify_live_start": True}}
        )
        self.assertEqual(response.status_code, 201)
        data = response.json()
        self.assertEqual(data["platform_name"], "bilibili")
        self.assertEqual(data["room_id"], "123")
        self.assertEqual(data["user_id"], user_id)
        self.assertTrue(data["settings"]["notify_live_start"])
        subscription_id = data["id"]

        response = self.client.get(f"/users/{user_id}/subscriptions")
        self.assertEqual(response.status_code, 200)
        data_list = response.json()
        self.assertEqual(len(data_list), 1)
        self.assertEqual(data_list[0]["id"], subscription_id)

    def test_delete_subscription(self):
        user_id = "test_user_del_sub"
        response_create = self.client.post(
            f"/users/{user_id}/subscriptions",
            json={"user_id": user_id, "platform_name": "douyu", "room_id": "456"}
        )
        self.assertEqual(response_create.status_code, 201)
        subscription_id = response_create.json()["id"]

        response_delete = self.client.delete(f"/users/{user_id}/subscriptions/{subscription_id}")
        self.assertEqual(response_delete.status_code, 204)

        response_get = self.client.get(f"/users/{user_id}/subscriptions")
        self.assertEqual(response_get.status_code, 200)
        self.assertEqual(len(response_get.json()), 0)
        
        response_delete_again = self.client.delete(f"/users/{user_id}/subscriptions/{subscription_id}")
        self.assertEqual(response_delete_again.status_code, 404)

    def test_update_subscription_settings(self):
        user_id = "test_user_update_sub"
        response_create = self.client.post(
            f"/users/{user_id}/subscriptions",
            json={"user_id": user_id, "platform_name": "huya", "room_id": "789",
                  "settings": {"notify_live_start": True, "notify_live_end": False}}
        )
        self.assertEqual(response_create.status_code, 201)
        subscription_id = response_create.json()["id"]

        new_settings = {"notify_live_start": False, "notify_live_end": True, "notify_title_change": True}
        response_update = self.client.put(
            f"/users/{user_id}/subscriptions/{subscription_id}",
            json=new_settings
        )
        self.assertEqual(response_update.status_code, 200)
        updated_data = response_update.json()
        self.assertEqual(updated_data["settings"]["notify_live_start"], False)
        self.assertEqual(updated_data["settings"]["notify_live_end"], True)
        self.assertEqual(updated_data["settings"]["notify_title_change"], True)

    # --- Notification Channel Tests ---
    def test_set_and_get_notification_channel(self):
        user_id = "test_user_chan"
        channel_type = "wxpusher"
        response_put = self.client.put(
            f"/users/{user_id}/notification_channels/{channel_type}",
            json={"user_id": user_id, "channel_type": channel_type, "channel_uid": "UID_ABC123"}
        )
        self.assertEqual(response_put.status_code, 200)
        data_put = response_put.json()
        self.assertEqual(data_put["user_id"], user_id)
        self.assertEqual(data_put["channel_type"], channel_type)
        self.assertEqual(data_put["channel_uid"], "UID_ABC123")
        channel_id = data_put["id"]

        response_get_specific = self.client.get(f"/users/{user_id}/notification_channels/{channel_type}")
        self.assertEqual(response_get_specific.status_code, 200)
        data_get_specific = response_get_specific.json()
        self.assertEqual(data_get_specific["id"], channel_id)
        self.assertEqual(data_get_specific["channel_uid"], "UID_ABC123")

        response_get_list = self.client.get(f"/users/{user_id}/notification_channels")
        self.assertEqual(response_get_list.status_code, 200)
        data_list = response_get_list.json()
        self.assertEqual(len(data_list), 1)
        self.assertEqual(data_list[0]["id"], channel_id)

        response_put_update = self.client.put(
            f"/users/{user_id}/notification_channels/{channel_type}",
            json={"user_id": user_id, "channel_type": channel_type, "channel_uid": "UID_XYZ789"}
        )
        self.assertEqual(response_put_update.status_code, 200)
        data_put_update = response_put_update.json()
        self.assertEqual(data_put_update["channel_uid"], "UID_XYZ789")
        self.assertEqual(data_put_update["id"], channel_id) 

    # --- Internal Endpoint Test ---
    def test_get_all_active_subscriptions_for_monitoring(self):
        user1 = "user_monitor_1"
        user2 = "user_monitor_2"
        self.client.post(f"/users/{user1}/subscriptions", json={"user_id": user1, "platform_name": "bilibili", "room_id": "r1"})
        self.client.post(f"/users/{user1}/subscriptions", json={"user_id": user1, "platform_name": "douyu", "room_id": "r2"})
        self.client.post(f"/users/{user2}/subscriptions", json={"user_id": user2, "platform_name": "huya", "room_id": "r3"})

        response = self.client.get("/internal/subscriptions/all_active")
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        self.assertIn(user1, data)
        self.assertIn(user2, data)
        self.assertEqual(len(data[user1]), 2)
        self.assertEqual(len(data[user2]), 1)
        self.assertEqual(data[user2][0]["platform_name"], "huya")

    def test_health_check(self):
        response = self.client.get("/health")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"status": "ok"})

    # --- Message Template Tests ---
    def test_create_message_template(self):
        template_data = {
            "name": "Global Live Start",
            "template_content": "Streamer {{ streamer_info.anchor_name }} is live!",
            "template_type": "live_start", # Uses Enum value
            "user_id": None 
        }
        response = self.client.post("/templates", json=template_data)
        self.assertEqual(response.status_code, 201)
        data = response.json()
        self.assertEqual(data["name"], template_data["name"])
        self.assertEqual(data["template_content"], template_data["template_content"])
        self.assertEqual(data["template_type"], template_data["template_type"])
        self.assertIsNone(data["user_id"])
        self.assertIn("id", data)

    def test_get_message_template_by_id(self):
        # Create a template first
        template_data = {
            "name": "Test Get By ID", "template_content": "Content", 
            "template_type": TemplateTypeEnum.live_start.value, "user_id": "user123"
        }
        create_response = self.client.post("/templates", json=template_data)
        template_id = create_response.json()["id"]

        # Get the template
        response = self.client.get(f"/templates/{template_id}")
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data["id"], template_id)
        self.assertEqual(data["name"], template_data["name"])

    def test_list_message_templates(self):
        # Create some templates
        self.client.post("/templates", json={
            "name": "Global Live Start", "template_content": "Global content", 
            "template_type": "live_start", "user_id": None
        })
        self.client.post("/templates", json={
            "name": "User1 Live Start", "template_content": "User1 content", 
            "template_type": "live_start", "user_id": "user1"
        })
        self.client.post("/templates", json={
            "name": "User1 Title Change", "template_content": "User1 title change", 
            "template_type": "title_change", "user_id": "user1"
        })

        # List all global live_start templates
        response_global_live_start = self.client.get("/templates?template_type=live_start")
        self.assertEqual(response_global_live_start.status_code, 200)
        data_global_ls = response_global_live_start.json()
        self.assertEqual(len(data_global_ls), 1)
        self.assertEqual(data_global_ls[0]["name"], "Global Live Start")

        # List all user1 templates
        response_user1 = self.client.get("/templates?user_id=user1")
        self.assertEqual(response_user1.status_code, 200)
        self.assertEqual(len(response_user1.json()), 2)

        # List user1 live_start templates
        response_user1_ls = self.client.get("/templates?user_id=user1&template_type=live_start")
        self.assertEqual(response_user1_ls.status_code, 200)
        data_user1_ls = response_user1_ls.json()
        self.assertEqual(len(data_user1_ls), 1)
        self.assertEqual(data_user1_ls[0]["name"], "User1 Live Start")
        
        # List all templates (no filter) - should be 3 total
        response_all = self.client.get("/templates")
        self.assertEqual(response_all.status_code, 200)
        # Note: The CRUD `get_message_templates_by_user_and_type` with user_id=None filters for global.
        # The API endpoint for /templates with no user_id query param means user_id in CRUD is None, so it lists global.
        # If we want to list ALL templates regardless of user, the CRUD/API needs adjustment.
        # For now, this tests listing global templates.
        # To test listing ALL, we'd need a different API or modify current one.
        # The current behavior for GET /templates (no params) is to list global templates. Let's test that.
        self.assertEqual(len(response_all.json()), 1) # Should be 1 global template
        self.assertEqual(response_all.json()[0]["name"], "Global Live Start")


    def test_update_message_template(self):
        template_data = {
            "name": "Initial Name", "template_content": "Initial content", 
            "template_type": "live_end", "user_id": "user_update"
        }
        create_response = self.client.post("/templates", json=template_data)
        template_id = create_response.json()["id"]

        update_payload: Dict[str, Any] = {"name": "Updated Name", "template_content": "Updated content"}
        response = self.client.put(f"/templates/{template_id}", json=update_payload)
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data["name"], update_payload["name"])
        self.assertEqual(data["template_content"], update_payload["template_content"])
        self.assertEqual(data["template_type"], template_data["template_type"]) # Type not updated

    def test_delete_message_template(self):
        template_data = {
            "name": "To Be Deleted", "template_content": "Delete me", 
            "template_type": "title_change"
        }
        create_response = self.client.post("/templates", json=template_data)
        template_id = create_response.json()["id"]

        delete_response = self.client.delete(f"/templates/{template_id}")
        self.assertEqual(delete_response.status_code, 204)

        get_response = self.client.get(f"/templates/{template_id}")
        self.assertEqual(get_response.status_code, 404)

if __name__ == "__main__":
    unittest.main()
