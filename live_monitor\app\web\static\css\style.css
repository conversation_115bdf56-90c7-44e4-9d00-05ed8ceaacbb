/* 现代化CSS变量 */
:root {
    /* 主色调 */
    --primary-color: #4361ee;
    --primary-light: #4895ef;
    --primary-dark: #3f37c9;

    /* 辅助色 */
    --success-color: #4cc9f0;
    --warning-color: #f72585;
    --danger-color: #e63946;
    --info-color: #457b9d;

    /* 中性色 */
    --background-color: #f8f9fa;
    --card-background: #ffffff;
    --text-color: #212529;
    --text-secondary: #6c757d;
    --border-color: #dee2e6;
    --divider-color: #e9ecef;

    /* 暗色主题 */
    --dark-background: #121212;
    --dark-card: #1e1e1e;
    --dark-text: #e0e0e0;
    --dark-text-secondary: #a0a0a0;
    --dark-border: #333333;

    /* 尺寸 */
    --border-radius: 10px;
    --card-border-radius: 12px;
    --button-border-radius: 8px;

    /* 阴影 */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);

    /* 动画 */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.5;
    font-size: 16px;
    min-height: 100vh;
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* 应用容器 */
.app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* 主内容区域 */
.main-content {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
    padding: 20px;
    flex: 1;
}

/* 头部样式 */
.header {
    background: var(--card-background);
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
    transition: background-color var(--transition-normal), box-shadow var(--transition-normal);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

/* 移除了主题切换按钮样式 */

/* 统计卡片容器 */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
}

.stat-card {
    background: var(--card-background);
    border-radius: var(--card-border-radius);
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    box-shadow: var(--shadow-sm);
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: rgba(67, 97, 238, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.stat-icon.live {
    background: rgba(76, 201, 240, 0.1);
    color: var(--success-color);
}

.stat-info h3 {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.stat-info p {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
}

/* 订阅面板 */
.subscription-panel {
    background: var(--card-background);
    border-radius: var(--card-border-radius);
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--shadow-sm);
}

.panel-header {
    margin-bottom: 20px;
}

.panel-header h2 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.panel-header h2 i {
    color: var(--primary-color);
}

.add-subscription-form {
    display: flex;
    gap: 16px;
    align-items: flex-end;
}

.form-group {
    flex: 1;
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border-radius: var(--button-border-radius);
    border: 1px solid var(--border-color);
    font-size: 1rem;
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

/* 筛选和排序 */
.filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 24px;
    background: var(--card-background);
    border-radius: var(--card-border-radius);
    padding: 16px 24px;
    box-shadow: var(--shadow-sm);
    align-items: center;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-group label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.filter-select {
    padding: 8px 12px;
    border-radius: var(--button-border-radius);
    border: 1px solid var(--border-color);
    background: var(--background-color);
    font-size: 0.9rem;
    color: var(--text-color);
    cursor: pointer;
    transition: border-color var(--transition-fast);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* 主播列表容器 */
.streamer-container {
    margin-bottom: 24px;
}

.section-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 主播卡片网格布局 */
.streamer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 24px;
}

/* 主播卡片样式 */
.streamer-card {
    background: var(--card-background);
    border-radius: var(--card-border-radius);
    box-shadow: var(--shadow-sm);
    padding: 24px;
    transition: all var(--transition-fast);
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    gap: 16px;
    position: relative;
    overflow: hidden;
    height: 100%;
}

.streamer-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-3px);
}

.streamer-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--primary-color);
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.streamer-card:hover::before {
    opacity: 1;
}

.streamer-card.live::before {
    background: var(--success-color);
    opacity: 1;
}

.streamer-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.streamer-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.streamer-platform {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: var(--background-color);
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
}

.streamer-name {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
    line-height: 1.3;
}

.streamer-title {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    background: var(--background-color);
    padding: 12px;
    border-radius: var(--border-radius);
    border-left: 3px solid var(--primary-color);
    word-break: break-word;
}

/* 直播状态样式 */
.streamer-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 8px 0;
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: var(--text-secondary);
    transition: all var(--transition-normal);
}

.status-indicator.live {
    background: var(--success-color);
    box-shadow: 0 0 0 2px rgba(76, 201, 240, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(76, 201, 240, 0.5);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(76, 201, 240, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(76, 201, 240, 0);
    }
}

.status-text {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
}

.status-text.live {
    color: var(--success-color);
}

/* 按钮容器 */
.card-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-top: auto;
}

.card-buttons .button {
    width: 100%;
    justify-content: center;
    padding: 10px 0;
}

.card-buttons .button.primary {
    grid-column: 1 / -1; /* 跨越所有列 */
}

/* 分隔线 */
.card-divider {
    height: 1px;
    background: var(--divider-color);
    margin: 8px 0;
    width: 100%;
}

/* 按钮样式 */
.button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 18px;
    border-radius: var(--button-border-radius);
    font-size: 14px;
    font-weight: 500;
    transition: all var(--transition-fast);
    cursor: pointer;
    border: none;
    gap: 8px;
    text-decoration: none;
}

.button i {
    font-size: 0.9em;
}

.button.primary {
    background: var(--primary-color);
    color: white;
}

.button.primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.button.secondary {
    background: var(--background-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.button.secondary:hover {
    background: var(--divider-color);
    transform: translateY(-1px);
}

.button.danger {
    background: var(--danger-color);
    color: white;
}

.button.danger:hover {
    background: #ff5a65;
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

/* 移除了暗色模式下的按钮样式 */

/* 订阅管理面板 */
.subscription-panel {
    background: var(--card-background);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
}

.subscription-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.subscription-title {
    font-size: 1.2rem;
    font-weight: 500;
}

/* 添加订阅表单 */
.add-subscription-form {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
}

.form-input {
    flex: 1;
    padding: 8px 12px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    font-size: 0.9rem;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* 加载动画 */
.loading {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 40px 20px;
    gap: 16px;
    width: 100%;
}

.loading p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--background-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 空状态样式 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    color: var(--text-secondary);
    width: 100%;
    background: var(--card-background);
    border-radius: var(--card-border-radius);
    box-shadow: var(--shadow-sm);
}

.empty-state i {
    font-size: 3.5rem;
    margin-bottom: 20px;
    opacity: 0.4;
    color: var(--primary-color);
}

.empty-state p {
    font-size: 1.1rem;
    max-width: 400px;
    line-height: 1.5;
}

.dialog-subtitle {
    font-size: 0.8rem;
    font-weight: normal;
    color: var(--text-secondary);
    margin-top: 4px;
}

/* 移除了页脚样式 */

/* 通知样式 */
.notification {
    position: fixed;
    bottom: 24px;
    right: 24px;
    background: var(--card-background);
    border-radius: var(--card-border-radius);
    box-shadow: var(--shadow-lg);
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 16px;
    z-index: 1000;
    max-width: 400px;
    border-left: 4px solid var(--primary-color);
    animation: slideIn 0.3s ease-out forwards;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(67, 97, 238, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--primary-color);
}

.notification-content {
    flex: 1;
}

.notification-message {
    font-size: 0.9rem;
    color: var(--text-color);
    line-height: 1.5;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 1rem;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color var(--transition-fast);
}

.notification-close:hover {
    color: var(--text-color);
}

/* 对话框样式 */
.dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.dialog {
    background: var(--card-background);
    border-radius: var(--card-border-radius);
    box-shadow: var(--shadow-lg);
    padding: 24px;
    width: 400px;
    max-width: 90vw;
    max-height: 85vh; /* 限制最大高度 */
    overflow-y: auto; /* 添加滚动条 */
    animation: dialogSlideIn 0.3s ease-out;
}

.settings-dialog {
    width: 450px;
}

/* 移动端设置对话框样式 */
@media (max-width: 768px) {
    .dialog {
        padding: 16px;
        max-height: 80vh;
    }

    .settings-dialog {
        width: 100%;
    }

    .dialog-title {
        font-size: 1.1rem;
        margin-bottom: 12px;
    }

    .dialog-buttons {
        position: sticky;
        bottom: 0;
        background: var(--card-background);
        padding-top: 12px;
        margin-top: 16px;
        z-index: 10;
    }
}

@keyframes dialogSlideIn {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.dialog-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-color);
}

.dialog-message {
    font-size: 0.95rem;
    line-height: 1.5;
    color: var(--text-secondary);
    margin-bottom: 24px;
}

.dialog-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
}

/* 设置对话框样式 */
.settings-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.setting-item {
    display: flex;
    align-items: center;
    gap: 16px;
}

.setting-label {
    font-size: 0.95rem;
    color: var(--text-color);
}

/* 移动端设置项样式 */
@media (max-width: 768px) {
    .settings-form {
        gap: 16px;
    }

    .setting-item {
        gap: 12px;
    }

    .setting-label {
        font-size: 0.9rem;
    }
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--border-color);
    transition: .3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .3s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(20px);
}

/* 时间选择器 */
.quiet-hours-settings {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 16px;
    background: var(--background-color);
    border-radius: var(--border-radius);
    margin-top: 12px;
    border-left: 3px solid var(--primary-color);
    transition: all var(--transition-normal);
}

.quiet-hours-settings.hidden {
    display: none;
}

.quiet-hours-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.quiet-hours-header h4 {
    font-size: 0.95rem;
    font-weight: 500;
    color: var(--text-color);
    margin: 0;
}

.quiet-hours-description {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin-bottom: 16px;
    line-height: 1.4;
}

.time-range-visual {
    height: 6px;
    background: var(--divider-color);
    border-radius: 3px;
    margin: 8px 0 16px;
    position: relative;
    overflow: hidden;
}

@media (max-width: 768px) {
    .time-range-visual {
        margin: 4px 0 12px;
    }
}

.time-range-active {
    position: absolute;
    height: 100%;
    background: var(--primary-color);
    border-radius: 3px;
    transition: all var(--transition-normal);
}

.time-pickers-container {
    display: flex;
    gap: 16px;
    align-items: center;
}

.time-picker {
    display: flex;
    flex-direction: column;
    gap: 6px;
    flex: 1;
}

.time-picker label {
    font-size: 0.85rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.time-picker input {
    padding: 10px 12px;
    border-radius: var(--button-border-radius);
    border: 1px solid var(--border-color);
    background: var(--card-background);
    font-size: 1rem;
    transition: all var(--transition-fast);
    -webkit-appearance: none; /* 移除iOS默认样式 */
    appearance: none;
    max-width: 100%;
    width: 100%;
}

.time-picker input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

.time-range-separator {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-top: 22px;
}

.quiet-hours-presets {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
}

.preset-button {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--button-border-radius);
    padding: 8px 12px;
    font-size: 0.85rem;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.preset-button:hover {
    background: var(--background-color);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* 移除了暗色主题相关样式 */

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        padding: 12px;
    }

    .subscription-panel {
        padding: 16px;
        margin-bottom: 16px;
    }

    .panel-header h2 {
        font-size: 1.1rem;
    }

    .header {
        padding: 12px 16px;
    }

    .logo h1 {
        font-size: 1.2rem;
    }

    .stats-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .stat-card {
        padding: 12px;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .stat-info h3 {
        font-size: 0.8rem;
    }

    .stat-info p {
        font-size: 1.2rem;
    }

    .streamer-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .streamer-card {
        padding: 16px;
        gap: 12px;
    }

    .streamer-name {
        font-size: 16px;
    }

    .streamer-title {
        padding: 10px;
        font-size: 13px;
    }

    .add-subscription-form {
        flex-direction: column;
        gap: 12px;
    }

    .form-group {
        width: 100%;
    }

    .filter-container {
        flex-direction: column;
        align-items: stretch;
        padding: 12px 16px;
        gap: 12px;
    }

    .filter-group {
        width: 100%;
        justify-content: space-between;
    }

    .filter-select {
        width: 70%;
        flex: 1;
    }

    .button {
        width: 100%;
    }

    .card-buttons {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .card-buttons .button {
        padding: 12px 0;
    }

    .quiet-hours-settings {
        padding: 12px;
        margin-top: 8px;
        border-left-width: 2px;
    }

    .quiet-hours-header h4 {
        font-size: 0.9rem;
    }

    .quiet-hours-description {
        font-size: 0.8rem;
        margin-bottom: 12px;
    }

    .time-pickers-container {
        flex-direction: column;
        gap: 16px;
    }

    .time-picker {
        width: 100%;
    }

    .time-picker input {
        height: 44px;
        font-size: 16px; /* 防止iOS缩放 */
        padding: 12px;
    }

    .time-range-separator {
        display: none;
    }

    .quiet-hours-presets {
        justify-content: center;
        gap: 10px;
    }

    .preset-button {
        padding: 10px 14px;
        font-size: 0.9rem;
        flex: 1;
        text-align: center;
        min-width: 120px;
    }

    /* 移动端通知样式 */
    .notification {
        bottom: 12px;
        right: 12px;
        left: 12px;
        max-width: none;
        padding: 12px;
    }

    .notification-icon {
        width: 32px;
        height: 32px;
        font-size: 1rem;
    }
}

/* 超小屏幕设备的样式 */
@media (max-width: 480px) {
    .stats-container {
        grid-template-columns: 1fr;
    }

    .logo i {
        font-size: 1.2rem;
    }

    .logo h1 {
        font-size: 1rem;
    }

    .header {
        padding: 10px;
    }

    .streamer-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 6px;
    }

    .streamer-platform {
        font-size: 10px;
        padding: 3px 6px;
    }

    .quiet-hours-presets {
        flex-direction: column;
        align-items: stretch;
    }

    .preset-button {
        width: 100%;
        min-width: 0;
        padding: 12px;
    }
}
