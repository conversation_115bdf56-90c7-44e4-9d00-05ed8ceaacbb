/* Basic Reset or Normalization (optional, can be more comprehensive) */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  font-weight: normal;
}

body {
  min-height: 100vh;
  color: var(--color-text);
  background: var(--color-background);
  transition:
    color 0.5s,
    background-color 0.5s;
  line-height: 1.6;
  font-family:
    Inter,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Fira Sans',
    'Droid Sans',
    'Helvetica Neue',
    sans-serif;
  font-size: 15px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Define some CSS variables for theming (example) */
:root {
  --color-background: #ffffff;
  --color-background-soft: #f8f8f8;
  --color-background-mute: #f2f2f2;

  --color-border: #e2e2e2;
  --color-border-hover: #cccccc;

  --color-heading: #2c3e50;
  --color-text: #333333;

  --color-primary: #3498db; /* Example primary color */
  --color-primary-hover: #2980b9;

  --section-gap: 160px; /* Example variable */
}

/* Basic link styling */
a {
  text-decoration: none;
  color: var(--color-primary);
  transition: 0.4s;
}

@media (hover: hover) {
  a:hover {
    color: var(--color-primary-hover);
  }
}

/* Utility classes (optional) */
.container {
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

h1, h2, h3, h4, h5, h6 {
  color: var(--color-heading);
}

/* Add more global styles or component-specific styles in their respective files */
