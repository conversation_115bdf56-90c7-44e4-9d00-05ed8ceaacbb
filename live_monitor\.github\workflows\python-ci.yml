name: Python Application CI

on:
  push:
    branches: [ main ] # Adjust if your main branch is named differently (e.g., master)
  pull_request:
    branches: [ main ] # Adjust if your main branch is named differently

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4 # Use a more recent version of checkout
    - name: Set up Python
      uses: actions/setup-python@v4 # Use a more recent version of setup-python
      with:
        python-version: '3.10' # Specify a Python version, e.g., 3.10
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    - name: Lint with Flake8
      run: |
        # stop the build if there are Python syntax errors or undefined names
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

  test:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false # Allow all matrix jobs to complete even if one fails
      matrix:
        python-version: ['3.9', '3.10', '3.11'] # Test on multiple Python versions

    steps:
    - uses: actions/checkout@v4
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    - name: Test with unittest
      run: |
        python -m unittest discover tests
