# Live Monitor v2 (MVP)

This is the Minimum Viable Product (MVP) of a new, microservices-based version of the Live Monitor application, now with enhanced features from Phase 2.

## Overview

This application consists of the following services:
- **API Gateway (`api_gateway_service`):** Exposes external APIs (e.g., for managing subscriptions and message templates).
- **Storage Service (`storage_service`):** Manages data persistence (subscriptions, user notification channels, message templates) using PostgreSQL.
- **Monitoring Service (`monitoring_service`):** Monitors multiple platforms (Bilibili, Douyu, Huya via plugins) for streamer status changes and publishes events to a message broker.
- **Notification Service (`notification_service`):** Consumes events from the message broker and sends notifications via WxPusher and Email (example), using customizable message templates.
- **Web UI (`web_app_service`):** Provides a user interface for managing subscriptions.

Backend dependencies (PostgreSQL, Redis, RabbitMQ) are also managed via Docker Compose.

## Prerequisites

- Docker
- Docker Compose

## How to Run the Application

1.  **Environment Configuration:**
    *   Navigate to `live_monitor_v2/deployment/docker-compose/`.
    *   You will need to create a `.env` file in this directory or directly edit `docker-compose.dev.yml` to set necessary environment variables, particularly:
        *   `LIVE_MONITOR_WXPUSHER_APP_TOKEN`: Your WxPusher Application Token (for the `notification_service`).
        *   **Example Email Notifier Configuration (add to your `.env` or `docker-compose.dev.yml` if testing email notifications):**
            ```
            LIVE_MONITOR_EMAIL_SMTP_HOST="smtp.example.com"
            LIVE_MONITOR_EMAIL_SMTP_PORT="587"
            LIVE_MONITOR_EMAIL_SMTP_USER="<EMAIL>"
            LIVE_MONITOR_EMAIL_SMTP_PASSWORD="your_smtp_password"
            LIVE_MONITOR_EMAIL_SENDER_EMAIL="<EMAIL>"
            LIVE_MONITOR_EMAIL_USE_TLS="true"
            # LIVE_MONITOR_EMAIL_USE_SSL="false" # Only set one of TLS/SSL to true
            ```
        *   Ensure other environment variables for service URLs are correctly pointing to Docker Compose service names (e.g., `STORAGE_SERVICE_URL=http://storage_service:8000`). The provided `docker-compose.dev.yml` generally handles these inter-service URLs.
    *   **Platform Adapters**: The Monitoring Service now includes adapters for Bilibili, Douyu, and Huya. These are enabled by default in the MVP but can be configured (enabled/disabled, specific settings) via the Monitoring Service's own configuration if needed (not typically top-level .env variables for simple enablement).

2.  **Start Services:**
    Navigate to `live_monitor_v2/deployment/docker-compose/` in your terminal and run:
    ```bash
    docker-compose -f docker-compose.dev.yml up --build
    ```
    This will build the images for each service (if not already built) and start all services and their dependencies.

3.  **Apply Database Migrations:**
    The `storage_service` is configured to run Alembic migrations on startup. Check its logs to ensure this was successful:
    ```bash
    docker-compose logs storage_service
    ```

## Using the Application

- **API Gateway:** Available at `http://localhost:8080` (by default).
- **API Documentation (Swagger UI):** Once the API Gateway is running, its OpenAPI documentation should be available at `http://localhost:8080/docs`. This interface allows you to explore and interact with all available endpoints.

### Web Interface

The application now includes a web interface, typically available at `http://localhost:8081` (if using default Docker Compose port).
To use it:
1.  Enter a `User ID` on the main page and click "Set User ID".
2.  Once the User ID is set, you can:
    *   View your current subscriptions.
    *   Add new subscriptions by specifying the platform (e.g., `bilibili`, `douyu`, `huya`) and the Room ID.
    *   Delete existing subscriptions.

### Customizable Message Templates

The format of notifications can now be customized using message templates. Default templates (hardcoded in the Notification Service) are used if no custom ones are set up for a given user/event type.
You can manage templates via the API. Key endpoints (available at the API Gateway, e.g., `http://localhost:8080/docs`):
- `POST /api/v2/templates`: Create a new global or user-specific template.
- `GET /api/v2/templates?user_id={user_id}&template_type={event_type}`: List templates.
- `PUT /api/v2/templates/{template_id}`: Update a template.
- `DELETE /api/v2/templates/{template_id}`: Delete a template.

Templates use Jinja2 syntax and receive event data (like `streamer_info` which includes `anchor_name`, `title`, `platform_name`, `platform_display_name`, etc.) as context.

**Example Usage Flow:**
1. Set your WxPusher App Token (and optionally Email SMTP details like `LIVE_MONITOR_EMAIL_SMTP_HOST`, etc.) in the environment configuration for the `notification_service`.
2. Start all services using `docker-compose -f docker-compose.dev.yml up --build`.
3. Access the Web UI at `http://localhost:8081`. Set your User ID (e.g., `my_user`).
4. Add your WxPusher UID (or email address if testing EmailNotifier) via the API Gateway's `/docs` page:
   `PUT /api/v2/users/my_user/notification_channels/wxpusher` with payload `{"user_id": "my_user", "channel_type": "wxpusher", "channel_uid": "YOUR_WXPUSHER_UID"}`.
   Or for email:
   `PUT /api/v2/users/my_user/notification_channels/email` with payload `{"user_id": "my_user", "channel_type": "email", "channel_uid": "<EMAIL>"}`.
5. (Optional) Add a custom message template via the API Gateway for a specific event type (e.g., `live_start` for `user_id: "my_user"`).
   `POST /api/v2/templates` with payload like:
   ```json
   {
     "name": "My Custom Live Start",
     "template_content": "Hey! {{ streamer_info.anchor_name }} on {{ streamer_info.platform_display_name }} is LIVE with title: {{ streamer_info.title }}! Watch here: {{ streamer_info.stream_url }}",
     "template_type": "live_start",
     "user_id": "my_user"
   }
   ```
6. In the Web UI, subscribe your User ID (`my_user`) to a Bilibili, Douyu, or Huya streamer's room ID.
7. When the `monitoring_service` detects a status change for that streamer, it publishes an event.
8. The `notification_service` consumes this event, attempts to fetch a custom template (user-specific, then global for the event type), renders it (or uses a hardcoded fallback), and sends a notification via WxPusher (and/or Email if configured and a channel exists for the user).

## Stopping the Services

In the `live_monitor_v2/deployment/docker-compose/` directory, run:
```bash
docker-compose -f docker-compose.dev.yml down
```
