import asyncio
import unittest
from unittest.mock import patch, AsyncMock, MagicMock, call
import json
from datetime import datetime, timezone
import aio_pika # For creating a mock IncomingMessage
import httpx # For RequestError
from jinja2 import TemplateSyntaxError # For testing template errors

# Mo<PERSON>les to test or mock from app.main of notification_service
from app.main import process_event_message, get_user_wxpusher_uid, fetch_template_from_storage, jinja_env
# We will patch global variables like http_storage_client and active_notifiers where they are used.

from app.config import settings # For STORAGE_SERVICE_URL
from libs.shared_models.events import LiveStatusChangedEvent, StreamerInfo
from libs.plugin_interfaces.base_notifier_v1 import BaseNotifierV1 # For mocking

# --- Mock Setup ---

class MockNotificationNotifier(BaseNotifierV1): # Renamed to avoid conflict
    def __init__(self, notifier_type: str, config = None):
        super().__init__(notifier_type, config)
        self.send_mock = AsyncMock()
        self.init_called = False
        self.closed_called = False

    async def init(self) -> None: self.init_called = True
    async def send(self, title: str, body: str, target_users: List[str], details = None) -> None:
        await self.send_mock(title, body, target_users, details)
    async def close(self) -> None: self.closed_called = True


class TestEventProcessing(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        # Reset mocks before each test
        self.mock_http_client = AsyncMock(spec=httpx.AsyncClient)
        
        self.mock_wxpusher_notifier = MockNotificationNotifier("wxpusher")
        self.active_notifiers_patch = patch('app.main.active_notifiers', {"wxpusher": self.mock_wxpusher_notifier})
        self.mock_active_notifiers = self.active_notifiers_patch.start()
        
        self.http_client_patch = patch('app.main.http_storage_client', self.mock_http_client)
        self.mock_http_storage_client_global = self.http_client_patch.start()

        # Default successful response for http_client.get()
        self.mock_http_response = AsyncMock(spec=httpx.Response)
        self.mock_http_response.status_code = 200
        self.mock_http_response.json.return_value = {} 
        self.mock_http_response.text = "{}"
        self.mock_http_client.get = AsyncMock(return_value=self.mock_http_response)
        
    def tearDown(self):
        self.active_notifiers_patch.stop()
        self.http_client_patch.stop()

    def _create_mock_incoming_message(self, event_data: Any) -> AsyncMock:
        mock_message = AsyncMock(spec=aio_pika.IncomingMessage)
        if isinstance(event_data, LiveStatusChangedEvent):
            mock_message.body = event_data.model_dump_json().encode()
        elif isinstance(event_data, str):
            mock_message.body = event_data.encode()
        else: # bytes
            mock_message.body = event_data
            
        mock_message.ack = AsyncMock()
        mock_message.reject = AsyncMock()
        mock_process_cm = AsyncMock()
        mock_message.process = MagicMock(return_value=mock_process_cm)
        mock_process_cm.__aenter__ = AsyncMock(return_value=None)
        mock_process_cm.__aexit__ = AsyncMock(return_value=None)
        return mock_message

    async def test_process_with_user_template(self):
        user_id = "user1_ut"
        event_type = "live_start"
        streamer_info = StreamerInfo(platform_name="bilibili", room_id="123", anchor_name="TestAnchor", title="Live Title", live_status=True, stream_url="url")
        event = LiveStatusChangedEvent(event_type=event_type, user_id=user_id, streamer_info=streamer_info)
        mock_message = self._create_mock_incoming_message(event)

        user_template_content = "User template: {{ streamer_info.anchor_name }} is LIVE!"
        wxpusher_uid = "UID_USER1_UT"

        # Configure side_effect for multiple calls to http_client.get
        # 1st call (get_user_wxpusher_uid): returns UID
        # 2nd call (fetch_template_from_storage for user): returns user template
        # 3rd call (fetch_template_from_storage for global - won't happen if user found): not configured as not needed
        mock_uid_response = AsyncMock(spec=httpx.Response); mock_uid_response.status_code=200; mock_uid_response.json.return_value = {"channel_uid": wxpusher_uid}
        mock_user_template_response = AsyncMock(spec=httpx.Response); mock_user_template_response.status_code=200; mock_user_template_response.json.return_value = [{"template_content": user_template_content}]
        
        self.mock_http_client.get.side_effect = [mock_uid_response, mock_user_template_response]
        
        await process_event_message(mock_message)

        expected_uid_url = f"{settings.STORAGE_SERVICE_URL}/users/{user_id}/notification_channels/wxpusher"
        expected_user_template_url = f"{settings.STORAGE_SERVICE_URL}/templates"
        
        calls = self.mock_http_client.get.call_args_list
        self.assertEqual(len(calls), 2)
        self.assertEqual(calls[0].args[0], expected_uid_url)
        self.assertEqual(calls[1].args[0], expected_user_template_url)
        self.assertEqual(calls[1].kwargs['params'], {'user_id': user_id, 'template_type': event_type})
        
        self.mock_wxpusher_notifier.send_mock.assert_called_once()
        send_args = self.mock_wxpusher_notifier.send_mock.call_args[0]
        self.assertIn("Bilibili - TestAnchor - Live Start", send_args[0]) 
        self.assertEqual(send_args[1], "User template: TestAnchor is LIVE!") 
        self.assertEqual(["UID_USER1_UT"], send_args[2]) 
        mock_message.ack.assert_called_once()

    async def test_process_with_global_template_if_user_not_found(self):
        user_id = "user_gt"
        event_type = "title_change"
        s_info = StreamerInfo(platform_name="douyu", room_id="456", anchor_name="Anchor2", title="New Title", live_status=True, stream_url="url2")
        event = LiveStatusChangedEvent(event_type=event_type, user_id=user_id, streamer_info=s_info)
        mock_message = self._create_mock_incoming_message(event)

        global_template_content = "Global: {{ streamer_info.title }} by {{ streamer_info.anchor_name }}"
        wxpusher_uid = "UID_USER_GT"

        mock_uid_response = AsyncMock(spec=httpx.Response); mock_uid_response.status_code=200; mock_uid_response.json.return_value = {"channel_uid": wxpusher_uid}
        mock_user_template_response = AsyncMock(spec=httpx.Response); mock_user_template_response.status_code=200; mock_user_template_response.json.return_value = [] # No user template
        mock_global_template_response = AsyncMock(spec=httpx.Response); mock_global_template_response.status_code=200; mock_global_template_response.json.return_value = [{"template_content": global_template_content}]
        
        self.mock_http_client.get.side_effect = [mock_uid_response, mock_user_template_response, mock_global_template_response]

        await process_event_message(mock_message)

        self.assertEqual(self.mock_http_client.get.call_count, 3)
        self.mock_wxpusher_notifier.send_mock.assert_called_once()
        send_args = self.mock_wxpusher_notifier.send_mock.call_args[0]
        self.assertIn("Douyu - Anchor2 - Title Change", send_args[0])
        self.assertEqual(send_args[1], "Global: New Title by Anchor2")
        mock_message.ack.assert_called_once()

    async def test_process_with_fallback_if_no_templates_found(self):
        user_id = "user_fb"
        event_type = "live_end"
        s_info = StreamerInfo(platform_name="huya", room_id="789", anchor_name="主播三", title="结束直播", live_status=False, stream_url="url3")
        event = LiveStatusChangedEvent(event_type=event_type, user_id=user_id, streamer_info=s_info)
        mock_message = self._create_mock_incoming_message(event)
        wxpusher_uid = "UID_USER_FB"

        mock_uid_response = AsyncMock(spec=httpx.Response); mock_uid_response.status_code=200; mock_uid_response.json.return_value = {"channel_uid": wxpusher_uid}
        mock_no_template_response = AsyncMock(spec=httpx.Response); mock_no_template_response.status_code=200; mock_no_template_response.json.return_value = []
        
        self.mock_http_client.get.side_effect = [mock_uid_response, mock_no_template_response, mock_no_template_response]

        await process_event_message(mock_message)
        
        self.mock_wxpusher_notifier.send_mock.assert_called_once()
        send_args = self.mock_wxpusher_notifier.send_mock.call_args[0]
        self.assertIn("Huya - 主播三 - Live End", send_args[0])
        self.assertIn("😴 主播 主播三 在 Huya 下播了。", send_args[1]) # Fallback body
        mock_message.ack.assert_called_once()

    async def test_process_malformed_json_message_rejects(self):
        malformed_json_body = "this is not json"
        mock_message = self._create_mock_incoming_message(malformed_json_body)

        with self.assertLogs(logger='app.main', level='ERROR') as cm:
            await process_event_message(mock_message)
        
        self.assertTrue(any("Failed to decode message body" in log for log in cm.output))
        mock_message.reject.assert_called_once_with(requeue=False)
        mock_message.ack.assert_not_called()
        self.mock_wxpusher_notifier.send_mock.assert_not_called()

    async def test_process_storage_service_uid_fetch_error_rejects(self):
        user_id = "user_storage_error"
        event = LiveStatusChangedEvent(event_type="live_start", user_id=user_id, streamer_info=MagicMock())
        mock_message = self._create_mock_incoming_message(event)

        self.mock_http_client.get.side_effect = httpx.RequestError("Storage service down")

        with self.assertLogs(logger='app.main', level='ERROR') as cm:
            await process_event_message(mock_message)

        self.assertTrue(any(f"Request error fetching WxPusher UID for user {user_id}" in log for log in cm.output))
        mock_message.reject.assert_called_once_with(requeue=False) # Or True depending on retry strategy
        self.mock_wxpusher_notifier.send_mock.assert_not_called()

    async def test_process_storage_service_template_fetch_error_uses_fallback(self):
        user_id = "user_template_error"
        event_type = "live_start"
        s_info = StreamerInfo(platform_name="test", room_id="1", anchor_name="A", title="T", live_status=True, stream_url="u")
        event = LiveStatusChangedEvent(event_type=event_type, user_id=user_id, streamer_info=s_info)
        mock_message = self._create_mock_incoming_message(event)
        wxpusher_uid = "UID_TEMPLATE_ERROR"

        mock_uid_response = AsyncMock(spec=httpx.Response); mock_uid_response.status_code=200; mock_uid_response.json.return_value = {"channel_uid": wxpusher_uid}
        # Simulate error during template fetching by making the second call (user template) raise an error
        self.mock_http_client.get.side_effect = [
            mock_uid_response, 
            httpx.RequestError("Template service down")
        ]
        
        with self.assertLogs(logger='app.main', level='ERROR') as cm_error: # For template fetch error
            with self.assertLogs(logger='app.main', level='INFO') as cm_info: # For fallback usage
                 await process_event_message(mock_message)

        self.assertTrue(any(f"Request error fetching template" in log for log in cm_error.output))
        self.assertTrue(any(f"Using fallback formatting for event {event_type}, user {user_id}" in log for log in cm_info.output))
        
        self.mock_wxpusher_notifier.send_mock.assert_called_once() # Should still send with fallback
        mock_message.ack.assert_called_once()

    @patch('app.main.jinja_env.from_string') # Patch the specific Jinja function
    async def test_process_template_rendering_error_uses_fallback(self, mock_from_string):
        user_id = "user_render_error"
        event_type = "live_start"
        s_info = StreamerInfo(platform_name="test", room_id="1", anchor_name="A", title="T", live_status=True, stream_url="u")
        event = LiveStatusChangedEvent(event_type=event_type, user_id=user_id, streamer_info=s_info)
        mock_message = self._create_mock_incoming_message(event)
        wxpusher_uid = "UID_RENDER_ERROR"
        user_template_content = "Bad template {{ " # Syntax error

        mock_uid_response = AsyncMock(spec=httpx.Response); mock_uid_response.status_code=200; mock_uid_response.json.return_value = {"channel_uid": wxpusher_uid}
        mock_user_template_response = AsyncMock(spec=httpx.Response); mock_user_template_response.status_code=200; mock_user_template_response.json.return_value = [{"template_content": user_template_content}]
        self.mock_http_client.get.side_effect = [mock_uid_response, mock_user_template_response]

        # Make the mocked template's render method raise an error
        mock_template_obj = MagicMock()
        mock_template_obj.render.side_effect = TemplateSyntaxError("Simulated syntax error", 1)
        mock_from_string.return_value = mock_template_obj

        with self.assertLogs(logger='app.main', level='ERROR') as cm_error:
             with self.assertLogs(logger='app.main', level='INFO') as cm_info:
                await process_event_message(mock_message)
        
        self.assertTrue(any(f"Error rendering Jinja2 template" in log for log in cm_error.output))
        self.assertTrue(any(f"Using fallback formatting" in log for log in cm_info.output))
        self.mock_wxpusher_notifier.send_mock.assert_called_once() # Fallback sent
        mock_message.ack.assert_called_once()

    async def test_process_notifier_send_error_rejects_message(self):
        user_id = "user_notifier_fail"
        event = LiveStatusChangedEvent(event_type="live_start", user_id=user_id, streamer_info=MagicMock())
        mock_message = self._create_mock_incoming_message(event)
        wxpusher_uid = "UID_NOTIFIER_FAIL"

        mock_uid_response = AsyncMock(spec=httpx.Response); mock_uid_response.status_code=200; mock_uid_response.json.return_value = {"channel_uid": wxpusher_uid}
        mock_no_template_response = AsyncMock(spec=httpx.Response); mock_no_template_response.status_code=200; mock_no_template_response.json.return_value = []
        self.mock_http_client.get.side_effect = [mock_uid_response, mock_no_template_response, mock_no_template_response] # Use fallback

        self.mock_wxpusher_notifier.send_mock.side_effect = Exception("Notifier failed to send")

        with self.assertLogs(logger='app.main', level='ERROR') as cm:
            await process_event_message(mock_message)
        
        self.assertTrue(any("Error processing event message: Notifier failed to send" in log for log in cm.output))
        self.mock_wxpusher_notifier.send_mock.assert_called_once()
        mock_message.reject.assert_called_once_with(requeue=False) # Message rejected
        mock_message.ack.assert_not_called()


if __name__ == "__main__":
    unittest.main()
