from pydantic_settings import BaseSettings, SettingsConfigDict

class Settings(BaseSettings):
    STORAGE_SERVICE_URL: str = "http://localhost:8000" # Default for local, overridden by env in Docker

    # For Pydantic V2, to load from .env file if present (though Docker env vars are preferred for services)
    model_config = SettingsConfigDict(env_file=".env", env_file_encoding='utf-8', extra='ignore')

settings = Settings()
