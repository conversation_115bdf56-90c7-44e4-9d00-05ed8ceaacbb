import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Subscription, SubscriptionCreatePayload, SubscriptionSettings } from '@/types' // Assuming types are in @/types

// Helper function to make API calls
async function apiFetch(url: string, options: RequestInit = {}) {
  const defaultHeaders = {
    'Content-Type': 'application/json',
    // Add other default headers if needed, e.g., Authorization
  };
  options.headers = { ...defaultHeaders, ...options.headers };

  const response = await fetch(url, options);
  if (!response.ok) {
    let errorData;
    try {
      errorData = await response.json();
    } catch (e) {
      errorData = { detail: response.statusText };
    }
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }
  if (response.status === 204) { // No Content
    return null;
  }
  return response.json();
}


export const useSubscriptionStore = defineStore('subscriptions', () => {
  // --- State ---
  const userId = ref<string | null>(null)
  const subscriptions = ref<Subscription[]>([])
  const isLoading = ref<boolean>(false)
  const error = ref<string | null>(null)

  // --- Getters (Computed) ---
  const hasUserId = computed(() => userId.value !== null && userId.value !== '')

  // --- Actions ---
  function setUserId(newUserId: string) {
    if (newUserId && newUserId.trim() !== "") {
        userId.value = newUserId.trim()
        error.value = null // Clear previous errors
        fetchSubscriptions() // Fetch subscriptions when userId is set
    } else {
        userId.value = null
        subscriptions.value = [] // Clear subscriptions if userId is cleared
        error.value = "User ID cannot be empty."
    }
  }

  async function fetchSubscriptions() {
    if (!userId.value) {
      error.value = 'User ID is not set. Cannot fetch subscriptions.'
      return
    }
    isLoading.value = true
    error.value = null
    try {
      // Vite proxy will handle /api/v2
      const data = await apiFetch(`/api/v2/users/${userId.value}/subscriptions`)
      subscriptions.value = data as Subscription[]
    } catch (e: any) {
      error.value = e.message || 'Failed to fetch subscriptions.'
      subscriptions.value = [] // Clear previous subs on error
    } finally {
      isLoading.value = false
    }
  }

  async function addSubscription(platformName: string, roomId: string) {
    if (!userId.value) {
      error.value = 'User ID is not set. Cannot add subscription.'
      return
    }
    if (!platformName.trim() || !roomId.trim()) {
      error.value = 'Platform name and Room ID cannot be empty.'
      return;
    }

    isLoading.value = true
    error.value = null
    
    const payload: SubscriptionCreatePayload = {
      user_id: userId.value,
      platform_name: platformName.trim(),
      room_id: roomId.trim(),
      // Settings can be omitted to use backend defaults
      // settings: { notify_live_start: true, notify_live_end: false, notify_title_change: false } 
    };

    try {
      await apiFetch(`/api/v2/users/${userId.value}/subscriptions`, {
        method: 'POST',
        body: JSON.stringify(payload),
      })
      await fetchSubscriptions() // Re-fetch to get the updated list with new ID and settings
    } catch (e: any) {
      error.value = e.message || 'Failed to add subscription.'
    } finally {
      isLoading.value = false
    }
  }

  async function deleteSubscription(subscriptionId: number) { // ID is number based on interface
    if (!userId.value) {
      error.value = 'User ID is not set. Cannot delete subscription.'
      return
    }
    isLoading.value = true
    error.value = null
    try {
      await apiFetch(`/api/v2/users/${userId.value}/subscriptions/${subscriptionId}`, {
        method: 'DELETE',
      })
      // Remove from local list or re-fetch
      subscriptions.value = subscriptions.value.filter(sub => sub.id !== subscriptionId)
      // Or await fetchSubscriptions() if you prefer to always get the fresh list
    } catch (e: any) {
      error.value = e.message || 'Failed to delete subscription.'
    } finally {
      isLoading.value = false
    }
  }
  
  // Action to update subscription settings (example, not explicitly required by MVP list)
  async function updateSubscriptionSettings(subscriptionId: number, settingsPayload: SubscriptionSettings) {
    if (!userId.value) {
      error.value = 'User ID is not set. Cannot update settings.';
      return;
    }
    isLoading.value = true;
    error.value = null;
    try {
      const updatedSub = await apiFetch(
        `/api/v2/users/${userId.value}/subscriptions/${subscriptionId}`,
        {
          method: 'PUT',
          body: JSON.stringify(settingsPayload),
        }
      );
      // Update local list
      const index = subscriptions.value.findIndex(sub => sub.id === subscriptionId);
      if (index !== -1) {
        subscriptions.value[index] = updatedSub as Subscription;
      }
    } catch (e: any) {
      error.value = e.message || 'Failed to update subscription settings.';
    } finally {
      isLoading.value = false;
    }
  }


  return {
    userId,
    subscriptions,
    isLoading,
    error,
    hasUserId,
    setUserId,
    fetchSubscriptions,
    addSubscription,
    deleteSubscription,
    updateSubscriptionSettings // Expose if used
  }
})
