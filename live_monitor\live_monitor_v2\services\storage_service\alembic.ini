# A generic alembic configuration file.
# https://alembic.sqlalchemy.org/en/latest/tutorial.html#editing-the-ini-file

[alembic]
# path to migration scripts
script_location = alembic

# template for migration file names, e.g. YYYYMMDDHHMMSS_my_revision.py
file_template = %%(rev)s_%%(slug)s

# timezone for timestamps within revision files
# leave blank for UTC
# timezone =

# sys.path path, will be prepended to sys.path if present.
# defaults to the current working directory.
# prepend_sys_path = .

# Logging configuration
[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console
qualname =

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %%(levelname)-5.5s [%%(name)s] %%(message)s
datefmt = %%H:%%M:%%S

# વિકાસકર્તા દ્વારા સુયોજિત કરવા માટે ડેટાબેઝ URL (Database URL to be set by the developer)
# sqlalchemy.url = driver://user:pass@localhost/dbname
sqlalchemy.url = ********************************************************
