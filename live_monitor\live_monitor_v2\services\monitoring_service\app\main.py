import asyncio
import logging
import signal
from typing import Dict, Any, Optional, List

import httpx

from app.config import settings
from app.plugin_loader import load_platform_adapters
from app.message_broker import message_broker_client, LiveStatusChangedEvent, StreamerInfo
from libs.plugin_interfaces.platform_adapter_v1 import PlatformAdapterV1

# Configure basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# In-memory cache for previous statuses
previous_statuses: Dict[str, Dict[str, Any]] = {}

# Loaded platform adapters
platform_adapters: Dict[str, PlatformAdapterV1] = {}

# HTTP client for calling Storage Service
http_storage_client: Optional[httpx.AsyncClient] = None

shutdown_event = asyncio.Event()

async def fetch_all_active_subscriptions() -> Optional[Dict[str, List[Dict[str, Any]]]]:
    """Fetches all active subscriptions from the Storage Service."""
    global http_storage_client
    if not http_storage_client:
        logger.error("HTTP client for Storage Service not initialized.")
        return None
    
    url = f"{settings.STORAGE_SERVICE_URL}/internal/subscriptions/all_active"
    try:
        response = await http_storage_client.get(url, timeout=10.0)
        response.raise_for_status()
        return response.json() 
    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error fetching subscriptions: {e.response.status_code} - {e.response.text}")
    except httpx.RequestError as e:
        logger.error(f"Request error fetching subscriptions: {e}")
    except Exception as e: 
        logger.error(f"Generic error fetching subscriptions: {e}")
    return None

async def process_subscription(
    user_id: str,
    subscription: Dict[str, Any],
    adapter: PlatformAdapterV1
):
    global previous_statuses
    platform_name = subscription.get("platform_name")
    room_id = subscription.get("room_id")

    if not platform_name or not room_id:
        logger.warning(f"Subscription for user {user_id} is missing platform_name or room_id: {subscription}")
        return

    logger.debug(f"Checking status for user {user_id}, platform {platform_name}, room {room_id}")
    
    # current_status_info_dict is the raw dict from the adapter's get_streamer_status
    current_status_info_dict = await adapter.get_streamer_status(room_id)

    if not current_status_info_dict:
        logger.warning(f"Failed to get status for {platform_name} room {room_id} using adapter.")
        return

    # Get platform display name from adapter
    platform_display_name = adapter.get_platform_display_name()

    cache_key = f"{user_id}_{platform_name}_{room_id}"
    prev_status_data = previous_statuses.get(cache_key)

    current_live = current_status_info_dict.get("live_status", False)
    current_title = current_status_info_dict.get("title", "")
    current_anchor_name = current_status_info_dict.get("anchor_name", "") 

    if prev_status_data is None:
        logger.info(f"First check for {cache_key} (Anchor: {current_anchor_name}). Status: {'Live' if current_live else 'Offline'}. Title: {current_title}. Storing initial status.")
        previous_statuses[cache_key] = {"live_status": current_live, "title": current_title, "anchor_name": current_anchor_name}
        return

    prev_live = prev_status_data["live_status"]
    prev_title = prev_status_data["title"]
    
    event_to_publish: Optional[LiveStatusChangedEvent] = None
    event_type: Optional[str] = None
    routing_key_base = f"live.event.{platform_name}.{room_id}.{user_id}" 

    if current_live != prev_live:
        event_type = "live_start" if current_live else "live_end"
        logger.info(f"Status change for {cache_key} (Anchor: {current_anchor_name}): {event_type}. Old: {prev_live}, New: {current_live}")
    elif current_live and current_title != prev_title:
        event_type = "title_change"
        logger.info(f"Title change for {cache_key} (Anchor: {current_anchor_name}): Old: '{prev_title}', New: '{current_title}'")
    
    if event_type:
        # Create StreamerInfo Pydantic model, now including platform_display_name
        streamer_info_for_event = StreamerInfo(
            platform_name=current_status_info_dict.get("platform_name", platform_name), # Use platform_name from adapter if available
            platform_display_name=platform_display_name, # Newly added
            room_id=str(current_status_info_dict.get("room_id", room_id)),
            anchor_name=current_status_info_dict.get("anchor_name", "Unknown Anchor"),
            title=current_status_info_dict.get("title", "Unknown Title"),
            live_status=current_status_info_dict.get("live_status", False),
            stream_url=current_status_info_dict.get("stream_url", ""),
            cover_image_url=current_status_info_dict.get("cover_image_url"),
            viewer_count=current_status_info_dict.get("viewer_count"),
            start_time=current_status_info_dict.get("start_time")
        )
        
        event_to_publish = LiveStatusChangedEvent(
            event_type=event_type,
            user_id=user_id,
            streamer_info=streamer_info_for_event
        )
        
        routing_key = f"{routing_key_base}.{event_type}"
        success = await message_broker_client.publish_event(event_to_publish, routing_key=routing_key)
        if success:
            logger.info(f"Successfully published {event_type} event for {cache_key} to {routing_key}")
        else:
            logger.error(f"Failed to publish {event_type} event for {cache_key} to {routing_key}")

    if event_to_publish or current_live != prev_live or current_title != prev_title:
         previous_statuses[cache_key] = {"live_status": current_live, "title": current_title, "anchor_name": current_anchor_name}


async def monitoring_loop():
    global platform_adapters, http_storage_client
    
    http_storage_client = httpx.AsyncClient()

    while not shutdown_event.is_set():
        logger.info("Starting new monitoring cycle...")
        
        subscriptions_by_user = await fetch_all_active_subscriptions()
        if subscriptions_by_user is None:
            logger.warning("Failed to fetch subscriptions, or no subscriptions found. Retrying in next cycle.")
            await asyncio.sleep(settings.MONITOR_INTERVAL_SECONDS)
            continue

        if not subscriptions_by_user:
            logger.info("No active subscriptions to monitor currently.")
            await asyncio.sleep(settings.MONITOR_INTERVAL_SECONDS)
            continue
            
        active_subscriptions_count = sum(len(subs) for subs in subscriptions_by_user.values())
        logger.info(f"Fetched {active_subscriptions_count} active subscriptions across {len(subscriptions_by_user)} users.")

        tasks = []
        for user_id, user_subscriptions in subscriptions_by_user.items():
            for sub_details in user_subscriptions:
                platform_name = sub_details.get("platform_name")
                adapter = platform_adapters.get(platform_name) 
                if adapter:
                    tasks.append(process_subscription(user_id, sub_details, adapter))
                else:
                    logger.warning(f"No adapter found for platform: {platform_name} for user {user_id}")
        
        if tasks:
            # Exceptions in process_subscription will be caught by gather if return_exceptions=True
            # The current process_subscription does not catch exceptions from adapter.get_streamer_status()
            # So, if an adapter fails, its exception will be here.
            results = await asyncio.gather(*tasks, return_exceptions=True) 
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    # Log the exception from a failed task
                    # Task details can be inferred from tasks[i] if needed, but it's complex to get back to user_id/platform/room_id
                    logger.error(f"Error processing a subscription task: {result}", exc_info=result)

        logger.info(f"Monitoring cycle finished. Waiting for {settings.MONITOR_INTERVAL_SECONDS} seconds.")
        try:
            await asyncio.wait_for(shutdown_event.wait(), timeout=settings.MONITOR_INTERVAL_SECONDS)
        except asyncio.TimeoutError:
            pass 
        except asyncio.CancelledError:
            logger.info("Monitoring loop cancelled during sleep.")
            break


async def main():
    global platform_adapters, http_storage_client

    logger.info("Initializing Monitoring Service...")
    
    platform_adapters = load_platform_adapters() # Uses settings from app.config
    if not platform_adapters:
        logger.error("No platform adapters loaded. Monitoring service cannot start effectively.")
    else:
        logger.info(f"Loaded platform adapters: {list(platform_adapters.keys())}")
        for adapter_name, adapter_instance in platform_adapters.items():
            try:
                await adapter_instance.init()
                logger.info(f"Initialized adapter: {adapter_name}")
            except Exception as e:
                logger.error(f"Failed to initialize adapter {adapter_name}: {e}")

    try:
        await message_broker_client.connect()
    except Exception as e:
        logger.error(f"Could not connect to message broker during startup: {e}. Service will exit.")
        # Close already initialized adapters if broker fails
        if http_storage_client: await http_storage_client.aclose()
        for adapter_name, adapter_instance in platform_adapters.items():
            if hasattr(adapter_instance, 'close') and callable(adapter_instance.close): # type: ignore
                try: await adapter_instance.close() # type: ignore
                except Exception as e_close: logger.error(f"Error closing adapter {adapter_name}: {e_close}")
        return 

    monitor_task = asyncio.create_task(monitoring_loop())
    logger.info("Monitoring loop started.")

    await shutdown_event.wait() 

    logger.info("Shutting down Monitoring Service...")
    monitor_task.cancel()
    try:
        await monitor_task
    except asyncio.CancelledError:
        logger.info("Monitoring task cancelled successfully.")
    
    if http_storage_client:
        await http_storage_client.aclose()
        logger.info("HTTP storage client closed.")

    for adapter_name, adapter_instance in platform_adapters.items():
        if hasattr(adapter_instance, 'close') and callable(adapter_instance.close):
            try:
                await adapter_instance.close() 
                logger.info(f"Closed adapter: {adapter_name}")
            except Exception as e:
                logger.error(f"Error closing adapter {adapter_name}: {e}")
                
    await message_broker_client.close()
    logger.info("Monitoring Service shut down gracefully.")


def signal_handler(sig, frame):
    logger.info(f"Received signal {sig}, initiating shutdown...")
    shutdown_event.set()

if __name__ == "__main__":
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("KeyboardInterrupt received, shutting down...")
        if not shutdown_event.is_set():
             shutdown_event.set()
    except Exception as e:
        logger.error(f"Unhandled exception in main: {e}", exc_info=True)
    finally:
        logger.info("Application exiting.")
