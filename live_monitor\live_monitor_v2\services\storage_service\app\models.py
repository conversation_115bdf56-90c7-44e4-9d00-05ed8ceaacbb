from sqlalchemy import Column, Integer, String, JSON, UniqueConstraint, Index, Text
from sqlalchemy.dialects.postgresql import JSONB # For PostgreSQL specific JSON type
from .database import Base # Import Base from database.py

class Subscription(Base):
    __tablename__ = "subscriptions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, index=True, nullable=False)
    platform_name = Column(String, nullable=False)
    room_id = Column(String, nullable=False)
    settings = Column(JSONB, nullable=False, default={}) # Using JSONB for PostgreSQL

    __table_args__ = (
        UniqueConstraint('user_id', 'platform_name', 'room_id', name='uq_user_platform_room'),
    )

class NotificationChannel(Base):
    __tablename__ = "notification_channels"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, index=True, nullable=False)
    channel_type = Column(String, nullable=False) # e.g., "wxpusher"
    channel_uid = Column(String, nullable=False)  # e.g., WxPusher UID

    __table_args__ = (
        UniqueConstraint('user_id', 'channel_type', name='uq_user_channel_type'),
    )

class MessageTemplate(Base):
    __tablename__ = "message_templates"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    template_content = Column(Text, nullable=False)
    template_type = Column(String(50), index=True, nullable=False) # e.g., "live_start"
    user_id = Column(String, index=True, nullable=True) # Null for global/system templates

    __table_args__ = (
        UniqueConstraint('user_id', 'name', 'template_type', name='uq_user_name_template_type'),
        Index('ix_message_templates_user_id_template_type', 'user_id', 'template_type'),
    )
