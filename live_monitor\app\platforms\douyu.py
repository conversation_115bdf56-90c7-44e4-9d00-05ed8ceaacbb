"""
斗鱼平台适配器 - 异步版本
"""
from typing import Dict, Any, Optional

from app.utils.logger import setup_logger
from app.utils.helper import async_retry
from app.core.container import container
from .base import BasePlatform

logger = setup_logger('platform.douyu')

class DouyuPlatform(BasePlatform):
    """
    斗鱼直播平台适配器

    负责与斗鱼直播API交互，获取主播直播状态、标题等信息
    """

    def __init__(self):
        """初始化斗鱼平台适配器"""
        # 从容器中获取配置
        config = container.get('config')
        platforms_config = config.get('platforms', {})
        douyu_config = platforms_config.get('douyu', {})

        super().__init__(
            platform="douyu",
            name=platforms_config.get('names', {}).get('douyu', '斗鱼'),
            api_url=douyu_config.get('api_url', 'https://open.douyucdn.cn/api/RoomApi/room/{}'),
            room_url=douyu_config.get('room_url', 'https://www.douyu.com/{}'),
            headers=douyu_config.get('headers', {'User-Agent': 'Mozilla/5.0'})
        )

    @async_retry(max_retries=3, retry_interval=1.0)
    async def get_room_status(self, room_id: str) -> Optional[Dict[str, Any]]:
        """
        获取房间状态

        Args:
            room_id: 房间ID

        Returns:
            dict: 房间状态信息
            {
                'platform': str,
                'room_id': str,
                'anchor_name': str,
                'title': str,
                'live_status': bool,
                'url': str
            }
        """
        try:
            url = self.api_url.format(room_id)
            data = await self.fetch_json(url)

            if data and data.get('error') == 0:
                room_info = data['data']
                return {
                    'platform': self.platform,
                    'room_id': str(room_id),
                    'anchor_name': room_info['owner_name'],
                    'title': room_info['room_name'],
                    'live_status': room_info['room_status'] == "1",
                    'url': self.room_url.format(room_id)
                }
            else:
                error_msg = data.get('data', '未知错误') if data else '请求失败'
                logger.error(f"获取房间信息失败: {error_msg}")
                return None

        except Exception as e:
            logger.error(f"请求斗鱼API异常: {str(e)}")
            raise

# 注意: 不再创建全局实例
# 而是在需要时通过 get_platform 函数获取实例
