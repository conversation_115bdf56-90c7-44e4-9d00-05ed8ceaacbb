"""
平台接口定义
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional


class IPlatform(ABC):
    """平台接口"""

    @abstractmethod
    async def init(self) -> None:
        """初始化平台"""
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        """清理资源"""
        pass

    @property
    @abstractmethod
    def platform(self) -> str:
        """
        获取平台标识名称

        Returns:
            str: 平台标识名称，如 'bilibili', 'douyu' 等
        """
        pass

    @property
    @abstractmethod
    def name(self) -> str:
        """
        获取平台显示名称

        Returns:
            str: 平台的中文显示名称，如 '哔哩哔哩', '斗鱼' 等
        """
        pass

    @abstractmethod
    async def get_room_status(self, room_id: str) -> Optional[Dict[str, Any]]:
        """
        获取房间状态

        Args:
            room_id: 房间ID

        Returns:
            Optional[Dict[str, Any]]: 房间状态信息，如果获取失败则返回None
        """
        pass

    @abstractmethod
    async def fetch_json(self, url: str) -> Optional[Dict[str, Any]]:
        """
        获取JSON数据

        Args:
            url: 请求URL

        Returns:
            Optional[Dict[str, Any]]: JSON数据，如果获取失败则返回None
        """
        pass

    @abstractmethod
    async def fetch_html(self, url: str) -> Optional[str]:
        """
        获取HTML内容

        Args:
            url: 请求URL

        Returns:
            Optional[str]: HTML内容，如果获取失败则返回None
        """
        pass
