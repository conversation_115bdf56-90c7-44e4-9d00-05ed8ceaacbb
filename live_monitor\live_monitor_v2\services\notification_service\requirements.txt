fastapi>=0.100.0 # Optional, for potential health check endpoint later
uvicorn[standard]>=0.23.0 # Optional
httpx>=0.24.0 # For calling Storage Service
pydantic>=2.0.0
pydantic-settings>=2.0.0 # For loading config from env
aio-pika>=9.0.0 # For RabbitMQ
Jinja2>=3.1.0 # For eventual templating, though <PERSON> uses hardcoded
aiosmtplib>=1.1.0 # For Email Notifier
# Ensure plugin_interfaces and shared_models are accessible via PYTHONPATH in Docker/execution environment
