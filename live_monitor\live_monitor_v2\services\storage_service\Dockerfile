FROM python:3.10-slim

WORKDIR /app

COPY ./requirements.txt /app/requirements.txt
RUN pip install --no-cache-dir --upgrade pip
RUN pip install --no-cache-dir -r /app/requirements.txt

COPY ./app /app/app
COPY ./alembic.ini /app/alembic.ini
COPY ./alembic /app/alembic

# Expose port (though actual binding is in docker-compose)
EXPOSE 8000

# Command to run migrations can be handled by an entrypoint script or docker-compose later.
# For development, uvicorn will run the app.
# For production, you might run migrations then start uvicorn.
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
