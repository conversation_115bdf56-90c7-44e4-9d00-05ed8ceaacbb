<template>
  <main class="manage-templates-view">
    <h2>Manage Message Templates</h2>

    <div v-if="!currentUserId" class="auth-prompt">
      <p>Please set a User ID in the Home view to manage templates effectively (to see your user-specific templates or create new ones associated with your ID).</p>
      <p>Showing globally accessible templates.</p>
    </div>
    <hr v-if="!currentUserId"/>

    <div class="template-controls">
      <button @click="openAddTemplateForm" v-if="currentUserId" class="add-template-btn">
        Add New Template
      </button>
      <p v-else class="info-message">Set User ID on Home page to create user-specific templates.</p>
    </div>

    <div v-if="showEditForm">
      <EditMessageTemplateForm
        :template-to-edit="templateToEditData"
        :current-user-id="currentUserId"
        @save-success="handleSaveSuccess"
        @cancel="handleCancelForm"
      />
    </div>

    <MessageTemplateList
      @edit-template="handleEditTemplate"
      :key="listRefreshKey"
      class="templates-list-section"
    />
    <!-- :key is used to force re-render/re-fetch if needed after certain operations,
         though store reactivity should handle most list updates.
         The MessageTemplateList itself fetches on mount and on filter changes.
    -->

  </main>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useSubscriptionStore } from '@/stores/subscriptions';
import MessageTemplateList from '@/components/MessageTemplateList.vue';
import EditMessageTemplateForm from '@/components/EditMessageTemplateForm.vue';
import type { MessageTemplate } from '@/types';

const subscriptionStore = useSubscriptionStore();
const currentUserId = computed(() => subscriptionStore.userId);

const showEditForm = ref(false);
const templateToEditData = ref<MessageTemplate | null>(null);
const listRefreshKey = ref(0); // Used to force list refresh if needed

function openAddTemplateForm() {
  templateToEditData.value = null;
  showEditForm.value = true;
}

function handleEditTemplate(template: MessageTemplate) {
  templateToEditData.value = template;
  showEditForm.value = true;
}

function handleSaveSuccess() {
  showEditForm.value = false;
  templateToEditData.value = null;
  // The MessageTemplateList component fetches its own data based on its internal filters
  // and store reactivity. If a direct re-trigger is needed:
  // listRefreshKey.value++;
  // However, the store should update reactively, making the list update.
}

function handleCancelForm() {
  showEditForm.value = false;
  templateToEditData.value = null;
}
</script>

<style scoped>
.manage-templates-view {
  max-width: 1000px;
  margin: 20px auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

h2 {
  text-align: center;
  margin-bottom: 25px;
  color: #333;
}

.auth-prompt {
  background-color: #fff3cd;
  border: 1px solid #ffeeba;
  color: #856404;
  padding: 10px 15px;
  border-radius: 5px;
  margin-bottom: 15px;
  text-align: center;
}
.info-message {
  background-color: #e2f3ff;
  border: 1px solid #b8dffc;
  color: #0c5460;
  padding: 10px 15px;
  border-radius: 5px;
  margin-bottom: 15px;
  text-align: center;
}


.template-controls {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start; /* Align button to the left */
}

.add-template-btn {
  padding: 10px 18px;
  background-color: #007bff; /* Primary blue */
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s;
}

.add-template-btn:hover {
  background-color: #0056b3;
}

.templates-list-section {
  margin-top: 20px; /* Add some space if form is hidden and list is directly below button */
}
</style>
