from pydantic import BaseModel, ConfigDict, Field
from typing import Dict, Any, Optional, List
from enum import Enum

# --- TemplateType Enum ---
class TemplateTypeEnum(str, Enum):
    live_start = "live_start"
    live_end = "live_end"
    title_change = "title_change"
    # Add other event types as needed

# --- MessageTemplate Schemas ---
class MessageTemplateBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, description="Descriptive name for the template")
    template_content: str = Field(..., min_length=1, description="Jinja2 template string")
    template_type: TemplateTypeEnum
    user_id: Optional[str] = Field(None, description="User ID for user-specific templates, None for global/system")

class MessageTemplateCreate(MessageTemplateBase):
    pass

class MessageTemplateUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    template_content: Optional[str] = Field(None, min_length=1)
    template_type: Optional[TemplateTypeEnum] = None
    # user_id is typically not updatable for an existing template, 
    # ownership change would be a different operation or delete/recreate.

class MessageTemplate(MessageTemplateBase):
    id: int
    
    model_config = ConfigDict(from_attributes=True) # Pydantic V2

# --- Subscription Schemas ---
class SubscriptionSettings(BaseModel):
    notify_live_start: bool = True
    notify_live_end: bool = False
    notify_title_change: bool = False
    # Example: quiet_hours: Optional[Dict[str, str]] = None 
    # e.g., {"start": "23:00", "end": "07:00", "enabled": True}

class SubscriptionBase(BaseModel):
    platform_name: str
    room_id: str
    settings: SubscriptionSettings = SubscriptionSettings()

class SubscriptionCreate(SubscriptionBase):
    user_id: str # User ID must be provided on creation

class Subscription(SubscriptionBase):
    id: int
    user_id: str

    model_config = ConfigDict(from_attributes=True) # Pydantic V2

# --- NotificationChannel Schemas ---
class NotificationChannelBase(BaseModel):
    user_id: str
    channel_type: str # e.g., "wxpusher", "email"
    channel_uid: str  # e.g., WxPusher UID, email address

class NotificationChannelCreate(NotificationChannelBase):
    pass

class NotificationChannel(NotificationChannelBase):
    id: int

    model_config = ConfigDict(from_attributes=True) # Pydantic V2
