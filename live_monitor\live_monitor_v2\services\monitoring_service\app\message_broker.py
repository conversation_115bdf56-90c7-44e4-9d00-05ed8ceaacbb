import asyncio
import aio_pika
import logging
from typing import Optional, Any
from app.config import settings # Assuming settings.MESSAGE_BROKER_URL
from libs.shared_models.events import LiveStatusChangedEvent # Path to shared model

logger = logging.getLogger(__name__)

class MessageBrokerClient:
    def __init__(self, broker_url: str):
        self.broker_url = broker_url
        self.connection: Optional[aio_pika.RobustConnection] = None
        self.channel: Optional[aio_pika.Channel] = None
        self.exchange_name = "live_events" # Define an exchange name

    async def connect(self) -> None:
        if self.connection and not self.connection.is_closed:
            logger.info("Message broker connection already established.")
            return

        try:
            self.connection = await aio_pika.connect_robust(self.broker_url)
            self.channel = await self.connection.channel() # type: ignore
            # Declare an exchange (e.g., a topic exchange for routing events)
            await self.channel.declare_exchange(
                self.exchange_name, aio_pika.ExchangeType.TOPIC, durable=True
            )
            logger.info(f"Connected to message broker and declared exchange '{self.exchange_name}'.")
        except Exception as e:
            logger.error(f"Failed to connect to message broker: {e}")
            self.connection = None
            self.channel = None
            raise # Re-raise to handle connection failure during startup

    async def close(self) -> None:
        try:
            if self.channel and not self.channel.is_closed: # type: ignore
                await self.channel.close() # type: ignore
            if self.connection and not self.connection.is_closed:
                await self.connection.close()
            logger.info("Message broker connection closed.")
        except Exception as e:
            logger.error(f"Error closing message broker connection: {e}")
        finally:
            self.channel = None
            self.connection = None
            
    async def publish_event(self, event: LiveStatusChangedEvent, routing_key: str) -> bool:
        if not self.channel or self.channel.is_closed: # type: ignore
            logger.error("Cannot publish event: channel is not available or closed.")
            # Attempt to reconnect or raise an error
            try:
                logger.info("Attempting to reconnect to message broker for publishing...")
                await self.connect()
                if not self.channel: # Still no channel after reconnect attempt
                     logger.error("Reconnect failed, cannot publish event.")
                     return False
            except Exception as e:
                logger.error(f"Reconnect failed during publish attempt: {e}")
                return False

        try:
            message_body = event.model_dump_json().encode() # Pydantic V2
            message = aio_pika.Message(
                body=message_body,
                content_type="application/json",
                delivery_mode=aio_pika.DeliveryMode.PERSISTENT # Make messages persistent
            )
            await self.channel.default_exchange.publish( # type: ignore
                message,
                routing_key=routing_key # Example: "live.event.bilibili.12345"
            )
            # If using a specific exchange:
            # exchange = await self.channel.get_exchange(self.exchange_name)
            # await exchange.publish(message, routing_key=routing_key)
            logger.info(f"Published event to routing key '{routing_key}': {event.event_type} for user {event.user_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to publish event: {e}. Event: {event.model_dump_json()}")
            return False

# Global instance (or manage via dependency injection if using FastAPI structure)
message_broker_client = MessageBrokerClient(broker_url=settings.MESSAGE_BROKER_URL)
