"""
B站平台适配器 - 异步版本
"""
from typing import Dict, Any, Optional

from app.utils.logger import setup_logger
from app.utils.helper import async_retry
from app.core.container import container
from .base import BasePlatform

logger = setup_logger('platform.bilibili')

class BilibiliPlatform(BasePlatform):
    """
    B站直播平台适配器

    负责与B站直播API交互，获取主播直播状态、标题等信息
    """

    def __init__(self):
        """初始化B站平台适配器"""
        # 从容器中获取配置
        config = container.get('config')
        platforms_config = config.get('platforms', {})
        bilibili_config = platforms_config.get('bilibili', {})

        super().__init__(
            platform="bilibili",
            name=platforms_config.get('names', {}).get('bilibili', '哔哩哔哩'),
            api_url=bilibili_config.get('api_url', 'https://api.live.bilibili.com/room/v1/Room/get_info?room_id={}'),
            room_url=bilibili_config.get('room_url', 'https://live.bilibili.com/{}'),
            headers=bilibili_config.get('headers', {'User-Agent': 'Mozilla/5.0'})
        )
        self.user_api_url = bilibili_config.get('user_api_url', 'https://api.live.bilibili.com/live_user/v1/Master/info?uid={}')

    @async_retry(max_retries=3, retry_interval=1.0)
    async def get_room_status(self, room_id: str) -> Optional[Dict[str, Any]]:
        """
        获取房间状态

        Args:
            room_id: 房间ID

        Returns:
            dict: 房间状态信息
            {
                'platform': str,
                'room_id': str,
                'anchor_name': str,
                'title': str,
                'live_status': bool,
                'url': str
            }
        """
        try:
            # 获取房间信息
            room_url = self.api_url.format(room_id)
            room_data = await self.fetch_json(room_url)

            if room_data and room_data.get('code') == 0:
                room_info = room_data['data']
                uid = room_info['uid']

                # 获取用户信息
                user_url = self.user_api_url.format(uid)
                user_data = await self.fetch_json(user_url)

                if user_data and user_data.get('code') == 0:
                    user_info = user_data['data']
                    return {
                        'platform': self.platform,
                        'room_id': str(room_id),
                        'anchor_name': user_info['info']['uname'],
                        'title': room_info['title'],
                        'live_status': room_info['live_status'] == 1,
                        'url': self.room_url.format(room_id)
                    }
                else:
                    error_msg = user_data.get('message', '未知错误') if user_data else '请求失败'
                    logger.error(f"获取用户信息失败: {error_msg}")
                    return None
            else:
                error_msg = room_data.get('message', '未知错误') if room_data else '请求失败'
                logger.error(f"获取房间信息失败: {error_msg}")
                return None

        except Exception as e:
            logger.error(f"请求B站API异常: {str(e)}")
            raise

# 注意: 不再创建全局实例
# 而是在需要时通过 get_platform 函数获取实例
