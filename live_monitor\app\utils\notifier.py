"""
通知模块 - 使用WxPusher实现消息推送 (异步版本)
"""
import json
import os
import aiohttp
from datetime import datetime
import pytz
from typing import Dict, List, Any

from app.interfaces.notifier import INotifier
from app.core.container import container
from app.constants import DEFAULT_SUBSCRIPTION_SETTINGS
from .logger import setup_logger
from .helper import get_platform_name

logger = setup_logger('notifier')

class AsyncWxPusher(INotifier):
    """
    基于WxPusher的异步通知器

    负责发送开播/下播通知和标题更新通知
    支持免打扰时段和自定义通知设置
    """

    def __init__(self):
        """
        初始化WxPusher
        """
        self._initialized = False

        # 从容器中获取配置
        config = container.get('config')
        wxpusher_config = config.get('wxpusher', {})

        self.app_token = wxpusher_config.get('app_token', '')
        self.api_url = f"{wxpusher_config.get('base_url', 'https://wxpusher.zjiecode.com/api')}/send/message"
        self.cloudflare_url = wxpusher_config.get('cloudflare_worker_url')
        self.cloudflare_api_key = wxpusher_config.get('cloudflare_api_key')

        # 上次推送状态缓存
        self.status_cache_file = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            "data",
            "status_cache.json"
        )
        self.status_cache = {}
        self._ensure_data_dir()

    def _ensure_data_dir(self):
        """确保数据目录存在"""
        data_dir = os.path.dirname(self.status_cache_file)
        os.makedirs(data_dir, exist_ok=True)

    async def load_status_cache(self):
        """加载状态缓存"""
        try:
            if os.path.exists(self.status_cache_file):
                with open(self.status_cache_file, 'r', encoding='utf-8') as f:
                    self.status_cache = json.load(f)
            else:
                self.status_cache = {}
        except (FileNotFoundError, json.JSONDecodeError):
            self.status_cache = {}

    async def save_status_cache(self):
        """保存状态缓存"""
        with open(self.status_cache_file, 'w', encoding='utf-8') as f:
            json.dump(self.status_cache, f, ensure_ascii=False, indent=2)

    def get_cache_key(self, platform: str, room_id: str) -> str:
        """
        获取缓存键

        Args:
            platform: 平台名称
            room_id: 房间ID

        Returns:
            str: 缓存键，格式为 "platform_room_id"
        """
        return f"{platform}_{room_id}"

    def format_live_message(self, streamer_info: Dict[str, Any], is_live: bool) -> str:
        """
        格式化直播消息

        Args:
            streamer_info: 主播信息
            is_live: 是否开播

        Returns:
            str: 格式化后的消息
        """
        platform_name = get_platform_name(streamer_info['platform'])

        if is_live:
            message = f"""【{platform_name}开播提醒】

🎮 主播：{streamer_info['anchor_name']}
🏠 房间号：{streamer_info['room_id']}
📺 标题：{streamer_info['title']}

🔴 直播已开始"""
        else:
            message = f"""【{platform_name}下播提醒】

🎮 主播：{streamer_info['anchor_name']}
🏠 房间号：{streamer_info['room_id']}
📺 标题：{streamer_info['title']}

⚪ 直播已结束"""

        return message

    async def check_and_notify(self, streamer_info: Dict[str, Any], uid: str) -> None:
        """
        检查状态变化并发送通知

        Args:
            streamer_info: 主播信息
            uid: 用户ID
        """
        try:
            # 获取用户订阅设置
            storage = container.get('storage')
            user_subs = await storage.get_user_subscriptions(uid)

            # 查找当前主播的订阅设置
            sub_settings = None
            for sub in user_subs:
                if sub['platform'] == streamer_info['platform'] and sub['room_id'] == streamer_info['room_id']:
                    sub_settings = sub.get('settings', {})
                    break

            if not sub_settings:
                # 使用默认设置
                sub_settings = DEFAULT_SUBSCRIPTION_SETTINGS

            # 检查是否在免打扰时段
            if sub_settings.get('quiet_hours', {}).get('enabled', False):
                # 使用北京时间而不是服务器本地时间
                beijing_tz = pytz.timezone('Asia/Shanghai')
                now = datetime.now(pytz.utc).astimezone(beijing_tz).time()
                start_time = datetime.strptime(sub_settings['quiet_hours']['start'], '%H:%M').time()
                end_time = datetime.strptime(sub_settings['quiet_hours']['end'], '%H:%M').time()

                # 记录当前使用的时区和时间
                local_time = datetime.now().time()
                logger.debug(f"当前服务器时间: {local_time}, 北京时间: {now}")

                # 判断当前时间是否在免打扰时段
                is_quiet_time = False
                if start_time > end_time:  # 跨越午夜
                    is_quiet_time = now >= start_time or now <= end_time
                else:
                    is_quiet_time = start_time <= now <= end_time

                if is_quiet_time:
                    logger.info(f"免打扰时段，跳过通知: {streamer_info['anchor_name']}")
                    return

            # 确保缓存已加载
            if not self.status_cache:
                await self.load_status_cache()

            cache_key = self.get_cache_key(streamer_info['platform'], streamer_info['room_id'])
            cached_info = self.status_cache.get(cache_key, {})
            previous_status = cached_info.get('live_status')
            previous_title = cached_info.get('title')
            current_status = streamer_info['live_status']
            current_title = streamer_info['title']

            # 更新缓存
            self.status_cache[cache_key] = {
                'live_status': current_status,
                'title': current_title,
                'last_update': datetime.now().isoformat()
            }
            await self.save_status_cache()

            # 如果是首次检查，只更新缓存，不发送通知
            if previous_status is None or previous_title is None:
                # 在程序启动时，我们只记录状态，不发送通知
                # 这样可以避免在每次启动时都收到开播通知
                logger.info(f"首次检查，记录状态: {streamer_info['anchor_name']} - 当前状态: {'开播' if current_status else '未开播'}")
                return

            # 检查直播状态变化
            if previous_status != current_status:
                # 开播通知
                if current_status and sub_settings.get('notify_live_start', True):
                    message = self.format_live_message(streamer_info, current_status)
                    success = await self.send_message(message, [uid])
                    if success:
                        # 使用print而非日志，直接输出到终端
                        print(f"主播 {streamer_info['anchor_name']} 已开播")
                        logger.info(f"开播通知已发送: {streamer_info['anchor_name']}")
                    else:
                        logger.warning(f"开播通知发送失败: {streamer_info['anchor_name']}")
                elif current_status == False and sub_settings.get('notify_live_end', True):
                    # 下播通知
                    message = self.format_live_message(streamer_info, current_status)
                    success = await self.send_message(message, [uid])
                    if success:
                        # 使用print而非日志，直接输出到终端
                        print(f"主播 {streamer_info['anchor_name']} 已下播")
                        logger.info(f"下播通知已发送: {streamer_info['anchor_name']}")
                    else:
                        logger.warning(f"下播通知发送失败: {streamer_info['anchor_name']}")
                else:
                    status_text = '开播' if current_status else '下播'
                    logger.info(f"由于用户设置，跳过{status_text}通知: {streamer_info['anchor_name']}")

            # 检查标题变化（无论是否在直播）
            elif previous_title != current_title and previous_title != "" and current_title != "":
                # 只有在设置允许时才发送标题更新通知
                if sub_settings.get('notify_title', True):
                    status_text = "直播中" if current_status else "未直播"
                    platform_name = get_platform_name(streamer_info['platform'])
                    title_message = f"""【{platform_name}】{streamer_info['anchor_name']}更改标题

🏠 房间号：{streamer_info['room_id']}
📺 新标题：{current_title}
💡 状态：{status_text}"""
                    success = await self.send_message(title_message, [uid])
                    if success:
                        # 使用print而非日志，直接输出到终端
                        print(f"主播 {streamer_info['anchor_name']} 更新标题")
                        logger.info(f"标题更新通知已发送: {streamer_info['anchor_name']} - {current_title}")
                    else:
                        logger.warning(f"标题更新通知发送失败: {streamer_info['anchor_name']} - {current_title}")
                else:
                    logger.info(f"由于用户设置，跳过标题更新通知: {streamer_info['anchor_name']} - {current_title}")

        except Exception as e:
            logger.error(f"检查状态并发送通知时出错: {str(e)}")

    async def send_message(self, content: str, uids: List[str]) -> bool:
        """
        发送消息

        Args:
            content: 消息内容
            uids: 接收用户的uid列表

        Returns:
            bool: 是否发送成功
        """
        try:
            data = {
                "appToken": self.app_token,
                "content": content,
                "contentType": 1,  # 1表示文本
                "uids": uids,
                "verifyPay": False
            }

            # 如果使用 Cloudflare Worker 且配置了 API 密钥，添加到请求中
            if self.cloudflare_url and self.cloudflare_api_key:
                data["apiKey"] = self.cloudflare_api_key

            logger.info(f"准备发送通知: appToken={self.app_token}, uids={uids}")

            async with aiohttp.ClientSession() as session:
                # 使用 Cloudflare Worker 作为中转，如果配置了的话
                if self.cloudflare_url:
                    logger.info(f"使用 Cloudflare Worker 中转: {self.cloudflare_url}")
                    async with session.post(self.cloudflare_url, json=data, timeout=10) as response:
                        result = await response.json()
                else:
                    # 直接调用 WxPusher API
                    async with session.post(self.api_url, json=data, timeout=10) as response:
                        result = await response.json()

                logger.info(f"WxPusher响应: {result}")

                # WxPusher 的成功代码是 1000
                success_code = 1000
                if 'code' in result and result['code'] != success_code:
                    logger.error(f"发送消息失败: {result}")
                    return False

                logger.info("消息发送成功")
                return True

        except Exception as e:
            error_msg = str(e)
            if not error_msg:
                error_msg = "未知错误"
            logger.error(f"发送消息时出错: {error_msg}")
            return False

    async def init(self) -> None:
        """初始化通知器"""
        if self._initialized:
            return

        logger.info("初始化通知器")

        # 加载状态缓存
        await self.load_status_cache()

        self._initialized = True

    async def cleanup(self) -> None:
        """清理资源"""
        if not self._initialized:
            return

        logger.info("清理通知器资源")
        self._initialized = False

# 注意: 不再创建全局WxPusher实例
# 而是通过依赖注入容器获取实例
