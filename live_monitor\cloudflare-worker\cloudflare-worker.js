/**
 * WxPusher API 代理 - Cloudflare Worker
 * 
 * 此Worker用于代理WxPusher API请求，解决某些环境无法直接访问WxPusher API的问题。
 * 它接收来自直播监控系统的请求，转发到WxPusher API，然后将响应返回给系统。
 * 
 * 使用方法：
 * 1. 在Cloudflare Workers中部署此代码
 * 2. 在config.yaml中设置cloudflare_worker_url和cloudflare_api_key
 */

// 配置项
const CONFIG = {
  // WxPusher API基础URL
  WXPUSHER_BASE_URL: 'https://wxpusher.zjiecode.com/api',
  
  // API密钥，用于验证请求的合法性
  API_KEY: 'your_secret_key',  // 替换为你的密钥，需要与config.yaml中的cloudflare_api_key一致
  
  // 允许的来源域名，设置为*表示允许所有域名
  ALLOWED_ORIGINS: ['*'],
  
  // 是否启用调试日志
  DEBUG: false
};

/**
 * 处理请求的主函数
 */
async function handleRequest(request) {
  // 检查请求方法
  if (request.method !== 'POST') {
    return new Response('Method Not Allowed', { status: 405 });
  }
  
  // 解析请求URL
  const url = new URL(request.url);
  
  // 获取请求头中的API密钥
  const apiKey = request.headers.get('X-API-Key');
  
  // 验证API密钥
  if (apiKey !== CONFIG.API_KEY) {
    return new Response('Unauthorized', { status: 401 });
  }
  
  try {
    // 获取请求体
    const requestBody = await request.json();
    
    // 获取目标路径
    const targetPath = url.pathname.replace(/^\/api/, '');
    
    // 构建目标URL
    const targetUrl = `${CONFIG.WXPUSHER_BASE_URL}${targetPath}`;
    
    if (CONFIG.DEBUG) {
      console.log(`Proxying request to: ${targetUrl}`);
      console.log(`Request body: ${JSON.stringify(requestBody)}`);
    }
    
    // 转发请求到WxPusher API
    const response = await fetch(targetUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'WxPusher-Proxy/1.0'
      },
      body: JSON.stringify(requestBody)
    });
    
    // 获取响应体
    const responseBody = await response.json();
    
    if (CONFIG.DEBUG) {
      console.log(`Response from WxPusher: ${JSON.stringify(responseBody)}`);
    }
    
    // 设置CORS头
    const headers = new Headers({
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': getAllowedOrigin(request),
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, X-API-Key'
    });
    
    // 返回响应
    return new Response(JSON.stringify(responseBody), {
      status: response.status,
      headers: headers
    });
  } catch (error) {
    console.error(`Error processing request: ${error.message}`);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': getAllowedOrigin(request)
      }
    });
  }
}

/**
 * 处理OPTIONS请求（预检请求）
 */
function handleOptions(request) {
  return new Response(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': getAllowedOrigin(request),
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, X-API-Key',
      'Access-Control-Max-Age': '86400'
    }
  });
}

/**
 * 获取允许的来源
 */
function getAllowedOrigin(request) {
  const origin = request.headers.get('Origin');
  
  // 如果配置允许所有来源，或者来源在允许列表中，则返回该来源
  if (CONFIG.ALLOWED_ORIGINS.includes('*') || 
      (origin && CONFIG.ALLOWED_ORIGINS.includes(origin))) {
    return origin || '*';
  }
  
  // 默认返回第一个允许的来源
  return CONFIG.ALLOWED_ORIGINS[0] || '*';
}

/**
 * 处理所有请求的入口函数
 */
addEventListener('fetch', event => {
  const request = event.request;
  
  // 处理OPTIONS请求（预检请求）
  if (request.method === 'OPTIONS') {
    event.respondWith(handleOptions(request));
    return;
  }
  
  // 处理其他请求
  event.respondWith(handleRequest(request));
});
