# Cloudflare Worker 部署指南

本指南将帮助您设置和部署 Cloudflare Worker 作为 WxPusher API 的代理，解决某些环境无法直接访问 WxPusher API 的问题。

## 为什么需要 Cloudflare Worker？

在某些网络环境下（例如某些国家/地区的服务器），可能无法直接访问 WxPusher API。Cloudflare Worker 可以作为中间代理，帮助您的直播监控系统与 WxPusher API 进行通信。

Cloudflare Worker 的优势：
- 全球分布的边缘网络，提供低延迟访问
- 免费计划每天提供 100,000 次请求
- 简单易用，无需管理服务器
- 高可用性和可靠性

## 前提条件

1. Cloudflare 账号（免费）
2. 基本的 JavaScript 知识（如需自定义 Worker）

## 部署步骤

### 1. 创建 Cloudflare 账号

如果您还没有 Cloudflare 账号，请前往 [Cloudflare 官网](https://www.cloudflare.com/) 注册一个免费账号。

### 2. 访问 Cloudflare Workers

1. 登录您的 Cloudflare 账号
2. 在左侧导航栏中点击 "Workers & Pages"
3. 如果是首次使用，您可能需要设置一个 Workers 子域名（例如 `your-username.workers.dev`）

### 3. 创建新的 Worker

1. 点击 "Create Worker" 按钮
2. 在编辑器中，删除默认代码
3. 复制 `cloudflare-worker.js` 文件中的代码并粘贴到编辑器中
4. 修改配置部分：
   ```javascript
   const CONFIG = {
     // WxPusher API基础URL
     WXPUSHER_BASE_URL: 'https://wxpusher.zjiecode.com/api',
     
     // API密钥，用于验证请求的合法性
     API_KEY: 'your_secret_key',  // 替换为您自定义的密钥
     
     // 允许的来源域名，设置为*表示允许所有域名
     ALLOWED_ORIGINS: ['*'],
     
     // 是否启用调试日志
     DEBUG: false
   };
   ```
   
   特别注意：
   - 将 `API_KEY` 设置为一个安全的随机字符串，这将用于验证请求的合法性
   - 记住这个密钥，稍后需要在直播监控系统的配置文件中使用

5. 点击 "Save and Deploy" 按钮

### 4. 获取 Worker URL

部署成功后，您将看到 Worker 的 URL，格式类似：
```
https://wxpusher-proxy.your-username.workers.dev
```

记下这个 URL，稍后需要在直播监控系统的配置文件中使用。

### 5. 配置直播监控系统

编辑 `config/config.yaml` 文件，更新 WxPusher 配置部分：

```yaml
# WxPusher配置
wxpusher:
  app_token: AT_xxxxxxxxxxxxxxxxxxxxxxxxxxxxx  # 替换为你的WxPusher应用Token
  base_url: https://wxpusher.zjiecode.com/api
  # Cloudflare Worker配置
  cloudflare_worker_url: https://wxpusher-proxy.your-username.workers.dev  # 替换为你的Worker URL
  cloudflare_api_key: your_secret_key  # 替换为你在Worker中设置的API_KEY
```

确保 `cloudflare_api_key` 与 Worker 代码中的 `API_KEY` 一致。

## 测试 Worker

部署完成后，您可以使用以下方法测试 Worker 是否正常工作：

### 使用 curl 测试

```bash
curl -X POST \
  https://wxpusher-proxy.your-username.workers.dev/api/send/message \
  -H 'Content-Type: application/json' \
  -H 'X-API-Key: your_secret_key' \
  -d '{
    "appToken": "YOUR_WXPUSHER_APP_TOKEN",
    "content": "测试消息",
    "summary": "测试摘要",
    "contentType": 1,
    "topicIds": [],
    "uids": ["YOUR_WXPUSHER_UID"],
    "url": ""
  }'
```

如果一切正常，您应该会收到一个成功的响应，并在微信上收到测试消息。

### 使用 Postman 测试

1. 打开 Postman
2. 创建一个新的 POST 请求
3. 设置请求 URL 为 `https://wxpusher-proxy.your-username.workers.dev/api/send/message`
4. 添加请求头：
   - `Content-Type: application/json`
   - `X-API-Key: your_secret_key`
5. 添加请求体（JSON 格式）：
   ```json
   {
     "appToken": "YOUR_WXPUSHER_APP_TOKEN",
     "content": "测试消息",
     "summary": "测试摘要",
     "contentType": 1,
     "topicIds": [],
     "uids": ["YOUR_WXPUSHER_UID"],
     "url": ""
   }
   ```
6. 点击 "Send" 按钮

## 故障排除

### Worker 返回 401 Unauthorized

- 检查请求中的 `X-API-Key` 头是否与 Worker 代码中的 `API_KEY` 一致
- 确保在 `config.yaml` 中正确设置了 `cloudflare_api_key`

### Worker 返回 500 Internal Server Error

- 检查 Worker 的日志（在 Cloudflare Dashboard 中）
- 确保 WxPusher API 的基础 URL 正确
- 尝试将 `DEBUG` 设置为 `true` 以获取更多日志信息

### 无法收到微信通知

- 确保 WxPusher 的 `app_token` 正确
- 确保用户已关注 WxPusher 公众号并完成订阅
- 检查 `uids` 是否正确

## 限制和注意事项

1. **请求限制**：Cloudflare Workers 免费计划每天限制 100,000 次请求，对于大多数直播监控系统来说已经足够
2. **超时限制**：Worker 的执行时间限制为 10ms（免费计划），确保您的代码高效执行
3. **安全性**：
   - 使用强密码作为 API 密钥
   - 考虑限制允许的来源域名
   - 不要在 Worker 代码中硬编码敏感信息

## 自定义和优化

您可以根据需要自定义 Worker 代码：

1. **缓存**：添加缓存机制，减少对 WxPusher API 的请求
2. **速率限制**：添加速率限制，防止滥用
3. **日志记录**：增强日志记录功能，便于调试
4. **错误处理**：添加更详细的错误处理和重试机制

## 更多资源

- [Cloudflare Workers 文档](https://developers.cloudflare.com/workers/)
- [WxPusher API 文档](https://wxpusher.zjiecode.com/docs/)
- [JavaScript Fetch API](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API)
