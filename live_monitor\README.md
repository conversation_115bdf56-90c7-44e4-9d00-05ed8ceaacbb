# 直播监控系统 (Live Monitor)

[![Python](https://img.shields.io/badge/Python-3.9+-green)](https://www.python.org/)
[![许可证](https://img.shields.io/badge/许可证-MIT-orange)](LICENSE)
[![Ask DeepWiki](https://deepwiki.com/badge.svg)](https://deepwiki.com/qxqdev/live_monitor)

一款支持多平台（B站、斗鱼、虎牙）的高性能异步直播监控系统。主播状态变化时，通过WxPusher接收通知。
(A high-performance, asynchronous live stream monitoring system for multiple platforms (Bilibili, Douyu, Huya). Get notified via WxPusher when streamer status changes.)

## ✨ 主要特性 (Core Features)

*   支持多平台监控（B站、斗鱼、虎牙）。
*   通过WxPusher实时推送主播状态变更通知。
*   可自定义开播、下播及标题变更通知。
*   支持免打扰时段设置。
*   简洁Web界面管理订阅及设置。

## 📋 系统要求 (Requirements)

*   Python 3.9 或更高版本。
*   WxPusher 账户及应用 App Token（用于接收通知）。
*   `pip` （用于安装依赖）。

## 🚀 快速开始 (Getting Started)

### 1. 安装 (Installation)

```bash
# 克隆仓库 (Clone the repository)
git clone https://github.com/qxqdev/live_monitor.git
cd live_monitor

# 安装依赖 (Install dependencies)
pip install -r requirements.txt
```

### 2. 配置 (Configuration)

1.  复制示例配置文件：
    ```bash
    cp config/config.yaml.example config/config.yaml
    ```
2.  编辑 `config/config.yaml` 并设置您的 WxPusher `app_token`：
    ```yaml
    wxpusher:
      app_token: "YOUR_WXPUSHER_APP_TOKEN" # 替换为您的真实Token
    ```
    您也可以在 `config.yaml` 中配置其他设置，例如 `system.port`（默认为 `58551`）。
    如果直接访问WxPusher有困难，可选配置Cloudflare Worker作为代理（详情请参考 `cloudflare-worker/cloudflare_worker_guide.md` 文件）。

### 3. 运行 (Running)

```bash
python app.py
```

启动后，应用将根据您的配置开始监控。
您可以访问Web界面：`http://localhost:58551/?uid=YOUR_DESIRED_USER_ID`
(请将 `YOUR_DESIRED_USER_ID` 替换为您想使用的用户ID，例如 `my_user`。如果您在 `config.yaml` 中修改了端口，请使用对应端口。)

## 🏗️ 项目结构 (Project Structure)

```
├── app/                  # 应用代码 (Application Code)
│   ├── api/              # API接口 (API Interfaces)
│   ├── core/             # 核心功能 (Core Functionality)
│   │   ├── container.py  # 依赖注入容器 (Dependency Injection Container)
│   │   ├── monitor.py    # 监控器实现 (Monitor Implementation)
│   │   ├── notification_service.py # 通知服务 (Notification Service)
│   │   └── storage.py    # 存储实现 (Storage Implementation)
│   ├── interfaces/       # 接口定义 (Interface Definitions)
│   ├── notifications/    # 通知器基类 (Notifier Base Classes)
│   │   └── base.py       # BaseNotifier
│   ├── notifiers/        # 具体通知器实现 (Specific Notifier Implementations)
│   │   └── wxpusher.py   # WxPusher通知器 (WxPusher Notifier)
│   ├── platforms/        # 平台适配器 (Platform Adapters)
│   │   ├── base.py       # 基础平台类 (Base Platform Class)
│   │   ├── bilibili.py   # B站适配器 (Bilibili Adapter)
│   │   ├── douyu.py      # 斗鱼适配器 (Douyu Adapter)
│   │   └── huya.py       # 虎牙适配器 (Huya Adapter)
│   ├── utils/            # 工具函数 (Utility Functions)
│   │   ├── cache.py      # 缓存工具 (Cache Utility)
│   │   ├── config_loader.py # 配置加载器 (Config Loader)
│   │   ├── helper.py     # 辅助函数 (Helper Functions)
│   │   ├── http_client.py # HTTP客户端 (HTTP Client)
│   │   └── logger.py     # 日志工具 (Logger Utility)
│   └── web/              # Web界面 (Web Interface)
├── cloudflare-worker/    # Cloudflare Worker代理 (Cloudflare Worker Proxy)
│   ├── cloudflare-worker.js      # Worker代码 (Worker Code)
│   └── cloudflare_worker_guide.md # 部署指南 (Deployment Guide)
├── config/               # 配置文件 (Configuration Files)
├── data/                 # 数据文件 (Data Files) - 通常不提交到git (Often not committed to git)
├── logs/                 # 日志文件 (Log Files) - 通常不提交到git (Often not committed to git)
├── tests/                # 测试代码 (Test Code)
│   ├── core/
│   └── notifiers/
├── .github/              # GitHub相关配置 (GitHub specific configuration)
│   └── workflows/        # GitHub Actions 工作流程 (GitHub Actions Workflows)
│       └── python-ci.yml
├── .gitignore
├── LICENSE
├── README.md
└── requirements.txt
```

## 🙏 鸣谢 (Acknowledgements)

本项目的开发得益于以下技术和服务：

*   **[Augment Code](https://augmentcode.com)**：为项目初期代码生成、优化及调试提供支持。
*   **[WxPusher](https://wxpusher.zjiecode.com/docs/#)**：提供便捷的微信推送服务。
*   **Jules (Google AI软件工程助手)**：为后续代码重构、功能添加及开发提供支持。

项目原有代码库鸣谢了在其创建过程中获得的显著AI辅助。

> 📢 **免责声明 (Disclaimer)**：本项目仅用于学习和研究目的，请遵守相关平台的使用条款和规定。

## 📄 许可证 (License)

本项目基于 MIT 许可证。详情请参阅 [LICENSE](LICENSE) 文件。

© 2023-2025
