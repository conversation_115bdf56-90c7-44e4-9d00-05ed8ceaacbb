"""
监控接口定义
"""
from abc import ABC, abstractmethod


class IMonitor(ABC):
    """监控接口"""

    @abstractmethod
    async def init(self) -> None:
        """初始化监控器"""
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        """清理资源"""
        pass

    @abstractmethod
    async def start(self) -> None:
        """启动监控"""
        pass

    @abstractmethod
    async def stop(self) -> None:
        """停止监控"""
        pass
