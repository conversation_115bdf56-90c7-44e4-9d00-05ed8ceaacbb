import logging
from typing import Dict, Any, Optional, List
import asyncio
import aiosmtplib
from email.message import EmailMessage

from libs.plugin_interfaces.base_notifier_v1 import BaseNotifierV1

logger = logging.getLogger(__name__)

class EmailNotifier(BaseNotifierV1):
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(notifier_type="email", config=config if config else {})
        
        self.smtp_host = self.config.get("smtp_host")
        self.smtp_port = int(self.config.get("smtp_port", 587)) # Default to 587 for TLS
        self.smtp_user = self.config.get("smtp_user")
        self.smtp_password = self.config.get("smtp_password")
        self.sender_email = self.config.get("sender_email")
        
        # Determine TLS/SSL settings based on port and explicit config
        # If use_ssl is explicitly true, it takes precedence.
        # If use_tls is explicitly true (or port is 587 and SSL is not true), use TLS.
        self.use_ssl = self.config.get("use_ssl", False)
        default_tls = (self.smtp_port == 587)
        self.use_tls = self.config.get("use_tls", default_tls)

        if self.use_ssl and self.use_tls:
            logger.warning("EmailNotifier: Both use_ssl and use_tls are true. Preferring SSL if port is 465, else TLS.")
            if self.smtp_port == 465:
                self.use_tls = False
            else:
                self.use_ssl = False
        elif self.use_ssl: # SSL is true, TLS must be false
             self.use_tls = False
        elif self.use_tls: # TLS is true, SSL must be false
             self.use_ssl = False
        
        # Validate required fields
        if not self.smtp_host:
            logger.error("EmailNotifier: 'smtp_host' is missing from config. Email notifications will fail.")
        if not self.sender_email:
            logger.error("EmailNotifier: 'sender_email' is missing from config. Email notifications will fail.")


    async def init(self) -> None:
        """Minimal init for EmailNotifier using aiosmtplib.send per call."""
        logger.info(
            f"EmailNotifier for type '{self.notifier_type}' initialized. "
            f"Host: {self.smtp_host}:{self.smtp_port}, User: {self.smtp_user}, "
            f"Sender: {self.sender_email}, SSL: {self.use_ssl}, TLS: {self.use_tls}"
        )
        # No persistent client to initialize if using aiosmtplib.send directly in send()

    async def send(self, title: str, body: str, target_users: List[str], details: Optional[Dict[str, Any]] = None) -> None:
        if not self.smtp_host or not self.sender_email:
            logger.error("EmailNotifier: SMTP host or sender email not configured. Cannot send email.")
            return
        if not target_users:
            logger.warning("EmailNotifier: No target_users (recipient emails) provided.")
            return

        # Determine content type from details or default to plain text
        content_type = "plain"
        if details and isinstance(details.get("html_content"), str) and details.get("html_content"):
            body = details["html_content"] # Use HTML content if provided
            content_type = "html"
        
        for recipient_email in target_users:
            msg = EmailMessage()
            msg["Subject"] = title
            msg["From"] = self.sender_email
            msg["To"] = recipient_email
            
            if content_type == "html":
                msg.set_content("Please enable HTML to view this message.") # Fallback for non-HTML clients
                msg.add_alternative(body, subtype='html')
            else:
                msg.set_content(body)

            try:
                # `aiosmtplib.send` handles connection, login, sending, and closing.
                # `start_tls` parameter in `aiosmtplib.send` is effectively what `use_tls` means here.
                # If `use_ssl` is True, `aiosmtplib.send` internally uses `aiosmtplib.SMTP(use_ssl=True, ...)`.
                # The `aiosmtplib.send` function auto-detects `start_tls` based on `use_tls` if port is not 465.
                # If port is 465, `use_tls` should be False, and `use_ssl` is implicitly True or can be explicit.
                
                # For aiosmtplib.send:
                # - use_tls=True will attempt STARTTLS if server supports it (typically on port 587 or 25).
                # - use_tls=False (default)
                # - To use SSL directly (implicit TLS on port 465), you typically instantiate SMTP client with use_ssl=True.
                #   aiosmtplib.send doesn't have a direct 'use_ssl' param, it's inferred if port is 465
                #   or if you pass an already connected SMTP client instance.
                #   For simplicity with `send`, if `self.use_ssl` is true, we rely on port 465 convention.
                #   If `self.use_tls` is true and port is not 465, `send` will try STARTTLS.

                send_kwargs = {
                    "hostname": self.smtp_host,
                    "port": self.smtp_port,
                    "username": self.smtp_user,
                    "password": self.smtp_password,
                }
                if self.use_ssl: # Typically for port 465
                    # aiosmtplib.send will handle SSL if port is 465.
                    # If another port is used with SSL, one might need to use SMTP client directly.
                    # For now, assume port 465 implies SSL for aiosmtplib.send or it's handled by library.
                    # If port is 465, use_tls is typically False.
                    send_kwargs["use_tls"] = False 
                elif self.use_tls: # Typically for port 587 or 25 transitioning to TLS
                    send_kwargs["use_tls"] = True
                else: # Plain connection (port 25, no TLS/SSL)
                    send_kwargs["use_tls"] = False


                await aiosmtplib.send(msg, **send_kwargs) # type: ignore
                logger.info(f"Email sent to {recipient_email} via {self.notifier_type}. Subject: {title}")

            except aiosmtplib.SMTPException as e:
                logger.error(f"SMTP error sending email to {recipient_email}: {e.code} {e.message}", exc_info=True)
            except Exception as e:
                logger.error(f"Generic error sending email to {recipient_email}: {e}", exc_info=True)

    async def close(self) -> None:
        """No persistent client to close if using aiosmtplib.send per call."""
        logger.info(f"EmailNotifier for type '{self.notifier_type}' closed (no persistent client).")
