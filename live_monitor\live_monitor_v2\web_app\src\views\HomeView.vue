<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { useSubscriptionStore } from '@/stores/subscriptions'
import AddSubscriptionForm from '@/components/AddSubscriptionForm.vue'
import SubscriptionList from '@/components/SubscriptionList.vue'
import NotificationChannelList from '@/components/NotificationChannelList.vue'
import EditNotificationChannelForm from '@/components/EditNotificationChannelForm.vue'
import type { NotificationChannel } from '@/types'

const store = useSubscriptionStore()

// Refs for Notification Channel functionality
const showChannelForm = ref(false)
const channelToEditData = ref<NotificationChannel | null>(null)

// Local ref for the user ID input field
const currentUserIdInput = ref(store.userId || '') // Initialize with store's userId if present

// Computed property to check if User ID is set in store
const isUserIdSet = computed(() => store.hasUserId)

function handleSetUserId() {
  if (currentUserIdInput.value.trim()) {
    store.setUserId(currentUserIdInput.value.trim())
  } else {
    // Optionally handle empty input submission, e.g., clear store userId or show local error
    // The store's setUserId already handles clearing if newUserId is empty.
    store.setUserId('') 
  }
}

// Watch for changes in the store's userId (e.g., if set from elsewhere or cleared)
// and update the local input field accordingly.
watch(() => store.userId, (newVal) => {
  currentUserIdInput.value = newVal || ''
  if (newVal) { // If a new valid User ID is set in store, fetch subscriptions
    // store.fetchSubscriptions(); // fetchSubscriptions is called within store.setUserId
  }
})

// Initial fetch if userId is already in store (e.g. from persisted state or direct set)
// if (store.userId) {
//   store.fetchSubscriptions(); // fetchSubscriptions is called within store.setUserId
// }

// --- Notification Channel Methods ---
function openAddChannelForm() {
  channelToEditData.value = null
  showChannelForm.value = true
}

function handleEditChannel(channel: NotificationChannel) {
  channelToEditData.value = channel
  showChannelForm.value = true
}

function handleSaveSuccess() {
  showChannelForm.value = false
  channelToEditData.value = null
  // List will auto-refresh as channel store fetches on successful set/delete
}

function handleCancelForm() {
  showChannelForm.value = false
  channelToEditData.value = null
}
</script>

<template>
  <main class="home-view">
    <div class="user-id-section">
      <h2>Enter User ID</h2>
      <form @submit.prevent="handleSetUserId" class="user-id-form">
        <input 
          type="text" 
          v-model="currentUserIdInput" 
          placeholder="Enter your User ID" 
          aria-label="User ID"
        />
        <button type="submit">Set User ID</button>
      </form>
      <p v-if="store.userId" class="current-user">Current User ID: <strong>{{ store.userId }}</strong></p>
    </div>

    <div v-if="isUserIdSet" class="content-section">
      <!-- Subscription Management -->
      <div class="subscription-management">
        <h3>Subscription Management</h3>
        <AddSubscriptionForm />
        <SubscriptionList />
      </div>

      <hr class="section-divider" />

      <!-- Notification Channel Management -->
      <div class="notification-channel-management">
        <h3>Notification Channels</h3>
        <div v-if="showChannelForm">
          <EditNotificationChannelForm
            :channel-to-edit="channelToEditData"
            @save-success="handleSaveSuccess"
            @cancel="handleCancelForm"
          />
        </div>
        <div v-else>
          <button @click="openAddChannelForm" class="add-channel-btn">Add Notification Channel</button>
          <NotificationChannelList @edit-channel="handleEditChannel" />
        </div>
      </div>
    </div>
    <div v-else class="prompt-message">
      <p>Please set a User ID to manage your subscriptions.</p>
    </div>
    
    <!-- Global error display, could be a separate component -->
    <div v-if="store.error && !isUserIdSet" class="error-message global-error">
      {{ store.error }}
    </div>
  </main>
</template>

<style scoped>
.home-view {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.user-id-section {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f0f8ff; /* Light blue background */
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.subscription-management, .notification-channel-management {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  background-color: #f9f9f9;
}

.section-divider {
  margin: 30px 0;
  border: 0;
  border-top: 1px solid #ddd;
}

.add-channel-btn {
  margin-bottom: 15px;
  padding: 10px 15px;
  background-color: #007bff; /* Blue */
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.95rem;
}

.add-channel-btn:hover {
  background-color: #0056b3;
}

.user-id-section h2 {
  text-align: center;
  color: #333;
  margin-bottom: 15px;
}

.user-id-form {
  display: flex;
  gap: 10px;
  align-items: center;
}

.user-id-form input[type="text"] {
  flex-grow: 1;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
}

.user-id-form button {
  padding: 10px 20px;
  background-color: #28a745; /* Green */
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s;
}

.user-id-form button:hover {
  background-color: #218838;
}

.current-user {
  text-align: center;
  margin-top: 10px;
  font-size: 0.9em;
  color: #555;
}

.content-section {
  margin-top: 20px;
}

.prompt-message {
  text-align: center;
  color: #777;
  margin-top: 20px;
  padding: 15px;
  background-color: #fff3cd; /* Light yellow */
  border: 1px solid #ffeeba;
  border-radius: 4px;
}

.global-error { /* For errors shown when userId is not set, or general store errors */
  margin-top: 15px;
  /* Styles for global error are defined in AddSubscriptionForm/SubscriptionList for context-specific errors */
}
.error-message { /* General error styling if needed outside components */
  color: red;
  background-color: #ffe0e0;
  border: 1px solid red;
  border-radius: 4px;
  padding: 10px;
  margin-top: 10px;
  text-align: center;
}
</style>
