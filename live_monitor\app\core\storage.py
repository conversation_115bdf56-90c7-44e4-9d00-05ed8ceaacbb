"""
存储模块 - 异步版本
"""
import os
import json
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional

from app.interfaces.storage import IStorage
from app.constants import DEFAULT_SUBSCRIPTION_SETTINGS
from app.utils.logger import setup_logger

logger = setup_logger('core.storage')

class AsyncStorage(IStorage):
    """异步存储类，实现IStorage接口"""

    def __init__(self, data_dir: str = None):
        """
        初始化存储

        Args:
            data_dir: 数据目录
        """
        if data_dir is None:
            data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data')

        self.data_dir = data_dir
        self.subscriptions_file = os.path.join(data_dir, 'subscriptions.json')
        self.status_cache_file = os.path.join(data_dir, 'status_cache.json')
        self._lock = asyncio.Lock()
        self._status_cache: Dict[str, Dict[str, Any]] = {}
        self._initialized = False

        # 确保数据目录存在
        os.makedirs(data_dir, exist_ok=True)

    async def load_subscriptions(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        加载订阅数据

        Returns:
            dict: 订阅数据
        """
        async with self._lock:
            try:
                if os.path.exists(self.subscriptions_file):
                    with open(self.subscriptions_file, 'r', encoding='utf-8') as f:
                        return json.load(f)
                return {}
            except Exception as e:
                logger.error(f"加载订阅数据失败: {str(e)}")
                return {}

    async def save_subscriptions(self, data: Dict[str, List[Dict[str, Any]]]) -> bool:
        """
        保存订阅数据

        Args:
            data: 订阅数据

        Returns:
            bool: 是否保存成功
        """
        async with self._lock:
            try:
                with open(self.subscriptions_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                return True
            except Exception as e:
                logger.error(f"保存订阅数据失败: {str(e)}")
                return False

    async def add_subscription(self, uid: str, platform: str, room_id: str, settings: Dict = None) -> bool:
        """
        添加订阅

        Args:
            uid: 用户ID
            platform: 平台
            room_id: 房间ID
            settings: 自定义设置

        Returns:
            bool: 是否成功
        """
        try:
            subscriptions = await self.load_subscriptions()

            if uid not in subscriptions:
                subscriptions[uid] = []

            # 检查是否已经订阅
            for sub in subscriptions[uid]:
                if sub['platform'] == platform and sub['room_id'] == room_id:
                    # 如果已订阅但提供了新设置，则更新设置
                    if settings:
                        sub['settings'] = settings
                        await self.save_subscriptions(subscriptions)
                        return True
                    return True

            # 使用默认设置
            default_settings = DEFAULT_SUBSCRIPTION_SETTINGS

            # 添加新订阅
            subscriptions[uid].append({
                'platform': platform,
                'room_id': room_id,
                'added_at': datetime.now().isoformat(),
                'settings': settings or default_settings
            })

            await self.save_subscriptions(subscriptions)
            return True

        except Exception as e:
            logger.error(f"添加订阅失败: {str(e)}")
            return False

    async def remove_subscription(self, uid: str, platform: str, room_id: str) -> bool:
        """
        移除订阅

        Args:
            uid: 用户ID
            platform: 平台
            room_id: 房间ID

        Returns:
            bool: 是否成功
        """
        try:
            subscriptions = await self.load_subscriptions()

            if uid not in subscriptions:
                return False

            # 查找并移除订阅
            user_subs = subscriptions[uid]
            for i, sub in enumerate(user_subs):
                if sub['platform'] == platform and sub['room_id'] == room_id:
                    user_subs.pop(i)
                    await self.save_subscriptions(subscriptions)

                    # 检查是否还有其他用户订阅了该主播
                    # 如果没有，则清理缓存
                    await self._clean_unused_streamer_cache(platform, room_id)

                    return True

            return False

        except Exception as e:
            logger.error(f"移除订阅失败: {str(e)}")
            return False

    async def get_user_subscriptions(self, uid: str) -> List[Dict[str, Any]]:
        """
        获取用户订阅

        Args:
            uid: 用户ID

        Returns:
            list: 用户订阅列表
        """
        subscriptions = await self.load_subscriptions()
        return subscriptions.get(uid, [])

    async def get_all_subscriptions(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        获取所有订阅

        Returns:
            dict: 所有订阅
        """
        return await self.load_subscriptions()

    async def init(self) -> None:
        """初始化存储"""
        if self._initialized:
            return

        logger.info("初始化存储")
        # 加载状态缓存
        self._status_cache = await self.load_status_cache()
        self._initialized = True

    async def cleanup(self) -> None:
        """清理资源"""
        if not self._initialized:
            return

        logger.info("清理存储资源")
        # 清理不再使用的缓存
        await self.cleanup_unused_cache()
        # 保存状态缓存
        await self.save_status_cache(self._status_cache)
        self._initialized = False

    async def load_status_cache(self) -> Dict[str, Dict[str, Any]]:
        """
        加载状态缓存

        Returns:
            Dict[str, Dict[str, Any]]: 状态缓存，格式为 {platform_room_id: {状态信息}}
        """
        async with self._lock:
            try:
                if os.path.exists(self.status_cache_file):
                    with open(self.status_cache_file, 'r', encoding='utf-8') as f:
                        return json.load(f)
                return {}
            except Exception as e:
                logger.error(f"加载状态缓存失败: {str(e)}")
                return {}

    async def save_status_cache(self, data: Dict[str, Dict[str, Any]]) -> bool:
        """
        保存状态缓存

        Args:
            data: 状态缓存，格式为 {platform_room_id: {状态信息}}

        Returns:
            bool: 是否保存成功
        """
        async with self._lock:
            try:
                # 更新内存中的状态缓存
                self._status_cache = data.copy()

                # 保存到文件
                with open(self.status_cache_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                return True
            except Exception as e:
                logger.error(f"保存状态缓存失败: {str(e)}")
                return False

    async def get_status_cache(self, platform: str, room_id: str) -> Optional[Dict[str, Any]]:
        """
        获取指定主播的状态缓存

        Args:
            platform: 平台名称
            room_id: 房间ID

        Returns:
            Optional[Dict[str, Any]]: 状态缓存，如果不存在则返回None
        """
        cache_key = f"{platform}_{room_id}"
        return self._status_cache.get(cache_key)

    async def update_status_cache(self, platform: str, room_id: str, status: Dict[str, Any]) -> bool:
        """
        更新指定主播的状态缓存

        Args:
            platform: 平台名称
            room_id: 房间ID
            status: 状态信息

        Returns:
            bool: 是否更新成功
        """
        try:
            cache_key = f"{platform}_{room_id}"
            self._status_cache[cache_key] = status
            # 定期保存状态缓存，避免频繁IO
            if len(self._status_cache) % 10 == 0:
                await self.save_status_cache(self._status_cache)
            return True
        except Exception as e:
            logger.error(f"更新状态缓存失败: {str(e)}")
            return False

    async def update_subscription_settings(self, uid: str, platform: str, room_id: str, settings: Dict) -> bool:
        """
        更新订阅设置

        Args:
            uid: 用户ID
            platform: 平台
            room_id: 房间ID
            settings: 新设置

        Returns:
            bool: 是否成功
        """
        try:
            subscriptions = await self.load_subscriptions()

            if uid not in subscriptions:
                return False

            # 查找并更新设置
            for sub in subscriptions[uid]:
                if sub['platform'] == platform and sub['room_id'] == room_id:
                    sub['settings'] = settings
                    await self.save_subscriptions(subscriptions)
                    return True

            return False

        except Exception as e:
            logger.error(f"更新订阅设置失败: {str(e)}")
            return False

    async def _clean_unused_streamer_cache(self, platform: str, room_id: str) -> None:
        """
        清理不再被任何用户订阅的主播缓存

        Args:
            platform: 平台
            room_id: 房间ID
        """
        try:
            # 获取所有订阅
            subscriptions = await self.load_subscriptions()

            # 检查是否还有其他用户订阅了该主播
            has_other_subscribers = False
            for user_subs in subscriptions.values():
                for sub in user_subs:
                    if sub['platform'] == platform and sub['room_id'] == room_id:
                        has_other_subscribers = True
                        break
                if has_other_subscribers:
                    break

            # 如果没有其他用户订阅，则清理缓存
            if not has_other_subscribers:
                cache_key = f"{platform}_{room_id}"
                if cache_key in self._status_cache:
                    async with self._lock:
                        if cache_key in self._status_cache:
                            del self._status_cache[cache_key]
                            logger.info(f"清理不再使用的主播缓存: {platform} {room_id}")
                            # 保存更新后的缓存
                            await self.save_status_cache(self._status_cache)
        except Exception as e:
            logger.error(f"清理主播缓存失败: {platform} {room_id}, 错误: {str(e)}")

    async def cleanup_unused_cache(self) -> None:
        """清理所有不再被任何用户订阅的主播缓存"""
        try:
            # 获取所有订阅
            subscriptions = await self.load_subscriptions()

            # 收集所有被订阅的主播
            active_streamers = set()
            for user_subs in subscriptions.values():
                for sub in user_subs:
                    cache_key = f"{sub['platform']}_{sub['room_id']}"
                    active_streamers.add(cache_key)

            # 清理不再被订阅的主播缓存
            async with self._lock:
                inactive_keys = [k for k in self._status_cache.keys() if k not in active_streamers]
                for k in inactive_keys:
                    del self._status_cache[k]

                # 保存更新后的缓存
                if inactive_keys:
                    await self.save_status_cache(self._status_cache)
                    logger.info(f"已清理 {len(inactive_keys)} 个不再使用的主播缓存")
        except Exception as e:
            logger.error(f"清理缓存失败: {str(e)}")

# 注意: 不再创建全局存储实例
# 而是通过依赖注入容器获取实例
