import httpx
import logging
import json # For potential manual JSON parsing if content-type is problematic
from typing import Dict, Any, Optional
from datetime import datetime, timezone # For start_time

from libs.plugin_interfaces.platform_adapter_v1 import PlatformAdapterV1

logger = logging.getLogger(__name__)

# Default Huya API URLs (can be overridden by config passed to __init__)
DEFAULT_HUYA_API_URL = "https://mp.huya.com/cache.php?m=Live&do=profileRoom&roomid={}"
DEFAULT_HUYA_ROOM_URL_TEMPLATE = "https://www.huya.com/{}"
DEFAULT_HUYA_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Referer': 'https://www.huya.com/' # Referer is often important for Huya
}

class HuyaAdapter(PlatformAdapterV1):
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(platform_name="huya", config=config)
        self.api_url_template = self.config.get("api_url_template", DEFAULT_HUYA_API_URL)
        self.room_url_template = self.config.get("room_url_template", DEFAULT_HUYA_ROOM_URL_TEMPLATE)
        self.headers = self.config.get("headers", DEFAULT_HUYA_HEADERS)
        self.http_client: Optional[httpx.AsyncClient] = None
        self.display_name = self.config.get("display_name", "虎牙") # Or "Huya"

    async def init(self) -> None:
        """Initialize the HTTP client."""
        if self.http_client is None:
            self.http_client = httpx.AsyncClient(headers=self.headers, timeout=10.0)
        logger.info(f"HuyaAdapter for '{self.platform_name}' initialized.")

    async def _fetch_json_from_html_content_type(self, url: str) -> Optional[Dict[str, Any]]:
        """Fetches data from Huya, which often returns JSON with text/html content-type."""
        if not self.http_client:
            await self.init()
        
        if not self.http_client: # Should not happen if init works
            logger.error("HTTP client not initialized in HuyaAdapter.")
            return None

        try:
            response = await self.http_client.get(url)
            response.raise_for_status()
            # Huya API might return JSON content with a 'text/html' Content-Type.
            # We attempt to parse it as JSON directly.
            # response.json() might fail if content_type is not application/json.
            # A robust way is to use response.text and then json.loads().
            try:
                return json.loads(response.text)
            except json.JSONDecodeError as json_err:
                logger.error(f"JSON decode error for URL {url}: {json_err}. Response text: {response.text[:200]}...")
                return None
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error fetching {url}: {e.response.status_code} - {e.response.text}")
        except httpx.RequestError as e:
            logger.error(f"Request error fetching {url}: {e}")
        except Exception as e:
            logger.error(f"Generic error fetching or parsing JSON from {url}: {e}")
        return None

    async def get_streamer_status(self, room_id: str) -> Optional[Dict[str, Any]]:
        """Fetch Huya streamer status."""
        api_url = self.api_url_template.format(room_id)
        data = await self._fetch_json_from_html_content_type(api_url)

        if not data or data.get("status") != 200 or "data" not in data or not data["data"]:
            error_msg = data.get("message", "Failed to fetch room data or error in response.") if data else "No data from API."
            logger.warning(f"Huya: Could not get room info for room_id {room_id}. Message: {error_msg}")
            return None

        room_info = data.get("data", {})
        profile_info = room_info.get("profileInfo") 
        live_data = room_info.get("liveData") # This might be None if streamer info is minimal

        if not profile_info: # profileInfo is essential for anchor name
            logger.warning(f"Huya: 'profileInfo' missing for room_id {room_id}.")
            # If we can't get anchor name, it might be better to return None or use a placeholder.
            # For now, let's try to proceed if other critical info is present.
            anchor_name = "Unknown Anchor (profileInfo missing)"
        else:
            anchor_name = profile_info.get("nick", "Unknown Anchor")


        # 'liveStatus' or 'realLiveStatus' can be "ON", "OFF", "REPLAY" etc.
        # Prioritize 'realLiveStatus' if available, then 'liveStatus'. Default to "OFF".
        live_status_str = room_info.get('realLiveStatus', room_info.get('liveStatus', 'OFF')).upper()
        is_live = live_status_str == "ON"
        
        title = live_data.get("introduction", "") if live_data else \
                room_info.get("roomName", "Unknown Title") # Fallback to roomName if liveData is absent

        # Huya API doesn't directly give a simple viewer count or a reliable start time easily from this endpoint.
        # '观众人数' (viewer count) might be in page source or another API.
        # 'startTime' in liveData might be available but needs checking its format and reliability.
        # For MVP, these can be None.
        viewer_count: Optional[int] = None
        start_time_obj: Optional[datetime] = None
        
        if is_live and live_data:
            # Example: if liveData has 'startTime' as unix timestamp
            # start_time_unix_str = live_data.get('startTime') 
            # if start_time_unix_str:
            #     try:
            #         start_time_obj = datetime.fromtimestamp(int(start_time_unix_str), tz=timezone.utc)
            #     except ValueError: 
            #         logger.warning(f"Huya: Could not parse startTime '{start_time_unix_str}' for room {room_id}")
            # For Huya, this specific API `profileRoom` does not seem to reliably provide viewer count or exact live start time.
            # These might require scraping the room page or using other internal APIs not intended for public use.
            # The old adapter did not extract these.
            pass


        status_dict: Dict[str, Any] = {
            "platform_name": self.platform_name,
            "room_id": str(room_id),
            "anchor_name": anchor_name,
            "title": title,
            "live_status": is_live,
            "stream_url": self.room_url_template.format(room_id),
            "cover_image_url": profile_info.get("avatar180") if profile_info else None, # Avatar as a proxy for cover
            "viewer_count": viewer_count, 
            "start_time": start_time_obj,
        }
        return status_dict

    def get_platform_display_name(self) -> str:
        """Return a user-friendly display name for the platform."""
        return self.display_name

    async def close(self) -> None:
        """Close the HTTP client."""
        if self.http_client:
            await self.http_client.aclose()
            self.http_client = None
        logger.info(f"HuyaAdapter for '{self.platform_name}' closed.")
