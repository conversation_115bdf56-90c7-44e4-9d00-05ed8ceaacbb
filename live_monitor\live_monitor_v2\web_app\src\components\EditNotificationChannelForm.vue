<template>
  <div class="edit-notification-channel-form">
    <h4>{{ formTitle }}</h4>
    <form @submit.prevent="submitForm">
      <div class="form-group">
        <label for="channelType">Channel Type:</label>
        <input type="text" id="channelType" v-model="formData.channel_type" :disabled="isEditMode" required />
        <small v-if="isEditMode">Channel type cannot be changed during an edit.</small>
      </div>

      <div class="form-group">
        <label for="channelUid">Channel UID (e.g., Email Address, Webhook URL, WxPusher UID):</label>
        <input type="text" id="channelUid" v_model="formData.channel_uid" required />
      </div>

      <div class="form-actions">
        <button type="submit" :disabled="isLoading">
          {{ isLoading ? (isEditMode ? 'Saving...' : 'Adding...') : (isEditMode ? 'Save Changes' : 'Add Channel') }}
        </button>
        <button type="button" @click="cancelForm" :disabled="isLoading">Cancel</button>
      </div>

      <div v_if="error" class="error-message">{{ error }}</div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue';
import { useNotificationChannelStore } from '@/stores/notificationChannels';
import { useSubscriptionStore } from '@/stores/subscriptions'; // To get userId
import type { NotificationChannel, NotificationChannelCreate } from '@/types';

const props = defineProps<{
  channelToEdit?: NotificationChannel | null; // Optional: pass null or undefined for new channel
}>();

const emit = defineEmits<{
  (e: 'saveSuccess'): void;
  (e: 'cancel'): void;
}>();

const channelStore = useNotificationChannelStore();
const subscriptionStore = useSubscriptionStore(); // For userId

const formData = ref<NotificationChannelCreate & { channel_type?: string }>({
  channel_type: '',
  channel_uid: '',
});

const isEditMode = computed(() => !!props.channelToEdit);
const formTitle = computed(() => (isEditMode.value ? 'Edit Notification Channel' : 'Add New Notification Channel'));

// Store's loading and error for form submission context
const isLoading = computed(() => channelStore.isLoading);
const error = computed(() => channelStore.error); // Display error from store related to setChannel

// Initialize form data when channelToEdit prop changes or on mount
watch(
  () => props.channelToEdit,
  (newVal) => {
    if (newVal) {
      formData.value.channel_type = newVal.channel_type;
      formData.value.channel_uid = newVal.channel_uid;
    } else {
      resetForm();
    }
  },
  { immediate: true } // Run the watcher immediately on component mount
);

onMounted(() => {
    // If not editing, ensure form is reset (e.g. if component is reused)
    if (!isEditMode.value) {
        resetForm();
    }
});

function resetForm() {
  formData.value.channel_type = '';
  formData.value.channel_uid = '';
  channelStore.error = null; // Clear any previous errors from the store
}

async function submitForm() {
  if (!subscriptionStore.userId) {
    channelStore.error = "User ID is not available. Cannot save channel.";
    return;
  }
  if (!formData.value.channel_type || !formData.value.channel_uid) {
    channelStore.error = "Channel Type and UID are required.";
    return;
  }

  // Clear previous errors before new submission attempt
  channelStore.error = null;

  try {
    // The `setChannel` action in the store takes userId, channelType, and channelUid.
    // It will use PUT, which is suitable for create or update.
    await channelStore.setChannel(
      subscriptionStore.userId,
      formData.value.channel_type,
      formData.value.channel_uid
    );

    // Check if there was an error set in the store during the action
    if (!channelStore.error) {
        emit('saveSuccess');
        resetForm(); // Reset form after successful submission
    }
    // If channelStore.error is set, it will be displayed by the computed 'error' property
  } catch (e) {
    // Error should be set in the store by the action itself.
    // No need to set channelStore.error here unless the action doesn't do it.
    console.error("Form submission error:", e);
  }
}

function cancelForm() {
  resetForm();
  emit('cancel');
}
</script>

<style scoped>
.edit-notification-channel-form {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: #f9f9f9;
}
.form-group {
  margin-bottom: 15px;
}
.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}
.form-group input[type="text"] {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
}
.form-group input[disabled] {
  background-color: #e9ecef;
  opacity: 0.7;
}
.form-group small {
  display: block;
  margin-top: 3px;
  font-size: 0.8em;
  color: #666;
}
.form-actions {
  margin-top: 20px;
  text-align: right;
}
.form-actions button {
  margin-left: 10px;
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
.form-actions button[type="submit"] {
  background-color: #007bff;
  color: white;
}
.form-actions button[type="button"] {
  background-color: #6c757d;
  color: white;
}
.form-actions button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.error-message {
  margin-top: 15px;
  padding: 10px;
  color: #D8000C;
  background-color: #FFD2D2;
  border-radius: 4px;
}
</style>
