import abc
from typing import Dict, Optional

class BaseNotifier(abc.ABC):

    @abc.abstractmethod
    async def send(self, title: str, body: str, details: Optional[Dict] = None) -> None:
        """
        Sends a notification.

        Args:
            title: The title of the notification.
            body: The main content of the notification.
            details: An optional dictionary for notifier-specific parameters or structured data.
        """
        pass
