<script setup lang="ts">
import { ref } from 'vue'
import { useSubscriptionStore } from '@/stores/subscriptions'

const store = useSubscriptionStore()

const platformOptions = ['bilibili', 'douyu', 'huya'];
const platformName = ref(''); // Initialize to empty string for default disabled option
const roomId = ref('')

async function handleSubmit() {
  // The .trim() for platformName is not strictly necessary for a select,
  // but doesn't hurt if value could somehow be whitespace.
  // For a select with a default disabled empty option, platformName.value will be non-empty if user selects one.
  if (!platformName.value || !roomId.value.trim()) {
    store.error = 'Platform and Room ID cannot be empty.'
    return
  }
  await store.addSubscription(platformName.value, roomId.value.trim())
  if (!store.error) { // Clear form only on success
    platformName.value = ''
    roomId.value = ''
  }
}
</script>

<template>
  <div class="add-subscription-form">
    <h3>Add New Subscription</h3>
    <form @submit.prevent="handleSubmit">
      <div class="form-group">
        <label for="platform">Platform:</label>
        <select id="platform" v-model="platformName" required>
          <option value="" disabled>-- Select Platform --</option>
          <option v-for="p in platformOptions" :key="p" :value="p">{{ p }}</option>
        </select>
      </div>
      <div class="form-group">
        <label for="roomId">Room ID:</label>
        <input type="text" id="roomId" v.model.trim="roomId" placeholder="Enter Room ID" required />
      </div>
      <button type="submit" :disabled="store.isLoading">
        {{ store.isLoading ? 'Adding...' : 'Add Subscription' }}
      </button>
      <div v-if="store.error" class="error-message form-error">{{ store.error }}</div>
    </form>
  </div>
</template>

<style scoped>
.add-subscription-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.add-subscription-form h3 {
  margin-top: 0;
  margin-bottom: 15px;
  text-align: center;
  color: #333;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #555;
}

.form-group input[type="text"],
.form-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box; /* Important for width 100% */
}

button[type="submit"] {
  width: 100%;
  padding: 10px 15px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s ease;
}

button[type="submit"]:hover {
  background-color: #0056b3;
}

button[type="submit"]:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.form-error { /* Specific error styling for form if needed, or use general .error-message */
  margin-top: 10px;
  /* color: red; background-color: #ffe0e0; padding: 10px; border-radius: 4px; */
}
.error-message { /* Ensure general error message style is available */
  color: red;
  background-color: #ffe0e0;
  border: 1px solid red;
  border-radius: 4px;
  padding: 10px;
  margin-top: 10px;
  text-align: center;
}
</style>
