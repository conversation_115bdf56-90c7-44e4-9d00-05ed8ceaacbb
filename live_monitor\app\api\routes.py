"""
API路由模块
"""
from aiohttp import web

from app.core.container import container
from app.platforms import PLATFORMS
from app.constants import DEFAULT_SUBSCRIPTION_SETTINGS
from app.utils.logger import setup_logger

logger = setup_logger('api.routes')

routes = web.RouteTableDef()

@routes.get('/api/subscriptions')
async def get_subscriptions(request: web.Request) -> web.Response:
    """
    获取用户订阅列表

    Query参数:
        uid: 用户ID
    """
    uid = request.query.get('uid')
    if not uid:
        return web.json_response({
            'success': False,
            'message': '缺少用户ID'
        })

    try:
        # 获取存储实例
        storage = container.get('storage')

        # 加载订阅数据
        user_subs = await storage.get_user_subscriptions(uid)

        # 获取每个订阅的最新状态
        result = []
        for sub in user_subs:
            platform_getter = PLATFORMS.get(sub['platform'])
            if platform_getter:
                # 调用getter函数获取平台实例
                platform = platform_getter()
                status = await platform.get_room_status(sub['room_id'])
                if status:
                    # 将订阅设置添加到状态信息中
                    status['settings'] = sub.get('settings', DEFAULT_SUBSCRIPTION_SETTINGS)
                    result.append(status)

        return web.json_response({
            'success': True,
            'subscriptions': result
        })
    except Exception as e:
        logger.error(f"获取订阅列表失败: {str(e)}")
        return web.json_response({
            'success': False,
            'message': str(e)
        })

@routes.post('/api/subscribe')
async def subscribe(request: web.Request) -> web.Response:
    """
    添加订阅

    请求体:
        uid: 用户ID
        platform: 平台
        room_id: 房间ID
    """
    try:
        data = await request.json()
        uid = data.get('uid')
        platform_name = data.get('platform')
        room_id = data.get('room_id')

        if not all([uid, platform_name, room_id]):
            return web.json_response({
                'success': False,
                'message': '参数不完整'
            })

        # 验证平台和房间号是否有效
        platform_getter = PLATFORMS.get(platform_name)
        if not platform_getter:
            return web.json_response({
                'success': False,
                'message': '不支持的平台'
            })

        # 调用getter函数获取平台实例
        platform = platform_getter()
        status = await platform.get_room_status(room_id)
        if not status:
            return web.json_response({
                'success': False,
                'message': '房间号无效'
            })

        # 获取存储实例
        storage = container.get('storage')

        # 使用默认设置
        default_settings = DEFAULT_SUBSCRIPTION_SETTINGS

        # 添加订阅
        success = await storage.add_subscription(uid, platform_name, room_id, default_settings)
        if success:
            return web.json_response({
                'success': True,
                'message': '订阅成功'
            })
        else:
            return web.json_response({
                'success': False,
                'message': '添加订阅失败'
            })

    except Exception as e:
        logger.error(f"添加订阅失败: {str(e)}")
        return web.json_response({
            'success': False,
            'message': str(e)
        })

@routes.post('/api/unsubscribe')
async def unsubscribe(request: web.Request) -> web.Response:
    """
    取消订阅

    请求体:
        uid: 用户ID
        platform: 平台
        room_id: 房间ID
    """
    try:
        data = await request.json()
        uid = data.get('uid')
        platform_name = data.get('platform')
        room_id = data.get('room_id')

        if not all([uid, platform_name, room_id]):
            return web.json_response({
                'success': False,
                'message': '参数不完整'
            })

        # 获取存储实例
        storage = container.get('storage')

        # 移除订阅
        success = await storage.remove_subscription(uid, platform_name, room_id)
        if success:
            return web.json_response({
                'success': True,
                'message': '取消订阅成功'
            })
        else:
            return web.json_response({
                'success': False,
                'message': '未找到该订阅'
            })

    except Exception as e:
        logger.error(f"取消订阅失败: {str(e)}")
        return web.json_response({
            'success': False,
            'message': str(e)
        })

@routes.get('/api/platforms')
async def get_platforms(request: web.Request) -> web.Response:
    """获取支持的平台列表"""
    try:
        platforms = []
        for platform_name, platform_getter in PLATFORMS.items():
            # 调用getter函数获取平台实例
            platform = platform_getter()
            platforms.append(platform.get_platform_info())

        return web.json_response({
            'success': True,
            'platforms': platforms
        })
    except Exception as e:
        logger.error(f"获取平台列表失败: {str(e)}")
        return web.json_response({
            'success': False,
            'message': str(e)
        })

@routes.post('/api/subscription/settings')
async def update_subscription_settings(request: web.Request) -> web.Response:
    """
    更新订阅设置

    请求体:
        uid: 用户ID
        platform: 平台
        room_id: 房间ID
        settings: 设置对象
    """
    try:
        data = await request.json()
        uid = data.get('uid')
        platform_name = data.get('platform')
        room_id = data.get('room_id')
        settings = data.get('settings')

        if not all([uid, platform_name, room_id, settings]):
            return web.json_response({
                'success': False,
                'message': '参数不完整'
            })

        # 获取存储实例
        storage = container.get('storage')

        # 更新设置
        success = await storage.update_subscription_settings(uid, platform_name, room_id, settings)
        if success:
            return web.json_response({
                'success': True,
                'message': '设置更新成功'
            })
        else:
            return web.json_response({
                'success': False,
                'message': '未找到该订阅'
            })

    except Exception as e:
        logger.error(f"更新订阅设置失败: {str(e)}")
        return web.json_response({
            'success': False,
            'message': str(e)
        })
