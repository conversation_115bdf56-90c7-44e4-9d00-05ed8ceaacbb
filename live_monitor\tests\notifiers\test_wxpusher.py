import asyncio
import unittest
from unittest.mock import patch, AsyncM<PERSON>, MagicMock, ANY
import aiohttp # For raising aiohttp.ClientError
import json # For json.JSONDecodeError

from app.notifiers.wxpusher import AsyncWxPusher

class TestAsyncWxPusherSend(unittest.IsolatedAsyncioTestCase):

    def _get_mock_wxpusher_config(self, app_token="test_token", base_url="https://mock.wxpusher.com/api",
                                  cf_url=None, cf_key=None):
        return {
            'wxpusher': {
                'app_token': app_token,
                'base_url': base_url,
                'cloudflare_worker_url': cf_url,
                'cloudflare_api_key': cf_key
            }
        }

    @patch('app.notifiers.wxpusher.container') 
    @patch('app.notifiers.wxpusher.aiohttp.ClientSession') # Patch where it's used by AsyncWxPusher
    async def test_send_successful_direct(self, MockClientSessionClass, mock_container):
        mock_app_config = self._get_mock_wxpusher_config(app_token="direct_token")
        mock_container.get.return_value = mock_app_config

        # 1. Mocking the session object (the result of ClientSession())
        mock_session_instance = MockClientSessionClass.return_value  # This is the 'session' instance
        mock_session_instance.__aenter__ = AsyncMock(return_value=mock_session_instance)
        mock_session_instance.__aexit__ = AsyncMock(return_value=None)

        # 2. Mocking the response object
        mock_response_object = AsyncMock(spec=aiohttp.ClientResponse) 
        mock_response_object.json = AsyncMock(return_value={'code': 1000, 'msg': 'Success'})
        mock_response_object.text = AsyncMock(return_value='{"code": 1000, "msg": "Success"}')
        mock_response_object.status = 200
        mock_response_object.request_info = MagicMock() 
        mock_response_object.request_info.url = "http://mockurl.com/direct"

        # 3. Mocking the context manager returned by session.post()
        # This object is what session.post() returns, and it's used in 'async with'
        post_context_manager_mock = AsyncMock() 
        post_context_manager_mock.__aenter__ = AsyncMock(return_value=mock_response_object)
        post_context_manager_mock.__aexit__ = AsyncMock(return_value=None)

        # 4. Mocking session.post()
        # session.post is a REGULAR method that returns an async context manager.
        mock_session_instance.post = MagicMock(return_value=post_context_manager_mock)
        
        notifier = AsyncWxPusher()
        await notifier.send("Direct Test", "Direct Body", details={'uids': ['UID_DIRECT']})

        mock_session_instance.post.assert_called_once()
        args, kwargs = mock_session_instance.post.call_args 
        self.assertEqual(args[0], "https://mock.wxpusher.com/api/send/message")
        expected_payload = {
            "appToken": "direct_token",
            "content": "Direct Test\n\nDirect Body",
            "contentType": 1,
            "uids": ['UID_DIRECT'],
            "verifyPay": False
        }
        self.assertEqual(kwargs['json'], expected_payload)

    @patch('app.notifiers.wxpusher.container')
    @patch('app.notifiers.wxpusher.aiohttp.ClientSession')
    async def test_send_successful_via_cloudflare(self, MockClientSessionClass, mock_container):
        mock_app_config = self._get_mock_wxpusher_config(
            app_token="cf_token",
            cf_url="https://my.cloudflare.worker",
            cf_key="cf_api_key"
        )
        mock_container.get.return_value = mock_app_config

        mock_session_instance = MockClientSessionClass.return_value
        mock_session_instance.__aenter__ = AsyncMock(return_value=mock_session_instance)
        mock_session_instance.__aexit__ = AsyncMock(return_value=None)

        mock_response_object = AsyncMock(spec=aiohttp.ClientResponse)
        mock_response_object.json = AsyncMock(return_value={'code': 1000, 'msg': 'Success'})
        mock_response_object.text = AsyncMock(return_value='{"code": 1000, "msg": "Success"}')
        mock_response_object.status = 200
        mock_response_object.request_info = MagicMock()
        mock_response_object.request_info.url = "http://mockurl.com/cf"

        post_context_manager_mock = AsyncMock()
        post_context_manager_mock.__aenter__ = AsyncMock(return_value=mock_response_object)
        post_context_manager_mock.__aexit__ = AsyncMock(return_value=None)
        
        mock_session_instance.post = MagicMock(return_value=post_context_manager_mock)

        notifier = AsyncWxPusher()
        await notifier.send("CF Test", "CF Body", details={'uids': ['UID_CF']})

        mock_session_instance.post.assert_called_once()
        args, kwargs = mock_session_instance.post.call_args 
        self.assertEqual(args[0], "https://my.cloudflare.worker")
        expected_payload = {
            "appToken": "cf_token",
            "content": "CF Test\n\nCF Body",
            "contentType": 1,
            "uids": ['UID_CF'],
            "verifyPay": False,
            "apiKey": "cf_api_key"
        }
        self.assertEqual(kwargs['json'], expected_payload)

    @patch('app.notifiers.wxpusher.container')
    @patch('app.notifiers.wxpusher.aiohttp.ClientSession')
    async def test_send_no_uids_provided(self, MockClientSessionClass, mock_container):
        mock_container.get.return_value = self._get_mock_wxpusher_config()
        
        mock_session_instance = MockClientSessionClass.return_value 
        mock_session_instance.__aenter__ = AsyncMock(return_value=mock_session_instance)
        mock_session_instance.__aexit__ = AsyncMock(return_value=None)
        # session.post should not be called. Make it a MagicMock just in case.
        mock_session_instance.post = MagicMock() 

        notifier = AsyncWxPusher()
        with self.assertLogs('notifier.wxpusher', level='WARNING') as cm:
            await notifier.send("No UIDs Test", "Body", details={}) 
        mock_session_instance.post.assert_not_called()
        self.assertTrue(any("No UIDs provided" in log for log in cm.output))

        mock_session_instance.post.reset_mock() 
        with self.assertLogs('notifier.wxpusher', level='WARNING') as cm:
            await notifier.send("No UIDs Test", "Body", details={'uids': []}) 
        mock_session_instance.post.assert_not_called()
        self.assertTrue(any("No UIDs provided" in log for log in cm.output))
        
        mock_session_instance.post.reset_mock()
        with self.assertLogs('notifier.wxpusher', level='WARNING') as cm:
            await notifier.send("No UIDs Test", "Body") 
        mock_session_instance.post.assert_not_called()
        self.assertTrue(any("No UIDs provided" in log for log in cm.output))

    @patch('app.notifiers.wxpusher.container')
    @patch('app.notifiers.wxpusher.aiohttp.ClientSession')
    async def test_send_api_error_response(self, MockClientSessionClass, mock_container):
        mock_container.get.return_value = self._get_mock_wxpusher_config()
        
        mock_session_instance = MockClientSessionClass.return_value
        mock_session_instance.__aenter__ = AsyncMock(return_value=mock_session_instance)
        mock_session_instance.__aexit__ = AsyncMock(return_value=None)

        mock_response_object = AsyncMock(spec=aiohttp.ClientResponse)
        mock_response_object.json = AsyncMock(return_value={'code': 2001, 'msg': 'appToken错误'})
        mock_response_object.text = AsyncMock(return_value='{"code": 2001, "msg": "appToken错误"}') 
        mock_response_object.status = 200 
        mock_response_object.request_info = MagicMock()
        mock_response_object.request_info.url = "http://mockurl.com/api_error"

        post_context_manager_mock = AsyncMock()
        post_context_manager_mock.__aenter__ = AsyncMock(return_value=mock_response_object)
        post_context_manager_mock.__aexit__ = AsyncMock(return_value=None)
        mock_session_instance.post = MagicMock(return_value=post_context_manager_mock)

        notifier = AsyncWxPusher()
        with self.assertLogs('notifier.wxpusher', level='ERROR') as cm:
            await notifier.send("API Error Test", "Body", details={'uids': ['UID_ERROR']})
        
        mock_session_instance.post.assert_called_once()
        self.assertTrue(any("Failed to send message" in log for log in cm.output), cm.output)
        self.assertTrue(any("API Response Code: 2001" in log for log in cm.output), cm.output)

    @patch('app.notifiers.wxpusher.container')
    @patch('app.notifiers.wxpusher.aiohttp.ClientSession')
    async def test_send_http_request_failure_client_error(self, MockClientSessionClass, mock_container):
        mock_container.get.return_value = self._get_mock_wxpusher_config()
        
        mock_session_instance = MockClientSessionClass.return_value
        mock_session_instance.__aenter__ = AsyncMock(return_value=mock_session_instance)
        mock_session_instance.__aexit__ = AsyncMock(return_value=None)
        
        # session.post() itself raises an error.
        mock_session_instance.post = MagicMock(side_effect=aiohttp.ClientError("Simulated connection error"))

        notifier = AsyncWxPusher()
        with self.assertLogs('notifier.wxpusher', level='ERROR') as cm:
            await notifier.send("HTTP Error Test", "Body", details={'uids': ['UID_HTTP_ERROR']})
        
        mock_session_instance.post.assert_called_once()
        self.assertTrue(any("HTTP client error sending message" in log for log in cm.output), cm.output)
        self.assertTrue(any("Simulated connection error" in log for log in cm.output), cm.output)

    @patch('app.notifiers.wxpusher.container')
    @patch('app.notifiers.wxpusher.aiohttp.ClientSession')
    async def test_send_http_request_failure_generic_exception(self, MockClientSessionClass, mock_container):
        mock_container.get.return_value = self._get_mock_wxpusher_config()

        mock_session_instance = MockClientSessionClass.return_value
        mock_session_instance.__aenter__ = AsyncMock(return_value=mock_session_instance)
        mock_session_instance.__aexit__ = AsyncMock(return_value=None)
        
        mock_session_instance.post = MagicMock(side_effect=Exception("Very generic simulated error"))

        notifier = AsyncWxPusher()
        with self.assertLogs('notifier.wxpusher', level='ERROR') as cm:
            await notifier.send("Generic Error Test", "Body", details={'uids': ['UID_GEN_ERROR']})
        
        mock_session_instance.post.assert_called_once()
        self.assertTrue(any("Generic error sending message" in log for log in cm.output), cm.output)
        self.assertTrue(any("Very generic simulated error" in log for log in cm.output), cm.output)

    @patch('app.notifiers.wxpusher.container')
    @patch('app.notifiers.wxpusher.aiohttp.ClientSession')
    async def test_send_non_json_response(self, MockClientSessionClass, mock_container):
        mock_container.get.return_value = self._get_mock_wxpusher_config()

        mock_session_instance = MockClientSessionClass.return_value
        mock_session_instance.__aenter__ = AsyncMock(return_value=mock_session_instance)
        mock_session_instance.__aexit__ = AsyncMock(return_value=None)

        mock_response_object = AsyncMock(spec=aiohttp.ClientResponse)
        # This text will cause json.loads() in the SUT to fail
        mock_response_object.text = AsyncMock(return_value="This is not HTML or JSON, just plain text")
        mock_response_object.status = 200 
        mock_response_object.request_info = MagicMock()
        mock_response_object.request_info.url = "http://mockurl.com/notjson"
        # If .json() was called on response, it would raise ContentTypeError.
        # But AsyncWxPusher calls .text() then json.loads().
        mock_response_object.json = AsyncMock(side_effect=json.JSONDecodeError("Simulated decode error", "doc", 0))


        post_context_manager_mock = AsyncMock()
        post_context_manager_mock.__aenter__ = AsyncMock(return_value=mock_response_object)
        post_context_manager_mock.__aexit__ = AsyncMock(return_value=None)
        mock_session_instance.post = MagicMock(return_value=post_context_manager_mock)

        notifier = AsyncWxPusher()
        with self.assertLogs('notifier.wxpusher', level='ERROR') as cm:
            await notifier.send("Non-JSON Test", "Body", details={'uids': ['UID_JSON_ERROR']})
        
        mock_session_instance.post.assert_called_once()
        self.assertTrue(any("Failed to decode JSON response" in log for log in cm.output), cm.output)
        self.assertTrue(any("This is not HTML or JSON, just plain text" in log for log in cm.output), cm.output)

if __name__ == '__main__':
    unittest.main()
