"""
工具函数模块 - 异步支持
"""
import asyncio
import functools
from typing import Callable, Any, TypeVar, Coroutine

from .logger import setup_logger

logger = setup_logger('helper')

T = TypeVar('T')

def async_retry(max_retries: int = 3, retry_interval: float = 1.0):
    """
    异步函数重试装饰器

    Args:
        max_retries: 最大重试次数
        retry_interval: 重试间隔（秒）
    """
    def decorator(func: Callable[..., Coroutine[Any, Any, T]]) -> Callable[..., Coroutine[Any, Any, T]]:
        """
        重试装饰器函数

        Args:
            func: 要装饰的异步函数

        Returns:
            Callable: 装饰后的函数
        """
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> T:
            """
            包装原函数的异步函数，添加重试功能

            Args:
                *args: 传递给原函数的位置参数
                **kwargs: 传递给原函数的关键字参数

            Returns:
                T: 原函数的返回值

            Raises:
                Exception: 如果所有重试都失败，抛出最后一个异常
            """

            last_exception = None
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        logger.warning(f"第{attempt+1}次尝试失败，{retry_interval}秒后重试: {str(e)}")
                        await asyncio.sleep(retry_interval)
                    else:
                        logger.error(f"重试{max_retries}次后仍然失败: {str(e)}")

            # 如果所有重试都失败，抛出最后一个异常
            raise last_exception

        return wrapper

    return decorator

from app.constants import PLATFORM_NAMES

def get_platform_name(platform: str) -> str:
    """
    获取平台的中文名称

    Args:
        platform: 平台英文名称

    Returns:
        str: 平台中文名称
    """
    return PLATFORM_NAMES.get(platform.lower(), platform)
