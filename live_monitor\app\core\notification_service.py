import importlib
from typing import List, Dict, Any, Optional

from app.notifications.base import BaseNotifier
from app.core.container import container # To get global config
from app.utils.logger import setup_logger

logger = setup_logger('core.notification_service')

class NotificationService:
    def __init__(self):
        # Attempt to get notification-specific config, default to empty dict if not found
        app_config = container.get('config')
        self.config = app_config.get('notifications', {}) if app_config else {}
        
        self.notifiers_config = self.config.get('notifiers', [])
        self.active_notifiers: List[BaseNotifier] = []
        self._load_notifiers()

    def _load_notifiers(self) -> None:
        if not self.notifiers_config:
            logger.warning("No notifiers configured in 'notifications.notifiers'.")
            return

        for notifier_conf in self.notifiers_config:
            notifier_type = notifier_conf.get('type')
            if not notifier_type:
                logger.error("Notifier configuration missing 'type'. Skipping.")
                continue

            notifier_enabled = notifier_conf.get('enabled', True)
            if not notifier_enabled:
                logger.info(f"Notifier '{notifier_type}' is disabled via configuration.")
                continue

            module_path = notifier_conf.get('module_path')
            class_name = notifier_conf.get('class_name')

            if not module_path:
                # Infer module_path if not explicitly provided
                module_path = f"app.notifiers.{notifier_type}"
                logger.debug(f"Module path for '{notifier_type}' inferred as: {module_path}")
            
            if not class_name:
                # Infer class_name if not explicitly provided
                # Example: 'wxpusher' -> 'AsyncWxPusher'
                class_name_parts = [part.capitalize() for part in notifier_type.split('_')]
                class_name = "".join(class_name_parts)
                if not class_name.startswith("Async"): # Based on current pattern
                    class_name = f"Async{class_name}"
                logger.debug(f"Class name for '{notifier_type}' inferred as: {class_name}")

            try:
                logger.info(f"Loading notifier: {module_path}.{class_name}")
                module = importlib.import_module(module_path)
                NotifierClass = getattr(module, class_name)
                
                # Current assumption: NotifierClass() constructor takes no specific config arguments,
                # as it fetches its own config from the global container (e.g., AsyncWxPusher).
                # If this changes, config (e.g., notifier_conf.get('config', {})) should be passed here.
                notifier_instance = NotifierClass() 
                
                if isinstance(notifier_instance, BaseNotifier):
                    self.active_notifiers.append(notifier_instance)
                    logger.info(f"Notifier '{notifier_type}' ({class_name}) loaded successfully.")
                else:
                    logger.error(f"Notifier class {class_name} from {module_path} does not inherit from BaseNotifier. Skipping.")

            except ImportError as e:
                logger.error(f"Failed to import notifier module {module_path} for type '{notifier_type}': {e}")
            except AttributeError as e:
                logger.error(f"Failed to find class {class_name} in module {module_path} for type '{notifier_type}': {e}")
            except Exception as e:
                logger.error(f"Failed to load or instantiate notifier {notifier_type} ({class_name}): {e}")
        
        if not self.active_notifiers:
            logger.warning("No active notifiers were successfully loaded. Notifications will not be sent.")

    async def init_all_notifiers(self) -> None:
        """Initializes all active notifiers."""
        if not self.active_notifiers:
            logger.info("No active notifiers to initialize.")
            return
            
        logger.info(f"Initializing {len(self.active_notifiers)} active notifiers...")
        for notifier in self.active_notifiers:
            if hasattr(notifier, 'init') and callable(notifier.init):
                try:
                    await notifier.init()
                    logger.info(f"Notifier {type(notifier).__name__} initialized successfully.")
                except Exception as e:
                    logger.error(f"Error initializing notifier {type(notifier).__name__}: {e}")
            else:
                logger.debug(f"Notifier {type(notifier).__name__} does not have an 'init' method.")

    async def cleanup_all_notifiers(self) -> None:
        """Cleans up all active notifiers."""
        if not self.active_notifiers:
            logger.info("No active notifiers to clean up.")
            return

        logger.info(f"Cleaning up {len(self.active_notifiers)} active notifiers...")
        for notifier in self.active_notifiers:
            if hasattr(notifier, 'cleanup') and callable(notifier.cleanup):
                try:
                    await notifier.cleanup()
                    logger.info(f"Notifier {type(notifier).__name__} cleaned up successfully.")
                except Exception as e:
                    logger.error(f"Error cleaning up notifier {type(notifier).__name__}: {e}")
            else:
                logger.debug(f"Notifier {type(notifier).__name__} does not have a 'cleanup' method.")

    async def dispatch_notification(self, title: str, body: str, details: Optional[Dict] = None) -> None:
        if not self.active_notifiers:
            logger.warning("Dispatch called, but no active notifiers loaded. Notification not sent.")
            return

        num_notifiers = len(self.active_notifiers)
        logger.info(f"Dispatching notification to {num_notifiers} notifier(s)...")
        
        for notifier in self.active_notifiers:
            try:
                logger.debug(f"Sending notification via {type(notifier).__name__} - Title: {title}")
                await notifier.send(title, body, details)
                logger.info(f"Notification successfully sent via {type(notifier).__name__}.")
            except Exception as e:
                logger.error(f"Error sending notification via {type(notifier).__name__}: {e}")
        logger.info("Finished dispatching notifications.")

# Example of how this might be registered or used (conceptual)
# async def main():
#     # Assume container and config are set up
#     notification_service = NotificationService()
#     await notification_service.init_all_notifiers()
#
#     # Example dispatch
#     await notification_service.dispatch_notification("Test Event", "A test event has occurred.", {"user_specific_data": "test"})
#
#     await notification_service.cleanup_all_notifiers()
#
# if __name__ == "__main__":
#    # This is placeholder, actual app setup will be different
#    # Setup container, load config etc.
#    # asyncio.run(main())
#    pass
