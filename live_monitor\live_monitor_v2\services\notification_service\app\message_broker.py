import asyncio
import aio_pika
import logging
from typing import Optional, Callable, Awaitable

from app.config import settings # Assuming settings.MESSAGE_BROKER_URL

logger = logging.getLogger(__name__)

class MessageConsumerClient:
    def __init__(self, broker_url: str):
        self.broker_url = broker_url
        self.connection: Optional[aio_pika.RobustConnection] = None
        self.channel: Optional[aio_pika.Channel] = None
        self.queue_name = "live_status_events_queue" # Define a queue name
        self.exchange_name = "live_events" # Must match the exchange used by publisher
        # For MVP, let's assume a simple routing key that catches all events.
        # Or, specify more complex binding keys if needed.
        self.binding_routing_key = "live.event.#" 

    async def connect(self) -> None:
        if self.connection and not self.connection.is_closed:
            logger.info("Message broker connection already established.")
            return
        try:
            self.connection = await aio_pika.connect_robust(self.broker_url)
            self.channel = await self.connection.channel() # type: ignore
            
            # Declare the exchange (idempotent, ensures it exists)
            await self.channel.declare_exchange(
                self.exchange_name, aio_pika.ExchangeType.TOPIC, durable=True
            )
            
            # Declare a durable queue
            queue = await self.channel.declare_queue(self.queue_name, durable=True)
            
            # Bind the queue to the exchange with the routing key
            await queue.bind(self.exchange_name, routing_key=self.binding_routing_key)
            
            logger.info(f"Connected to message broker, declared exchange '{self.exchange_name}', queue '{self.queue_name}', and binding '{self.binding_routing_key}'.")
        except Exception as e:
            logger.error(f"Failed to connect to message broker: {e}")
            self.connection = None
            self.channel = None
            raise

    async def close(self) -> None:
        try:
            if self.channel and not self.channel.is_closed: # type: ignore
                await self.channel.close() # type: ignore
            if self.connection and not self.connection.is_closed:
                await self.connection.close()
            logger.info("Message broker connection closed.")
        except Exception as e:
            logger.error(f"Error closing message broker connection: {e}")
        finally:
            self.channel = None
            self.connection = None
            
    async def consume_events(self, on_message_callback: Callable[[aio_pika.IncomingMessage], Awaitable[None]]) -> None:
        if not self.channel or self.channel.is_closed: # type: ignore
            logger.error("Cannot consume events: channel is not available or closed.")
            # Attempt to reconnect or raise
            try:
                logger.info("Attempting to reconnect to message broker for consumption...")
                await self.connect()
                if not self.channel:
                    logger.error("Reconnect failed, cannot consume events.")
                    return # Or raise an exception
            except Exception as e:
                logger.error(f"Reconnect failed during consume attempt: {e}")
                return # Or raise
        
        try:
            queue = await self.channel.get_queue(self.queue_name) # type: ignore
            logger.info(f"Starting to consume messages from queue '{self.queue_name}'...")
            # `no_ack=False` means manual acknowledgement is required
            await queue.consume(on_message_callback, no_ack=False) 
            logger.info("Consumer started. Waiting for messages.")
            # Keep consumer alive (e.g., by waiting on an event or a long sleep in the main loop)
            # In this structure, the caller of consume_events will manage keeping the process alive.
        except Exception as e:
            logger.error(f"Error consuming events: {e}")
            # Handle specific exceptions like connection issues if needed

# Global instance (or manage via DI if this becomes part of a larger FastAPI app)
message_consumer_client = MessageConsumerClient(broker_url=settings.MESSAGE_BROKER_URL)
