# Example settings for Monitoring Service (monitoring_service/config/settings.yaml)
# These would be loaded by the service's pydantic-settings model.

# Interval for checking subscriptions, in seconds
monitor_interval_seconds: 60

# Storage Service URL (for fetching subscriptions)
# Typically set via environment variable STORAGE_SERVICE_URL in docker-compose
# storage_service_url: "http://storage_service:8000" 

# Message Broker URL (for publishing events)
# Typically set via environment variable MESSAGE_BROKER_URL in docker-compose
# message_broker_url: "amqp://lmv2_user:lmv2_password@message_broker/"

# Directory where platform adapter plugins are located
# This path is relative to the monitoring_service/app directory, or an absolute path.
# For plugins within the service, it might be "./plugins" or "app.plugins" if using Python module paths.
# The plugin_loader.py was designed to scan a directory.
plugin_dir: "app.plugins" # Assuming plugins are in monitoring_service/app/plugins and importable

# Configuration for individual platform adapters
# The keys (bilibili, douyu, huya) should match the 'platform_name' returned by the adapter.
platform_adapters:
  bilibili:
    enabled: true
    # No specific config needed for Bilibili adapter for MVP
    # Example of a platform-specific setting:
    # api_variant: "v2" 
    # specific_settings: # This key is used by the PlatformAdapterConfig model
    #   api_variant: "v2"


  douyu:
    enabled: true
    # Example: if Douyu needed a specific API key (it doesn't for MVP)
    # specific_settings:
    #   api_key: "YOUR_DOUYU_API_KEY_IF_NEEDED"

  huya:
    enabled: true
    # No specific config needed for Huya adapter for MVP

  # Example of a disabled adapter
  # another_platform:
  #   enabled: false
  #   specific_settings: {}
