from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field # Import Field for default_factory
from typing import List, Dict, Any, Optional

class NotifierPluginConfig(BaseSettings):
    """
    Represents the configuration for a single notifier plugin.
    """
    type: str 
    enabled: bool = True
    module: Optional[str] = None 
    class_name: Optional[str] = None 
    config: Dict[str, Any] = Field(default_factory=dict)

class Settings(BaseSettings):
    STORAGE_SERVICE_URL: str = "http://localhost:8000" 
    MESSAGE_BROKER_URL: str = "amqp://lmv2_user:lmv2_password@localhost:5672/" 
    
    NOTIFIER_PLUGIN_DIR: str = "notifiers" 
    
    # WxPusher specific settings
    WXPUSHER_APP_TOKEN: Optional[str] = None 
    WXPUSHER_BASE_URL: Optional[str] = None 
    WXPUSHER_CLOUDFLARE_WORKER_URL: Optional[str] = None
    WXPUSHER_CLOUDFLARE_API_KEY: Optional[str] = None

    # Email Notifier specific settings
    EMAIL_SMTP_HOST: Optional[str] = None
    EMAIL_SMTP_PORT: int = 587 # Default for TLS
    EMAIL_SMTP_USER: Optional[str] = None
    EMAIL_SMTP_PASSWORD: Optional[str] = None
    EMAIL_SENDER_EMAIL: Optional[str] = None
    EMAIL_USE_TLS: bool = True # Default to True, esp. for port 587
    EMAIL_USE_SSL: bool = False # Default to False

    model_config = SettingsConfigDict(
        env_file=".env", 
        env_file_encoding='utf-8', 
        extra='ignore',
        env_prefix='LIVE_MONITOR_' 
    )

settings = Settings()

# Construct NotifierPluginConfig list based on loaded settings for all known notifiers
MVP_NOTIFIERS_CONFIG: List[NotifierPluginConfig] = []

# WxPusher Configuration
if settings.WXPUSHER_APP_TOKEN:
    MVP_NOTIFIERS_CONFIG.append(
        NotifierPluginConfig(
            type="wxpusher",
            enabled=True, 
            # module="wxpusher_notifier", # Can be inferred by plugin_loader
            # class_name="WxPusherNotifier", # Can be inferred
            config={
                "app_token": settings.WXPUSHER_APP_TOKEN,
                "base_url": settings.WXPUSHER_BASE_URL, 
                "cloudflare_worker_url": settings.WXPUSHER_CLOUDFLARE_WORKER_URL,
                "cloudflare_api_key": settings.WXPUSHER_CLOUDFLARE_API_KEY,
            }
        )
    )

# Email Notifier Configuration
# Email notifier is considered enabled if essential SMTP host and sender email are provided.
if settings.EMAIL_SMTP_HOST and settings.EMAIL_SENDER_EMAIL:
    MVP_NOTIFIERS_CONFIG.append(
        NotifierPluginConfig(
            type="email",
            enabled=True,
            # module="email_notifier", # Can be inferred
            # class_name="EmailNotifier", # Can be inferred
            config={
                "smtp_host": settings.EMAIL_SMTP_HOST,
                "smtp_port": settings.EMAIL_SMTP_PORT,
                "smtp_user": settings.EMAIL_SMTP_USER,
                "smtp_password": settings.EMAIL_SMTP_PASSWORD,
                "sender_email": settings.EMAIL_SENDER_EMAIL,
                "use_tls": settings.EMAIL_USE_TLS,
                "use_ssl": settings.EMAIL_USE_SSL,
            }
        )
    )

# Log which notifiers are configured (optional, could also be done in main.py)
# import logging # Add this import if using logger here
# logger = logging.getLogger(__name__)
if not MVP_NOTIFIERS_CONFIG:
    # logger.warning("No notifiers configured based on environment variables (e.g., LIVE_MONITOR_WXPUSHER_APP_TOKEN or LIVE_MONITOR_EMAIL_SMTP_HOST).")
    pass # Main application logic will handle logging of loaded notifiers.
