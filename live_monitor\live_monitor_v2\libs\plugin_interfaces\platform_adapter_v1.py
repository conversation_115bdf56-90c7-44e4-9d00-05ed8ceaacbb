import abc
from typing import Dict, Any, Optional
from datetime import datetime # Added for 'start_time' type hint

class PlatformAdapterV1(abc.ABC):
    def __init__(self, platform_name: str, config: Optional[Dict[str, Any]] = None):
        self.platform_name = platform_name
        self.config = config or {} # Specific config for this platform instance

    @abc.abstractmethod
    async def init(self) -> None:
        """Initialize the adapter, e.g., setup HTTP client with platform-specific headers."""
        pass

    @abc.abstractmethod
    async def get_streamer_status(self, room_id: str) -> Optional[Dict[str, Any]]:
        """
        Fetch streamer status.
        Should return a dictionary with keys like:
        'platform_name': str (e.g., 'bilibili')
        'room_id': str
        'anchor_name': str
        'title': str
        'live_status': bool (True for live, False for offline)
        'stream_url': str (URL to the stream)
        'cover_image_url': Optional[str]
        'viewer_count': Optional[int]
        'start_time': Optional[datetime] # If live
        Or None if room not found or error.
        """
        pass

    @abc.abstractmethod
    def get_platform_display_name(self) -> str:
        """Return a user-friendly display name for the platform."""
        pass
