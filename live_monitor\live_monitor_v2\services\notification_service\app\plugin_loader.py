import importlib
import inspect
import logging
import os
from typing import Dict, Type, Any, Optional, List

from libs.plugin_interfaces.base_notifier_v1 import BaseNotifierV1
from app.config import settings, NotifierPluginConfig # For NOTIFIER_PLUGIN_DIR and structured config

logger = logging.getLogger(__name__)

def load_notifiers(
    plugin_dir_setting: str, # e.g., "notifiers" from settings.NOTIFIER_PLUGIN_DIR
    configured_notifiers: List[NotifierPluginConfig] # List of NotifierPluginConfig from settings
) -> Dict[str, BaseNotifierV1]:
    """
    Loads notifier plugins from the specified directory based on provided configurations.
    
    Args:
        plugin_dir_setting: The directory name where plugin modules are located (e.g., "notifiers").
                            This is used to construct the Python import path.
        configured_notifiers: A list of NotifierPluginConfig objects detailing which notifiers
                              to load and their specific configurations.
    
    Returns:
        A dictionary mapping notifier_type (e.g., "wxpusher") to an instance of its notifier.
    """
    loaded_notifiers: Dict[str, BaseNotifierV1] = {}
    
    if not configured_notifiers:
        logger.warning("No notifiers configured to be loaded.")
        return loaded_notifiers

    # The actual directory path for scanning files (if needed, though not strictly necessary if module paths are correct)
    # Assuming this script (plugin_loader.py) is in 'app/', and 'notifiers/' is also in 'app/' or at root.
    # Dockerfile copies 'notifiers' to '/app/notifiers'. With PYTHONPATH=/app, import path is 'notifiers.module'.
    # So, plugin_dir_setting should be "notifiers".

    for notifier_conf in configured_notifiers:
        if not notifier_conf.enabled:
            logger.info(f"Notifier type '{notifier_conf.type}' is disabled in config. Skipping.")
            continue

        notifier_type = notifier_conf.type
        module_name_str = notifier_conf.module or f"{notifier_type}_notifier" # Infer module name
        class_name_str = notifier_conf.class_name or \
                         f"{''.join(part.capitalize() for part in notifier_type.split('_'))}Notifier" # Infer class name

        full_module_path = f"{plugin_dir_setting}.{module_name_str}" # e.g., "notifiers.wxpusher_notifier"
        
        try:
            module = importlib.import_module(full_module_path)
            NotifierClass = getattr(module, class_name_str, None)

            if NotifierClass is None:
                logger.error(f"Class '{class_name_str}' not found in module '{full_module_path}' for notifier type '{notifier_type}'. Skipping.")
                continue
            
            if not issubclass(NotifierClass, BaseNotifierV1) or NotifierClass is BaseNotifierV1:
                logger.error(f"Class '{class_name_str}' from '{full_module_path}' does not correctly implement BaseNotifierV1. Skipping.")
                continue
            
            try:
                # Pass the specific 'config' dict from NotifierPluginConfig to the notifier instance
                instance_config = notifier_conf.config or {}
                notifier_instance = NotifierClass(config=instance_config)
                
                if notifier_instance.notifier_type in loaded_notifiers: # Should match notifier_conf.type
                    logger.warning(
                        f"Duplicate notifier for type '{notifier_instance.notifier_type}' "
                        f"found via {full_module_path}. Keeping the first one loaded."
                    )
                else:
                    loaded_notifiers[notifier_instance.notifier_type] = notifier_instance
                    logger.info(f"Loaded notifier plugin '{class_name_str}' for type '{notifier_instance.notifier_type}' from {full_module_path}")
            except Exception as e:
                logger.error(f"Failed to instantiate notifier '{class_name_str}' from {full_module_path} with config {notifier_conf.config}: {e}", exc_info=True)
                
        except ImportError as e:
            logger.error(f"Failed to import notifier module {full_module_path} for type '{notifier_type}': {e}", exc_info=True)
        except Exception as e:
            logger.error(f"Unexpected error loading notifier plugin from {full_module_path} for type '{notifier_type}': {e}", exc_info=True)
            
    if not loaded_notifiers:
        logger.warning(f"No notifier plugins were successfully loaded from '{plugin_dir_setting}' based on provided configurations.")
    return loaded_notifiers
