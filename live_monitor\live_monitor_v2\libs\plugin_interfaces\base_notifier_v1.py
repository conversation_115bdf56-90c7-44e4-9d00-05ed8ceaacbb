import abc
from typing import Dict, Any, Optional, List

class BaseNotifierV1(abc.ABC):
    def __init__(self, notifier_type: str, config: Optional[Dict[str, Any]] = None):
        self.notifier_type = notifier_type # e.g., 'wxpusher'
        self.config = config or {}

    @abc.abstractmethod
    async def init(self) -> None:
        """Initialize the notifier, e.g., setup API tokens, HTTP client."""
        pass

    @abc.abstractmethod # Making send abstract as per typical interface design
    async def send(self, title: str, body: str, target_users: List[str], details: Optional[Dict[str, Any]] = None) -> None:
        """
        Sends a notification.
        'target_users' here usually refers to specific UIDs for that notifier (e.g. WxPusher UIDs).
        'details' can carry extra info if needed.
        """
        pass
