import unittest
from unittest.mock import patch, MagicMock, mock_open
import os
import importlib
import inspect # To help mock inspect.getmembers

# Assuming libs and app are importable (e.g., via PYTHONPATH in test environment)
from libs.plugin_interfaces.platform_adapter_v1 import PlatformAdapterV1
from app.config import Settings, PlatformAdapterConfig # From monitoring_service.app.config
from app.plugin_loader import load_platform_adapters # The function to test

# --- Mock Platform Adapter Classes ---
class MockAdapterOne(PlatformAdapterV1):
    # Class attribute to indicate its platform key for config lookup
    platform_key_for_config = "one"

    def __init__(self, config: dict): # Accept dict for specific_settings
        super().__init__(platform_name=self.platform_key_for_config, config=config)
        self.initialized_with_specific_settings = config
        self.init_called = False

    async def init(self) -> None: self.init_called = True
    async def get_streamer_status(self, room_id: str): return None
    def get_platform_display_name(self) -> str: return "Mock One"

class MockAdapterTwo(PlatformAdapterV1):
    platform_key_for_config = "two"
    def __init__(self, config: dict):
        super().__init__(platform_name=self.platform_key_for_config, config=config)
        self.specific_settings_received = config
    async def init(self) -> None: pass
    async def get_streamer_status(self, room_id: str): return None
    def get_platform_display_name(self) -> str: return "Mock Two"

class NotAnAdapter: # Not inheriting from PlatformAdapterV1
    pass


class TestMonitoringPluginLoader(unittest.TestCase):

    def setUp(self):
        # Create a basic settings mock that plugin_loader will use
        self.mock_settings = Settings(
            PLUGIN_DIR="test_plugins_dir", # Actual FS scanning will be mocked
            platform_adapters={} # Default to no adapters configured
        )

    @patch('app.plugin_loader.settings') # Patch where 'settings' is imported in plugin_loader.py
    @patch('os.path.isdir')
    @patch('os.listdir')
    @patch('importlib.import_module')
    def test_load_successful_adapters_with_config(self, mock_import_module, mock_listdir, mock_isdir, mock_settings_obj):
        mock_settings_obj.PLUGIN_DIR = "mock_plugins_package" # Import path base
        mock_settings_obj.platform_adapters = {
            "one": PlatformAdapterConfig(enabled=True, specific_settings={"key1": "value1"}),
            "two": PlatformAdapterConfig(enabled=True, specific_settings={"key2": "value2"}),
        }
        
        mock_isdir.return_value = True # Simulate plugin directory exists
        mock_listdir.return_value = ["adapter_one_file.py", "adapter_two_file.py", "__init__.py"]

        # --- Mocking importlib.import_module and inspect.getmembers ---
        def import_module_side_effect(module_path):
            mock_module = MagicMock()
            if module_path == "mock_plugins_package.adapter_one_file":
                # Simulate inspect.getmembers finding MockAdapterOne
                # getattr(module, class_name_str) will be called by plugin_loader
                # The loader uses inspect.getmembers. We need to mock that.
                # For simplicity, let's assume the loader can get the class if module is correct.
                mock_module.MockAdapterOneInFile = MockAdapterOne 
            elif module_path == "mock_plugins_package.adapter_two_file":
                mock_module.MockAdapterTwoInFile = MockAdapterTwo
            else:
                raise ImportError(f"Cannot import {module_path}")
            return mock_module
        
        mock_import_module.side_effect = import_module_side_effect
        
        # The loader uses inspect.getmembers(module, inspect.isclass)
        # We need to ensure that when it inspects our mocked modules, it finds the classes.
        # Patching inspect.getmembers directly can be complex.
        # A simpler way for this test is to ensure the classes are attributes of the mock_module
        # and the plugin_loader's logic correctly identifies them.
        # The current plugin_loader.py iterates getmembers.
        # Let's refine the side_effect to make inspect.getmembers work as expected.

        def getmembers_side_effect(module, predicate):
            if module.MockAdapterOneInFile is MockAdapterOne: # Check based on what we set
                 return [("MockAdapterOneInFile", MockAdapterOne)]
            if module.MockAdapterTwoInFile is MockAdapterTwo:
                 return [("MockAdapterTwoInFile", MockAdapterTwo)]
            return []

        with patch('inspect.getmembers', side_effect=getmembers_side_effect):
            loaded_adapters = load_platform_adapters()

        self.assertEqual(len(loaded_adapters), 2)
        self.assertIn("one", loaded_adapters)
        self.assertIsInstance(loaded_adapters["one"], MockAdapterOne)
        self.assertEqual(loaded_adapters["one"].initialized_with_specific_settings, {"key1": "value1"})
        
        self.assertIn("two", loaded_adapters)
        self.assertIsInstance(loaded_adapters["two"], MockAdapterTwo)
        self.assertEqual(loaded_adapters["two"].specific_settings_received, {"key2": "value2"})

    @patch('app.plugin_loader.settings')
    @patch('os.path.isdir', return_value=True)
    @patch('os.listdir', return_value=["adapter_one_file.py"])
    @patch('importlib.import_module')
    def test_load_adapter_disabled_in_config(self, mock_import_module, mock_listdir, mock_isdir, mock_settings_obj):
        mock_settings_obj.PLUGIN_DIR = "mock_plugins_package"
        mock_settings_obj.platform_adapters = {
            "one": PlatformAdapterConfig(enabled=False, specific_settings={}) 
        }
        
        mock_module_one = MagicMock()
        mock_module_one.AdapterClass = MockAdapterOne # platform_key_for_config = "one"
        mock_import_module.return_value = mock_module_one
        
        with patch('inspect.getmembers', return_value=[("AdapterClass", MockAdapterOne)]):
            loaded_adapters = load_platform_adapters()

        self.assertEqual(len(loaded_adapters), 0) # Should not load disabled adapter

    @patch('app.plugin_loader.settings')
    @patch('os.path.isdir', return_value=True)
    @patch('os.listdir', return_value=["adapter_one_file.py"])
    @patch('importlib.import_module')
    def test_load_adapter_not_in_config(self, mock_import_module, mock_listdir, mock_isdir, mock_settings_obj):
        mock_settings_obj.PLUGIN_DIR = "mock_plugins_package"
        mock_settings_obj.platform_adapters = {
            # "one" is not configured, so MockAdapterOne should be skipped
            "two": PlatformAdapterConfig(enabled=True, specific_settings={"key": "val"})
        }

        mock_module_one = MagicMock()
        mock_module_one.AdapterClass = MockAdapterOne # platform_key_for_config = "one"
        mock_import_module.return_value = mock_module_one
        
        with patch('inspect.getmembers', return_value=[("AdapterClass", MockAdapterOne)]):
            loaded_adapters = load_platform_adapters()
        
        self.assertEqual(len(loaded_adapters), 0)


    @patch('app.plugin_loader.settings')
    @patch('os.path.isdir', return_value=True)
    @patch('os.listdir', return_value=["invalid_adapter_file.py"])
    @patch('importlib.import_module')
    def test_load_invalid_adapter_class(self, mock_import_module, mock_listdir, mock_isdir, mock_settings_obj):
        mock_settings_obj.PLUGIN_DIR = "mock_plugins_package"
        # Assume config allows loading 'invalid' platform, but class is wrong type
        # The plugin_loader needs a way to map "invalid" to the class.
        # Let's assume NotAnAdapter has a platform_key_for_config = "invalid"
        NotAnAdapter.platform_key_for_config = "invalid_platform_key" 
        mock_settings_obj.platform_adapters = {
             NotAnAdapter.platform_key_for_config : PlatformAdapterConfig(enabled=True)
        }

        mock_module_invalid = MagicMock()
        mock_module_invalid.AdapterClass = NotAnAdapter 
        mock_import_module.return_value = mock_module_invalid

        with patch('inspect.getmembers', return_value=[("AdapterClass", NotAnAdapter)]):
            with self.assertLogs(logger='app.plugin_loader', level='ERROR') as cm:
                loaded_adapters = load_platform_adapters()
        
        self.assertEqual(len(loaded_adapters), 0)
        self.assertTrue(any(f"does not correctly implement PlatformAdapterV1" in log for log in cm.output))


    @patch('app.plugin_loader.settings')
    @patch('os.path.isdir', return_value=False) # Simulate plugin directory not found
    def test_plugin_directory_not_found(self, mock_isdir, mock_settings_obj):
        mock_settings_obj.PLUGIN_DIR = "non_existent_dir"
        mock_settings_obj.platform_adapters = {}

        with self.assertLogs(logger='app.plugin_loader', level='ERROR') as cm:
            loaded_adapters = load_platform_adapters()
        
        self.assertEqual(len(loaded_adapters), 0)
        self.assertTrue(any("Plugin directory" in log and "not found" in log for log in cm.output))

    @patch('app.plugin_loader.settings')
    @patch('os.path.isdir', return_value=True)
    @patch('os.listdir', return_value=["bad_import_file.py"])
    @patch('importlib.import_module', side_effect=ImportError("Simulated import failure"))
    def test_import_module_failure(self, mock_import_module, mock_listdir, mock_isdir, mock_settings_obj):
        mock_settings_obj.PLUGIN_DIR = "mock_plugins_package"
        mock_settings_obj.platform_adapters = {} # No specific config needed as import fails first

        with self.assertLogs(logger='app.plugin_loader', level='ERROR') as cm:
            loaded_adapters = load_platform_adapters()

        self.assertEqual(len(loaded_adapters), 0)
        self.assertTrue(any("Failed to import plugin module" in log for log in cm.output))

if __name__ == "__main__":
    unittest.main()
