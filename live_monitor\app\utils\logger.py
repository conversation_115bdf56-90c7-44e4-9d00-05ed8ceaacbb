"""
日志模块 - 异步支持
"""
import os
import logging
from logging.handlers import RotatingFileHandler

def setup_logger(name):
    """
    设置日志记录器

    Args:
        name: 日志记录器名称

    Returns:
        logging.Logger: 配置好的日志记录器
    """
    # 确保日志目录存在
    log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs')
    os.makedirs(log_dir, exist_ok=True)

    # 创建日志记录器
    logger = logging.getLogger(name)
    # 设置日志级别为DEBUG，以便在日志文件中记录详细信息
    logger.setLevel(logging.DEBUG)
    # 禁止将日志传递给父记录器，避免日志被根记录器处理
    logger.propagate = False

    # 避免重复添加处理器
    if not logger.handlers:
        # 控制台处理器 - 只显示WARNING及以上级别的日志
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.WARNING)  # 显示警告和错误信息
        # 使用简洁的格式，去除时间和模块名
        console_formatter = logging.Formatter(
            '【%(levelname)s】%(message)s'
        )
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)

        # 文件处理器
        file_handler = RotatingFileHandler(
            os.path.join(log_dir, f'{name}.log'),
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        # 文件处理器记录DEBUG及以上级别的日志，保留详细信息
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)

    return logger
